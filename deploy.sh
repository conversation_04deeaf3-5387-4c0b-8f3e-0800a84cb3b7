#!/bin/bash
set -euxo pipefail

DEPLOY_DIR="/root/transportapp-api"
APP_NAME="transportapp"

echo ">>> Starting deployment process..."

# Ensure we're in the correct directory
cd $DEPLOY_DIR

# Stop existing containers if running
echo ">>> Stopping existing API containers..."
docker-compose down api || true

# Remove old dist directory if it exists
echo ">>> Cleaning up old files..."
rm -rf dist

# Ensure all necessary files are present
for file in "docker-compose.yml" "nginx.conf"; do
    if [ ! -f "$file" ]; then
        echo "ERROR: Required file $file is missing!"
        exit 1
    fi
done

# Start the application
echo ">>> Starting application..."
docker-compose up -d

# Wait for container to be ready
echo ">>> Waiting for application to start..."
sleep 10

# Check if container is running
if docker ps | grep -q $APP_NAME; then
    echo ">>> Deployment successful! Application is running."
else
    echo "ERROR: Container failed to start!"
    docker-compose logs
    exit 1
fi

echo ">>> Deployment completed successfully!"
