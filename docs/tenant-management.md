# Tenant Management

## Overview

The Transport Application API implements a robust multi-tenant architecture that allows the system to serve multiple isolated client organizations (tenants) from a single deployment. This document explains how the tenant management system works, its key components, and how it integrates with the rest of the application.

## Key Concepts

### Tenant

A tenant represents a client organization using the system. Each tenant has:

- A unique identifier (`id`)
- A company name and unique company ID
- Contact information
- Configuration settings
- Preferences and metadata

Tenants are isolated from each other, ensuring that data and operations for one tenant do not affect others.

## Architecture

### Tenant Data Model

The tenant entity (`TenantEntity`) includes:

```typescript
@Entity({
  name: 'tenants',
})
@Index(['companyUniqueId'], { unique: true })
@Index(['contactEmail'], { unique: true })
export class TenantEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ name: 'company_unique_id', length: 100 })
  companyUniqueId: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ default: true })
  status: boolean;

  @Column({ type: 'jsonb', default: '{}' })
  settings: Record<string, any>;

  // Additional fields for contact info, preferences, etc.
}
```

### Tenant Context

The application uses a tenant context system to track the current tenant for each request:

1. **TenantContextInterceptor**: Extracts tenant information from the JWT token and sets it in the request context
2. **TenantAuthGuard**: Ensures that requests have valid tenant access
3. **TenantValidationService**: Validates tenant existence and status

## Tenant Management Flow

### Tenant Registration

1. User submits registration with company details
2. System validates company ID uniqueness
3. New tenant record is created
4. Admin user is created and associated with the tenant
5. JWT token is generated with tenant context

```typescript
async registerWithTenant(
  registrationDto: TenantRegistrationDto,
): Promise<{ user: UserEntity; tenant: TenantEntity; accessToken: string }> {
  // Validation logic
  // Create tenant and admin user in a transaction
  // Generate JWT token with tenant context
}
```

### Tenant Access Control

Every authenticated request includes tenant context:

```typescript
export interface RequestWithUser extends Request {
  user?: any;
  tenantContext?: {
    tenantId: string | null;
    hasTenantAccess: boolean;
    userType?: string;
  };
}
```

The `TenantValidationService` ensures proper tenant access:

```typescript
async validateTenantAccess(request: RequestWithUser): Promise<Tenant> {
  // Check if user has tenant access in context
  // Check if tenantId exists in context
  // Verify tenant exists and is active in database
  return tenant;
}
```

## Admin Management of Tenants

The admin module provides endpoints for tenant management:

- `GET /admin/tenants`: List all tenants with pagination
- Future endpoints may include tenant creation, updates, and deactivation

## Multi-Tenant Data Isolation

All entity models that store tenant-specific data include a `tenantId` field:

```typescript
@Column('uuid', { name: 'tenant_id' })
@Index()
tenantId: string;
```

Repository queries filter by tenant ID to ensure data isolation.

## Best Practices

1. **Always validate tenant access**: Use `TenantValidationService` before performing tenant-specific operations
2. **Include tenant ID in all tenant-specific entities**: Ensures proper data isolation
3. **Use tenant context in security decisions**: Check tenant context before allowing access to resources

## Integration with Other Modules

The tenant system integrates with:

1. **Authentication**: JWT tokens include encrypted tenant context
2. **Authorization**: Access control decisions consider tenant context
3. **Data Access**: Repositories filter queries by tenant ID
4. **API Endpoints**: Controllers use tenant context to scope operations

## Extending the Tenant System

To add tenant-specific functionality:

1. Ensure new entities include a `tenantId` field
2. Use `TenantValidationService` in controllers
3. Filter repository queries by tenant ID
4. Consider tenant-specific configuration needs