# Transport Application Pricing System Guide

## Table of Contents

1. [Overview](#overview)
2. [Core Components](#core-components)
3. [Price Sets](#price-sets)
4. [Price Modifiers](#price-modifiers)
5. [Zone-Based Pricing](#zone-based-pricing)
6. [Pricing Calculation Flow](#pricing-calculation-flow)
7. [Examples](#examples)
8. [Implementation Details](#implementation-details)
9. [API Reference](#api-reference)

## Overview

The Transport Application pricing system is a flexible, modular framework designed to handle complex pricing scenarios for transportation services. It allows for:

- Time-based availability of pricing options
- Location-based (zone) pricing
- Customizable price modifiers
- Customer-specific pricing
- Comprehensive calculation strategies

This guide explains how the various components work together to determine the final price for an order.

## Core Components

The pricing system consists of four main components:

```ascii
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│   Price Sets    │────▶│ Price Modifiers │────▶│  Calculation    │
│                 │     │                 │     │   Strategies    │
└────────┬────────┘     └─────────────────┘     └─────────────────┘
         │
         ▼
┌─────────────────┐
│                 │
│   Zone Tables   │
│                 │
└─────────────────┘
```

1. **Price Sets**: Define pricing configurations with schedules and availability
2. **Price Modifiers**: Apply adjustments to the base price
3. **Zone Tables**: Determine base prices based on origin and destination
4. **Calculation Strategies**: Implement different calculation methods for modifiers

## Price Sets

A price set is a complete pricing configuration that can be applied to orders.

### Key Properties

- **Name & Internal Name**: Identifiers for the price set
- **Payment Option**: None, Partial, or Full
- **Description & Notes**: Additional information
- **Availability Settings**: When the price set is available
- **Active Status**: Whether the price set is currently active

### Availability Types

Price sets can have three availability types:

1. **Never**: Price set is never available
2. **Always**: Price set is always available
3. **Weekly**: Price set is available on specific days and times

### Schedule Configuration

For weekly availability, schedules define when a price set is available:

```ascii
┌─────────────────────────────────────────────┐
│ Schedule                                    │
├─────────────┬─────────────┬─────────────────┤
│    Days     │  Start Time │    End Time     │
├─────────────┼─────────────┼─────────────────┤
│ Monday,     │   08:00     │     17:00       │
│ Wednesday   │             │                 │
└─────────────┴─────────────┴─────────────────┘
```

### Offset Types

For delivery time calculation, two offset types are supported:

1. **To**: Sets a specific time for delivery (using hours and minutes)
2. **By**: Calculates delivery time relative to pickup time (using time, daysOut, and includeWeekends)

## Price Modifiers

Price modifiers adjust the base price of an order based on various factors.

### Modifier Types

There are ten calculation types available:

1. **FlatAmount**: Adds a fixed amount to the price
2. **FlatPercentage**: Adds a percentage of a specified field value
3. **FlatOverageAmount**: Adds a fixed amount if a field value exceeds a threshold
4. **FlatOveragePercentage**: Adds a percentage if a field value exceeds a threshold
5. **IncrementalOverageAmount**: Adds an amount for each increment over a threshold
6. **IncrementalOveragePercentage**: Adds a percentage for each increment over a threshold
7. **TieredFixedOverageAmount**: Applies a fixed amount based on which tier a value falls into
8. **TieredFixedOveragePercentage**: Applies a percentage based on which tier a value falls into
9. **TieredIncrementalOverageAmount**: Applies an amount per increment based on which tier a value falls into
10. **TieredIncrementalOveragePercentage**: Applies a percentage per increment based on which tier a value falls into

### Calculation Fields

Modifiers can be applied to various fields:

- **BasePrice**: The starting price
- **DeclaredPrice**: Value declared for insurance
- **Weight**: Weight in kilograms
- **Distance**: Distance in kilometers
- **Quantity**: Number of items
- **Height/Width/Length**: Dimensions in centimeters
- **CubicDimensions**: Volume in cubic centimeters
- **CollectionWaitTime/DeliveryWaitTime**: Wait times in minutes
- **CustomAmount**: Custom value

### Modifier Groups

Modifiers can be grouped with different behaviors:

1. **UseSum**: Adds up all the amounts from child modifiers
2. **UseHighest**: Only applies the child modifier with the highest calculated amount
3. **UseLowest**: Only applies the child modifier with the lowest calculated amount

### Configuration Types

When assigned to a price set, modifiers can have different configurations:

1. **None**: Modifier is not applied by default
2. **Selected**: Modifier is selected by default but can be deselected
3. **Required**: Modifier is always applied and cannot be deselected

## Zone-Based Pricing

Zone-based pricing determines the base price based on origin and destination zones.

### Zones

A zone is a geographical area defined by a collection of postal codes:

```ascii
┌─────────────────────────────────────────────┐
│ Zone                                        │
├─────────────┬─────────────────────────────┬─┤
│    Name     │       Postal Codes          │ │
├─────────────┼─────────────────────────────┼─┤
│ Downtown    │ 10001, 10002, 10003         │ │
│ Suburbs     │ 20001, 20002, 20003         │ │
└─────────────┴─────────────────────────────┴─┘
```

### Zone Tables

A zone table defines prices between different origin-destination zone pairs:

```ascii
┌─────────────────────────────────────────────────────────┐
│ Zone Table                                              │
├─────────────┬─────────────────┬─────────────────┬───────┤
│     ID      │   Origin Zone   │ Destination Zone│ Value │
├─────────────┼─────────────────┼─────────────────┼───────┤
│     1       │    Downtown     │    Downtown     │  10.0 │
│     2       │    Downtown     │    Suburbs      │  15.0 │
│     3       │    Suburbs      │    Downtown     │  15.0 │
│     4       │    Suburbs      │    Suburbs      │  20.0 │
└─────────────┴─────────────────┴─────────────────┴───────┘
```

### Assignment to Price Sets

Each price set can have one zone table assigned to it, which determines the base price for orders.

## Pricing Calculation Flow

The pricing calculation follows this flow:

```ascii
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Determine      │     │  Look up        │     │  Apply          │
│  Origin &       │────▶│  Base Price     │────▶│  Price          │
│  Destination    │     │  from Zone Table│     │  Modifiers      │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │  Calculate      │
                                               │  Final Price    │
                                               └─────────────────┘
```

1. **Determine Zones**: Based on postal codes, find the origin and destination zones
2. **Look up Base Price**: Find the matching value in the zone table
3. **Apply Modifiers**: Process each modifier using the appropriate calculation strategy
4. **Calculate Final Price**: Sum the base price and all modifier amounts

### Modifier Processing

For each modifier:

1. Check if the modifier is enabled
2. If it's a group modifier, process each child modifier according to the group behavior
3. If it's an individual modifier, use the appropriate calculation strategy
4. Add the result to the total price

## Examples

### Example 1: Simple Flat Amount Modifier

```javascript
// Base price from zone table: $20
// Modifier: Flat amount of $5

const basePrice = 20;
const modifierAmount = 5;
const totalPrice = basePrice + modifierAmount; // $25
```

### Example 2: Weight-Based Tiered Pricing

```javascript
// Base price from zone table: $15
// Modifier: Tiered pricing based on weight
// - 0-5kg: $0
// - 5-10kg: $5
// - >10kg: $10
// Order weight: 12kg

const basePrice = 15;
const weight = 12;
const modifierAmount = 10; // >10kg tier
const totalPrice = basePrice + modifierAmount; // $25
```

### Example 3: Group Modifier with UseHighest Behavior

```javascript
// Base price from zone table: $20
// Group modifier with UseHighest behavior:
// - Child 1: Distance-based, $5
// - Child 2: Weight-based, $8
// - Child 3: Quantity-based, $3

const basePrice = 20;
const childAmounts = [5, 8, 3];
const groupAmount = Math.max(...childAmounts); // $8 (highest)
const totalPrice = basePrice + groupAmount; // $28
```

## Implementation Details

### Strategy Pattern for Calculations

The system uses the Strategy pattern to implement different calculation types:

1. **BaseCalculationStrategy**
   - Abstract base class implementing ICalculationStrategy
   - Provides common functionality like getFieldValue()
   - Requires concrete implementations to define calculate() and setParams()

2. **Concrete Strategy Implementations**
   - One class per calculation type (FlatAmountStrategy, FlatPercentageStrategy, etc.)
   - Each implements the specific calculation logic
   - Decorated with @PricingStrategy to register with the strategy registry

3. **StrategyRegistryService**
   - Discovers and registers all strategy implementations at runtime
   - Provides getStrategy() to retrieve the appropriate strategy by type

### Price Calculation Service

The PriceCalculatorService is the central component for price calculations:

1. **calculatePrice() Method**
   - Takes an order and an array of modifiers
   - Processes each modifier and accumulates the results
   - Returns a calculation result with base price, modifiers, and total price

2. **processModifier() Method**
   - Handles both individual and group modifiers
   - For individual modifiers, uses the appropriate strategy
   - For group modifiers, processes each child and applies the group behavior

3. **Error Handling**
   - Handles invalid modifiers, disabled modifiers, and calculation errors
   - Includes errors in the calculation result for debugging

### Schedule Processing

1. **Availability Type Handling**
   - For Never, clears all schedule and offset data
   - For Always, clears only schedule data
   - For Weekly, requires valid schedule entries

2. **Offset Type Handling**
   - For To, uses hours and minutes fields
   - For By, uses time, daysOut, and includeWeekends fields

3. **Delivery Date Calculation**
   - Calculates expected delivery date based on pickup date and offset settings
   - Handles different offset types appropriately

### Zone Table Management

1. **Zone Validation**
   - Validates that all zones referenced in a zone table exist
   - Ensures zone tables have at least one value

2. **Price Set Integration**
   - Each price set can have one zone table for base pricing
   - When a new zone table is assigned, it replaces any existing one

3. **Base Price Lookup**
   - Finds the matching origin-destination pair in the zone table
   - Uses the value as the base price for calculations

## API Reference

### Price Sets Endpoints

- `POST /priceSets`: Create a new price set
- `GET /priceSets`: Get all price sets with pagination
- `GET /priceSets/:priceSetId`: Get price set details
- `PUT /priceSets/:priceSetId`: Update a price set
- `DELETE /priceSets/:priceSetId`: Delete a price set
- `POST /priceSets/:priceSetId/schedule`: Create/edit schedule
- `GET /priceSets/:priceSetId/schedule`: Get schedule details
- `PUT /priceSets/:priceSetId/modifiers`: Assign modifiers
- `GET /priceSets/:priceSetId/modifiers`: Get assigned modifiers
- `PUT /priceSets/:priceSetId/zone`: Assign zone table
- `GET /priceSets/:priceSetId/zone`: Get assigned zone table

### Price Modifiers Endpoints

- `POST /priceModifiers`: Create a new price modifier
- `GET /priceModifiers`: Get all price modifiers
- `GET /priceModifiers/:id`: Get price modifier details
- `PUT /priceModifiers/:id`: Update a price modifier
- `DELETE /priceModifiers/:id`: Delete a price modifier
- `POST /priceModifiers/groups`: Create a modifier group
- `GET /priceModifiers/groups`: Get all modifier groups
- `GET /priceModifiers/groups/:id`: Get group details
- `PUT /priceModifiers/groups/:id`: Update a group
- `DELETE /priceModifiers/groups/:id`: Delete a group

### Zones Endpoints

- `POST /zones`: Create a new zone
- `GET /zones`: Get all zones
- `GET /zones/:zoneId`: Get zone details
- `PUT /zones/:zoneId`: Update a zone
- `DELETE /zones/:zoneId`: Delete a zone
- `GET /zones/postalCode/:postalCode`: Find zone by postal code

### Zone Tables Endpoints

- `POST /zoneTable`: Create a new zone table
- `GET /zoneTable`: Get all zone tables
- `GET /zoneTable/:zoneTableId`: Get zone table details
- `PUT /zoneTable/:zoneTableId`: Update a zone table
- `DELETE /zoneTable/:zoneTableId`: Delete a zone table