# Analytics Module Documentation

## Overview

The Analytics Module provides a provider-agnostic system for tracking events and user interactions throughout the Transport Application API. It's designed with a flexible architecture that currently supports PostHog but can be easily extended to support other analytics providers without changing the application's business logic.

## Architecture

The Analytics Module is built on a provider-agnostic architecture with the following key components:

1. **Core Interface**: Defines the contract that all analytics providers must implement
2. **Provider Factory**: Creates the appropriate analytics service based on configuration
3. **Interceptor**: Automatically tracks HTTP events based on request patterns
4. **Configuration System**: Supports multiple providers through a unified configuration interface

### Key Components

#### Analytics Interface

The `IAnalyticsService` interface defines the contract for all analytics providers:

```typescript
export interface IAnalyticsService {
  trackEvent(
    distinctId: string,
    event: string,
    properties?: Record<string, any>,
  ): Promise<void>;
  
  identifyUser(
    distinctId: string,
    properties?: Record<string, any>,
  ): Promise<void>;
  
  isEnabled(): boolean;
  
  getProviderName(): string;
}
```

#### Analytics Factory

The `AnalyticsFactoryService` creates the appropriate analytics service based on configuration:

```typescript
@Injectable()
export class AnalyticsFactoryService {
  createAnalyticsService(): IAnalyticsService {
    const provider = this.configService.get('analytics.provider', { infer: true });
    const enabled = this.configService.get('analytics.enabled', { infer: true });

    if (!enabled) {
      return new NullAnalyticsService();
    }

    switch (provider) {
      case AnalyticsProvider.POSTHOG:
        return new PosthogService(this.configService);
      // Other providers can be added here
      default:
        return new NullAnalyticsService();
    }
  }
}
```

#### Analytics Interceptor

The `AnalyticsInterceptor` automatically tracks HTTP events:

```typescript
@Injectable()
export class AnalyticsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<RequestWithUser>();
    const { method, path, user } = request;
    const distinctId = user?.id || 'anonymous';
    
    return next.handle().pipe(
      tap({
        next: () => {
          // Track successful requests
          const eventName = this.getEventNameFromPath(method, path);
          if (eventName) {
            void this.analyticsService.trackEvent(distinctId, eventName, {
              method, path, status: 'success'
            });
          }
        },
        error: (error) => {
          // Track failed requests
          const eventName = this.getEventNameFromPath(method, path, true);
          if (eventName) {
            void this.analyticsService.trackEvent(distinctId, eventName, {
              method, path, status: 'error', errorMessage: error.message
            });
          }
        },
      }),
    );
  }
}
```

## Supported Providers

### PostHog

The PostHog implementation provides:

- Event tracking with custom properties
- User identification
- Automatic error handling
- Debug logging

```typescript
@Injectable()
export class PosthogService implements IAnalyticsService {
  private client: PostHog | null = null;
  
  constructor(private configService: ConfigService<AllConfigType>) {
    const posthogConfig = this.configService.get('analytics.posthog', { infer: true });
    // Initialize PostHog client
  }
  
  async trackEvent(distinctId: string, event: string, properties?: Record<string, any>) {
    if (!this.enabled || !this.client) return;
    
    try {
      await this.client.capture({
        distinctId,
        event,
        properties,
      });
    } catch (error) {
      this.logger.error(`Failed to track event: ${event}`, error);
    }
  }
}
```

### Null Provider

A null implementation is provided for when analytics is disabled:

```typescript
@Injectable()
export class NullAnalyticsService implements IAnalyticsService {
  async trackEvent(): Promise<void> {
    // Do nothing
    return Promise.resolve();
  }
  
  // Other methods similarly do nothing
}
```

## Tracked Events

The Analytics Module automatically tracks the following events:

| Category | Event Name | Description | Trigger |
|----------|------------|-------------|---------|
| Auth | user_signed_in | When a user logs into the system | Backend |
| Auth | user_signed_out | When a user logs out of the system | Backend |
| Address | address_created | When an address is successfully saved | Backend |
| Address | address_creation_failed | Validation or API failure on create | Backend |
| Orders | order_created | When an order is successfully saved | Backend |
| Orders | order_creation_failed | Validation or API failure on create | Backend |
| Orders | order_updated | When an order is successfully updated | Backend |
| Orders | order_update_failed | Validation or API failure on update | Backend |
| Orders | order_fetch | When an order is successfully fetched | Backend |
| Orders | order_fetch_failed | Validation or API failure on fetching all orders details | Backend |
| Prices | prices_fetched | When a price is successfully fetched | Backend |
| Prices | prices_fetch_failed | Validation or API failure on fetch | Backend |

## Configuration

The Analytics Module is configured through environment variables:

```
# Main analytics configuration
ANALYTICS_PROVIDER=posthog
ANALYTICS_ENABLED=true

# PostHog Configuration
POSTHOG_API_KEY=your_posthog_api_key_here
POSTHOG_HOST=https://app.posthog.com
POSTHOG_ENABLED=true

# Other providers can be configured similarly
```

## Integration with Other Modules

The Analytics Module integrates with other modules through dependency injection:

1. **Authentication Module**: User IDs from authenticated requests are used as distinct IDs
2. **Exception Handling**: Failed requests are tracked with error details
3. **Business Logic**: Services can inject the analytics service to track custom events

## Usage Examples

### Tracking Custom Events

```typescript
@Injectable()
export class OrderService {
  constructor(
    @Inject(ANALYTICS_SERVICE)
    private readonly analyticsService: IAnalyticsService,
  ) {}

  async createOrder(orderData: CreateOrderDto, userId: string) {
    try {
      const order = await this.orderRepository.create(orderData);
      
      // Track custom event with order details
      await this.analyticsService.trackEvent(
        userId,
        'order_payment_initiated',
        {
          orderId: order.id,
          amount: order.totalAmount,
          paymentMethod: order.paymentMethod,
        }
      );
      
      return order;
    } catch (error) {
      // Error handling
    }
  }
}
```

### Identifying Users

```typescript
@Injectable()
export class UserService {
  constructor(
    @Inject(ANALYTICS_SERVICE)
    private readonly analyticsService: IAnalyticsService,
  ) {}

  async updateUserProfile(userId: string, profileData: UpdateProfileDto) {
    const user = await this.userRepository.update(userId, profileData);
    
    // Identify user with updated profile data
    await this.analyticsService.identifyUser(
      userId,
      {
        email: user.email,
        name: user.contactName,
        company: user.companyName,
        role: user.userType,
        updatedAt: new Date().toISOString(),
      }
    );
    
    return user;
  }
}
```

## Extending the Analytics Module

### Adding a New Provider

To add a new analytics provider:

1. Create a new service that implements the `IAnalyticsService` interface
2. Update the `AnalyticsProvider` enum
3. Add the provider configuration to the config types
4. Update the factory service to create your new provider

Example for adding Segment support:

```typescript
// 1. Create the service
@Injectable()
export class SegmentService implements IAnalyticsService {
  // Implementation details
}

// 2. Update the enum
export enum AnalyticsProvider {
  // Existing providers
  SEGMENT = 'segment',
}

// 3. Update the factory
switch (provider) {
  // Existing cases
  case AnalyticsProvider.SEGMENT:
    return new SegmentService(this.configService);
}
```

### Adding New Events

To track new events automatically:

1. Update the `getEventNameFromPath` method in `AnalyticsInterceptor`
2. Add the new event mapping based on HTTP method and path

```typescript
private getEventNameFromPath(method: string, path: string, isError = false): string | null {
  // Existing mappings
  
  // New mapping
  if (path.includes('/notifications') && method === 'POST') {
    return isError ? 'notification_send_failed' : 'notification_sent';
  }
  
  return null;
}
```

## Best Practices

1. **Use the analytics service for business events**: Track meaningful business events, not just technical operations
2. **Include relevant properties**: Add context to events with properties that help analyze user behavior
3. **Handle errors gracefully**: The analytics service should never throw exceptions that affect the main application flow
4. **Use consistent event naming**: Follow a consistent naming convention for events (e.g., `object_action`)
5. **Respect user privacy**: Be mindful of what data you track and ensure compliance with privacy regulations

## Troubleshooting

Common issues and solutions:

1. **Events not appearing in PostHog**: Check that `POSTHOG_API_KEY` is correct and `POSTHOG_ENABLED` is set to `true`
2. **Errors in event tracking**: Look for error logs from the `PosthogService` which indicate connection or API issues
3. **Performance concerns**: If analytics is affecting performance, consider using a queue system for event tracking