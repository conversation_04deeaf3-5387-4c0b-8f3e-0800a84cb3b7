# Core Modules Documentation

## Overview

The Transport Application API is built on a foundation of core modules that provide essential infrastructure and cross-cutting concerns. These modules handle authentication, observability, error handling, and other fundamental aspects of the application. This document provides a comprehensive overview of these core modules, their responsibilities, and how they work together.

## Authentication Module

The authentication module (`src/core/auth`) provides user authentication, authorization, and session management.

### Key Components

#### Authentication Service

The `AuthService` handles:

- User login and registration
- JWT token generation and validation
- Tenant registration
- Contact authentication

```typescript
@Injectable()
export class AuthService {
  async login(loginDto: LoginDto) {
    // Validate credentials
    // Generate JWT token
    // Update login statistics
  }

  async registerWithTenant(registrationDto: TenantRegistrationDto) {
    // Create tenant and user
    // Generate JWT token
  }
}
```

#### JWT Strategy

The JWT strategy validates tokens and extracts user information:

```typescript
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  validate(payload: JwtPayload) {
    // Extract and validate user information from JWT payload
  }
}
```

#### Guards

- `JwtAuthGuard`: Ensures requests are authenticated
- `TenantAuthGuard`: Ensures requests have valid tenant access
- `RolesGuard`: Enforces role-based access control

#### Context Management

The `TenantContextInterceptor` extracts tenant information from JWT tokens and sets it in the request context:

```typescript
@Injectable()
export class TenantContextInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler) {
    // Extract tenant context from JWT
    // Set tenant context in request
  }
}
```

## Infrastructure Module

The infrastructure module (`src/core/infrastructure`) provides foundational services for observability, error handling, and request processing.

### Tracing

The tracing module implements distributed tracing using OpenTelemetry:

```typescript
@Injectable()
export class TracerService implements OnModuleInit, OnModuleDestroy {
  async trace<T>(
    name: string,
    operation: () => Promise<T>,
    attributes: Record<string, any> = {},
  ): Promise<T> {
    // Create and manage trace spans
    // Track operation execution
    // Record errors
  }
}
```

Key features:
- Integration with OpenTelemetry Collector
- Automatic instrumentation of HTTP, Express, NestJS, and database operations
- Custom trace spans for business operations
- Correlation ID propagation

### Correlation

The correlation module tracks request context across service boundaries:

```typescript
@Injectable()
export class CorrelationService {
  getCurrentContext(): CorrelationContext {
    // Get correlation ID, tenant ID, user ID from current context
  }
}
```

The `CorrelationInterceptor` ensures correlation IDs are set for each request.

### Logging

The logging module provides structured logging with context:

```typescript
@Injectable()
export class LoggerService implements ILoggerService {
  log(message: string, context?: string, metadata?: LogMetadata) {
    // Log with correlation context
    // Format structured logs
  }
}
```

Features:
- Correlation ID inclusion in logs
- Structured JSON logging
- Log level configuration
- Context-aware logging

### Error Tracking

The error tracking module captures and reports errors:

```typescript
@Injectable()
export class ErrorTrackingService implements IErrorTrackingService {
  captureException(error: Error, metadata?: Record<string, any>) {
    // Record error details
    // Add correlation context
    // Send to error tracking service
  }
}
```

### Exception Handling

The exceptions module provides standardized error handling:

```typescript
@Injectable()
export class ExceptionsService implements IExceptionsService {
  handleError(error: Error, host: ArgumentsHost) {
    // Convert errors to appropriate HTTP responses
    // Log errors
    // Track errors
  }
}
```

The `AllExceptionsFilter` catches and processes all unhandled exceptions.

### Monitoring

The monitoring module tracks application metrics:

```typescript
@Injectable()
export class MonitoringService implements IMonitoringService {
  recordOperation(operation: Operation) {
    // Record operation metrics
    // Track duration, success/failure
  }
}
```

## Payment Module

The payment module (`src/core/payment`) provides a flexible payment processing system:

- Support for multiple payment providers (currently Stripe)
- Association with any entity type through `entityId` and `entityType`
- Comprehensive payment lifecycle management
- Webhook handling for asynchronous events

See [Payment Module Documentation](payment-module.md) for details.

## Integration Between Core Modules

The core modules work together to provide a cohesive foundation:

1. **Request Flow**:
   - Request enters the application
   - `CorrelationInterceptor` sets correlation ID
   - `TenantContextInterceptor` extracts tenant context
   - `TraceInterceptor` starts a trace span
   - Request is processed
   - Any errors are caught by `AllExceptionsFilter`
   - Response is returned

2. **Observability Chain**:
   - `TracerService` creates trace spans
   - `LoggerService` includes correlation IDs in logs
   - `ErrorTrackingService` captures exceptions with context
   - `MonitoringService` records operation metrics

## Configuration

Core modules are configured through environment variables and the NestJS configuration system:

```typescript
@Global()
@Module({
  imports: [
    ConfigModule.forRoot(),
    LoggerModule,
    TracingModule,
    // Other modules
  ],
})
export class InfrastructureModule {}
```

## Best Practices

1. **Use dependency injection**: Core services are provided through NestJS's DI system
2. **Leverage decorators**: Use `@Trace()` for automatic tracing, `@HasRole()` for authorization
3. **Include correlation context**: Always include correlation IDs in logs and traces
4. **Proper error handling**: Use domain-specific exceptions for better error reporting
5. **Structured logging**: Use metadata objects for structured logging

## Extending Core Modules

To extend core functionality:

1. **Custom trace spans**: Use `TracerService.customTrace()` for business operations
2. **Domain-specific exceptions**: Create new exception classes in `src/utils/errors/exceptions/`
3. **Custom guards**: Implement new guards for specific authorization needs
4. **Middleware extensions**: Add middleware for request/response processing