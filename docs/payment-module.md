# Payment Module Documentation

## Overview

The Payment Module is a flexible, extensible system designed to handle payment processing across different parts of the application. It provides a unified interface for creating, processing, and managing payments while abstracting away the complexities of different payment providers.

## Core Concepts

### Entity ID and Entity Type

One of the key design features of the payment module is its ability to associate payments with any entity in the system through the `entityId` and `entityType` fields:

- **Entity Type**: A string identifier that represents the type of entity related to the payment (e.g., "order", "invoice", "subscription", etc.). This field allows the payment module to categorize payments based on what they're for.

- **Entity ID**: A UUID that uniquely identifies the specific entity instance being paid for. This is typically the primary key of the related entity.

Together, these fields create a flexible relationship system that allows payments to be associated with any entity in the application without requiring direct database relationships. This design enables:

1. **Decoupling**: The payment module doesn't need to know the details of the entities it's processing payments for.
2. **Flexibility**: New entity types can be added without modifying the payment module's database schema.
3. **Querying**: Payments can be easily queried by entity type and ID, allowing for efficient retrieval of all payments related to a specific entity.

For example, when processing a payment for an order, you would set:
- `entityType` = "order"
- `entityId` = "[UUID of the specific order]"

### Module Flexibility

The payment module is designed with flexibility in mind, allowing it to be integrated with various parts of the application:

#### 1. Provider Abstraction

The module abstracts payment provider details through a provider interface. Currently, it implements Stripe, but the architecture allows for easy addition of other providers:

- The `PaymentProvider` enum can be extended to include new providers
- New provider services (similar to `StripeService`) can be implemented for each provider
- The `PaymentService` can be extended to use the appropriate provider based on configuration

#### 2. Payment Method Flexibility

The module supports multiple payment method types through the `PaymentMethodType` enum:
- Credit Card
- Bank Transfer
- Digital Wallet
- Cash
- Other

New payment methods can be added by extending this enum and implementing the necessary provider-specific logic.

#### 3. Payment Status Workflow

The payment lifecycle is managed through the `PaymentStatus` enum:
- Pending
- Processing
- Completed
- Failed
- Refunded
- PartiallyRefunded
- Cancelled

This workflow accommodates various payment scenarios and can be extended for more complex requirements.

#### 4. Webhook Support

The module includes webhook handling for payment providers, allowing for asynchronous payment processing and status updates:
- `PaymentWebhookController` processes incoming webhook events
- Provider-specific webhook handling (e.g., for Stripe events)

#### 5. Multi-Tenant Support

The payment module is designed with multi-tenancy in mind:
- All payments are associated with a `tenantId`
- Access control ensures tenants can only access their own payments
- Payment methods are tenant-specific

## Integration Examples

### Creating a Payment for an Order

```typescript
// In an order service
const payment = await paymentService.createPayment(
  tenantId,
  userId,
  orderTotal,
  'USD',
  'order',  // entityType
  orderId,  // entityId
  customerId,
  customerEmail,
  customerName,
  paymentMethodId,
  `Payment for Order #${orderReference}`,
  { orderReference }
);
```

### Retrieving Payments for an Entity

```typescript
// Get all payments for a specific order
const orderPayments = await paymentService.getPaymentsForEntity('order', orderId);
```

### Processing a Refund

```typescript
// Process a refund for a payment
const refundResult = await paymentService.refundPayment(
  paymentId,
  userId,
  refundAmount,
  'Customer requested refund'
);
```

## Extending the Payment Module

### Adding a New Payment Provider

1. Add the provider to the `PaymentProvider` enum
2. Create a new provider service implementing the necessary methods
3. Update the `PaymentService` to use the new provider when specified

### Adding a New Entity Type

No changes to the payment module are needed! Simply use the new entity type string when creating payments:

```typescript
await paymentService.createPayment(
  tenantId,
  userId,
  amount,
  currency,
  'new_entity_type',  // Your new entity type
  entityId,
  // ... other parameters
);
```

## Security Considerations

- All payment operations require tenant validation
- Payment methods are securely stored with provider-specific encryption
- Sensitive payment details are not stored in the application database
- Webhook endpoints are secured with provider-specific signature verification

## Conclusion

The payment module's flexible design allows it to be integrated with any part of the application that requires payment processing. The use of `entityType` and `entityId` creates a powerful abstraction layer that decouples the payment system from the specific entities being paid for, making the module highly reusable and adaptable to future requirements.