# Transport Application - Simplified Architecture Overview

## System Overview

```mermaid
flowchart TD
    Client[Client Applications] --> API[API Gateway]
    API --> AppModule[App Module]
    
    AppModule --> AdminModule[Admin Module]
    AppModule --> BusinessModule[Business Module]
    AppModule --> CoreModule[Core Services]
    AppModule --> Database[(Database)]
    
    CoreModule --> External[External Services]
    
    classDef client fill:#f9f,stroke:#333,stroke-width:2px
    classDef api fill:#bbf,stroke:#333,stroke-width:2px
    classDef modules fill:#dfd,stroke:#333,stroke-width:1px
    classDef data fill:#fbb,stroke:#333,stroke-width:2px
    classDef ext fill:#fdb,stroke:#333,stroke-width:1px
    
    class Client client
    class API api
    class AppModule,AdminModule,BusinessModule,CoreModule modules
    class Database data
    class External ext
```

## Key Components

### Client Layer
- Web Portal for customers
- Mobile Apps for customers and drivers
- Admin Interface for system management

### API Gateway
- Entry point for all client requests
- Authentication and authorization
- Request routing

### App Module
- Root module that brings together all components
- Configuration management
- Application bootstrapping

### Admin Module
- Admin management
- System settings
- Master data management

### Business Module
- Order management
- Pricing and zones
- User and vehicle management
- Address management

### Core Services
- Authentication and authorization
- Payment processing
- Notifications
- Pricing engine
- File storage
- Infrastructure services (logging, monitoring)

### Database
- PostgreSQL database
- Multi-admin data storage
- Data access repositories

### External Services
- Payment providers (Stripe)
- Notification services (Twilio)
- File storage (S3)
- Analytics and monitoring

## Basic Data Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Service
    participant Database
    participant External
    
    Client->>API: Request
    API->>Service: Process
    Service->>Database: Query/Update
    Service->>External: External Operation
    External-->>Service: Result
    Database-->>Service: Data
    Service-->>API: Response
    API-->>Client: Result
```

## Multi-Admin Architecture

```mermaid
flowchart LR
    Request[Request] --> AdminMiddleware[Admin Middleware]
    AdminMiddleware --> AdminContext[Admin Context]
    AdminContext --> BusinessLogic[Business Logic]
    BusinessLogic --> Database[(Database)]
    
    classDef flow fill:#dfd,stroke:#333,stroke-width:1px
    classDef data fill:#fbb,stroke:#333,stroke-width:2px
    
    class Request,AdminMiddleware,AdminContext,BusinessLogic flow
    class Database data
```

This simplified architecture document provides a high-level overview of the Transport Application system structure and key components without diving into implementation details.