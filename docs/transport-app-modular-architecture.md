# Transport Application - Comprehensive System Architecture

This document provides an in-depth view of the Transport Application system architecture using a modular approach for better visualization and understanding.

## 1. High-Level System Overview

```mermaid
flowchart LR
    Client[Client Applications] --> API[API Gateway]
    API --> Core[Core Services]
    API --> Business[Business Domain]
    API --> Admin[Admin Domain]
    
    Core <--> DB[(Database)]
    Business <--> DB
    Admin <--> DB
    
    Core <--> External[External Services]
    
    classDef primary fill:#bbf,stroke:#333,stroke-width:2px
    classDef secondary fill:#dfd,stroke:#333,stroke-width:1px
    classDef data fill:#fbb,stroke:#333,stroke-width:2px
    classDef ext fill:#fdb,stroke:#333,stroke-width:1px
    
    class Client,API primary
    class Core,Business,Admin secondary
    class DB data
    class External ext
```

### System Layers

1. **Client Layer**
   - Web Portal: Customer-facing web application for order management
   - Mobile Applications: Native iOS/Android apps for customers and drivers
   - Admin Interface: Back-office system for administrators and dispatchers
   - Third-Party Integrations: External systems connecting via API

2. **API Gateway**
   - Centralized entry point for all client requests
   - Request routing and load balancing
   - Authentication and authorization enforcement
   - Request/response transformation
   - Rate limiting and throttling

3. **Core Domain**
   - Foundational services used across the application
   - Authentication, payment processing, notifications
   - Infrastructure services (logging, monitoring, etc.)

4. **Business Domain**
   - Order management and processing
   - Pricing and zone management
   - User and vehicle management
   - Address validation and geocoding

5. **Admin Domain**
   - Multi-tenant management
   - System configuration and settings
   - Data migration tools
   - Master data management

6. **Data Layer**
   - PostgreSQL database for persistent storage
   - Database migrations and seeding
   - Data access repositories

7. **External Services**
   - Payment processing (Stripe)
   - Notifications (Twilio)
   - File storage (S3)
   - Analytics and error tracking

## 2. Core Services Architecture

```mermaid
flowchart TD
    Core[Core Module] --> Auth[Auth Module]
    Core --> Payment[Payment Module]
    Core --> Notification[Notification Module]
    Core --> Pricing[Pricing Core Module]
    Core --> RBAC[RBAC Module]
    Core --> Mail[Mail Module]
    Core --> Storage[File Storage]
    Core --> Analytics[Analytics]
    Core --> Infra[Infrastructure]
    
    Auth --> AuthService[Auth Service]
    Auth --> JWTStrategy[JWT Strategy]
    Auth --> AuthGuards[Auth Guards]
    
    Payment --> PaymentService[Payment Service]
    Payment --> StripeService[Stripe Service]
    Payment --> PaymentWebhooks[Payment Webhooks]
    
    Notification --> NotificationService[Notification Service]
    Notification --> TemplateService[Template Service]
    Notification --> TwilioService[Twilio Service]
    
    Pricing --> PriceCalculator[Price Calculator]
    Pricing --> StrategyRegistry[Strategy Registry]
    Pricing --> CalculationUtils[Calculation Utils]
    
    RBAC --> RBACService[RBAC Service]
    RBAC --> PermissionManager[Permission Manager]
    
    Infra --> Logger[Logger Service]
    Infra --> Monitoring[Monitoring]
    Infra --> ErrorTracking[Error Tracking]
    Infra --> Correlation[Correlation Service]
    Infra --> Tracing[Tracing Service]
    
    classDef module fill:#bbf,stroke:#333,stroke-width:2px
    classDef service fill:#dfd,stroke:#333,stroke-width:1px
    
    class Core module
    class Auth,Payment,Notification,Pricing,RBAC,Mail,Storage,Analytics,Infra module
    class AuthService,JWTStrategy,AuthGuards,PaymentService,StripeService,PaymentWebhooks,NotificationService,TemplateService,TwilioService,PriceCalculator,StrategyRegistry,CalculationUtils,RBACService,PermissionManager,Logger,Monitoring,ErrorTracking,Correlation,Tracing service
```

### Core Services Details

1. **Auth Module**
   - **Auth Service**: Handles user authentication and token management
   - **JWT Strategy**: Implements JWT-based authentication
   - **Auth Guards**: Protects routes based on authentication status
   - **Key Features**: Token generation/validation, password hashing, session management

2. **Payment Module**
   - **Payment Service**: Orchestrates payment processing
   - **Stripe Service**: Integration with Stripe payment gateway
   - **Payment Webhooks**: Handles asynchronous payment events
   - **Key Features**: Payment method management, transaction processing, refunds

3. **Notification Module**
   - **Notification Service**: Central service for sending notifications
   - **Template Service**: Manages notification templates
   - **Twilio Service**: SMS notification delivery
   - **Key Features**: Multi-channel notifications, templating, delivery tracking

4. **Pricing Core Module**
   - **Price Calculator Service**: Calculates prices based on complex rules
   - **Strategy Registry Service**: Manages pricing calculation strategies
   - **Calculation Utils**: Utility functions for pricing calculations
   - **Key Features**: Strategy pattern implementation, extensible pricing models

5. **RBAC Module**
   - **RBAC Service**: Role-based access control management
   - **Permission Manager**: Handles permission assignments and checks
   - **Key Features**: Role management, permission checking, tenant-specific permissions

6. **Infrastructure Module**
   - **Logger Service**: Centralized logging
   - **Monitoring Service**: System health monitoring
   - **Error Tracking**: Error capture and reporting
   - **Correlation Service**: Request correlation for distributed tracing
   - **Tracing Service**: Performance monitoring and tracing
   - **Key Features**: Structured logging, metrics collection, distributed tracing

## 3. Business Domain Architecture

```mermaid
flowchart TD
    Business[Business Module] --> Order[Order Module]
    Business --> Pricing[Pricing Module]
    Business --> User[User Module]
    Business --> Address[Address Module]
    Business --> Zone[Zone Module]
    Business --> Vehicle[Vehicle Module]
    Business --> Customer[Customer Portal]
    Business --> Mobile[Mobile API]
    
    Order --> OrderController[Order Controller]
    Order --> OrderService[Order Service]
    Order --> OrderRepository[Order Repository]
    Order --> OrderStatus[Order Status Service]
    Order --> OrderValidation[Order Validation]
    Order --> OrderEvents[Order Events]
    
    Pricing --> PriceSetController[Price Set Controller]
    Pricing --> PriceSetService[Price Set Service]
    Pricing --> ModifierController[Modifier Controller]
    Pricing --> ModifierService[Price Modifier Service]
    Pricing --> CalculationService[Calculation Service]
    
    User --> UserController[User Controller]
    User --> UserService[User Service]
    User --> UserRepository[User Repository]
    User --> ProfileService[Profile Service]
    
    Address --> AddressController[Address Controller]
    Address --> AddressService[Address Service]
    Address --> AddressRepository[Address Repository]
    Address --> ValidationService[Address Validation]
    Address --> GeocodeService[Geocoding Service]
    
    Zone --> ZoneController[Zone Controller]
    Zone --> ZoneService[Zone Service]
    Zone --> ZoneRepository[Zone Repository]
    Zone --> ZoneTableService[Zone Table Service]
    
    Vehicle --> VehicleController[Vehicle Controller]
    Vehicle --> VehicleService[Vehicle Service]
    Vehicle --> VehicleRepository[Vehicle Repository]
    Vehicle --> DriverService[Driver Service]
    Vehicle --> FleetService[Fleet Management]
    
    classDef module fill:#bbf,stroke:#333,stroke-width:2px
    classDef controller fill:#def,stroke:#333,stroke-width:1px
    classDef service fill:#dfd,stroke:#333,stroke-width:1px
    classDef repo fill:#fee,stroke:#333,stroke-width:1px
    
    class Business module
    class Order,Pricing,User,Address,Zone,Vehicle,Customer,Mobile module
    class OrderController,PriceSetController,ModifierController,UserController,AddressController,ZoneController,VehicleController controller
    class OrderService,OrderStatus,OrderValidation,OrderEvents,PriceSetService,ModifierService,CalculationService,UserService,ProfileService,AddressService,ValidationService,GeocodeService,ZoneService,ZoneTableService,VehicleService,DriverService,FleetService service
    class OrderRepository,UserRepository,AddressRepository,ZoneRepository,VehicleRepository repo
```

### Business Domain Details

1. **Order Module**
   - **Order Controller**: REST endpoints for order operations
   - **Order Service**: Business logic for order processing
   - **Order Repository**: Data access for orders
   - **Order Status Service**: Manages order status transitions
   - **Order Validation**: Validates order data
   - **Order Events**: Event-driven notifications for order changes
   - **Key Features**: Order creation, tracking, status updates, history

2. **Pricing Module**
   - **Price Set Controller**: Manages price set configurations
   - **Price Set Service**: Business logic for price sets
   - **Modifier Controller**: Manages price modifiers
   - **Price Modifier Service**: Business logic for price modifiers
   - **Calculation Service**: Integrates with core pricing engine
   - **Key Features**: Price set management, modifier configuration, zone-based pricing

3. **User Module**
   - **User Controller**: User management endpoints
   - **User Service**: User business logic
   - **User Repository**: Data access for users
   - **Profile Service**: User profile management
   - **Key Features**: User registration, profile management, preferences

4. **Address Module**
   - **Address Controller**: Address management endpoints
   - **Address Service**: Address business logic
   - **Address Repository**: Data access for addresses
   - **Validation Service**: Address validation
   - **Geocode Service**: Geocoding and reverse geocoding
   - **Key Features**: Address storage, validation, geocoding, standardization

5. **Zone Module**
   - **Zone Controller**: Zone management endpoints
   - **Zone Service**: Zone business logic
   - **Zone Repository**: Data access for zones
   - **Zone Table Service**: Zone table management
   - **Key Features**: Zone definition, postal code mapping, zone tables

6. **Vehicle Module**
   - **Vehicle Controller**: Vehicle management endpoints
   - **Vehicle Service**: Vehicle business logic
   - **Vehicle Repository**: Data access for vehicles
   - **Driver Service**: Driver management
   - **Fleet Service**: Fleet management
   - **Key Features**: Vehicle tracking, driver assignment, maintenance

## 4. Admin Domain Architecture

```mermaid
flowchart TD
    Admin[Admin Module] --> Tenant[Tenant Management]
    Admin --> Settings[System Settings]
    Admin --> Migration[OnTime360 Migration]
    Admin --> Master[Master Data]
    
    Tenant --> TenantController[Tenant Controller]
    Tenant --> TenantService[Tenant Service]
    Tenant --> TenantRepo[Tenant Repository]
    Tenant --> TenantProvisioning[Tenant Provisioning]
    
    Settings --> SettingsController[Settings Controller]
    Settings --> SettingsService[Settings Service]
    Settings --> SettingsRepo[Settings Repository]
    Settings --> ConfigValidation[Config Validation]
    
    Migration --> MigrationController[Migration Controller]
    Migration --> MigrationService[Migration Service]
    Migration --> DataTransformation[Data Transformation]
    
    Master --> MasterController[Master Controller]
    Master --> MasterService[Master Data Service]
    Master --> MasterRepo[Master Repository]
    
    classDef module fill:#bbf,stroke:#333,stroke-width:2px
    classDef controller fill:#def,stroke:#333,stroke-width:1px
    classDef service fill:#dfd,stroke:#333,stroke-width:1px
    classDef repo fill:#fee,stroke:#333,stroke-width:1px
    
    class Admin module
    class Tenant,Settings,Migration,Master module
    class TenantController,SettingsController,MigrationController,MasterController controller
    class TenantService,TenantProvisioning,SettingsService,ConfigValidation,MigrationService,DataTransformation,MasterService service
    class TenantRepo,SettingsRepo,MasterRepo repo
```

### Admin Domain Details

1. **Tenant Management**
   - **Tenant Controller**: Multi-tenant management endpoints
   - **Tenant Service**: Tenant business logic
   - **Tenant Repository**: Data access for tenants
   - **Tenant Provisioning**: New tenant setup and configuration
   - **Key Features**: Tenant creation, configuration, isolation, billing

2. **System Settings**
   - **Settings Controller**: System settings endpoints
   - **Settings Service**: Settings business logic
   - **Settings Repository**: Data access for settings
   - **Config Validation**: Validates configuration changes
   - **Key Features**: Global settings, tenant-specific settings, validation

3. **OnTime360 Migration**
   - **Migration Controller**: Migration control endpoints
   - **Migration Service**: Migration orchestration
   - **Data Transformation**: Legacy data transformation
   - **Key Features**: Data import, mapping, validation, transformation

4. **Master Data**
   - **Master Controller**: Master data management endpoints
   - **Master Service**: Master data business logic
   - **Master Repository**: Data access for master data
   - **Key Features**: Reference data management, validation, versioning

## 5. Data Flow: Order Processing

```mermaid
sequenceDiagram
    participant Client
    participant API as API Gateway
    participant Order as Order Service
    participant Validation as Order Validation
    participant Pricing as Pricing Service
    participant Zone as Zone Service
    participant Payment as Payment Service
    participant Notification as Notification Service
    participant DB as Database
    
    Client->>API: Create Order Request
    API->>Order: Process Order
    Order->>Validation: Validate Order Data
    Validation-->>Order: Validation Result
    
    Order->>Zone: Get Zone Information
    Zone-->>Order: Zone Data
    
    Order->>Pricing: Calculate Price
    Pricing->>Zone: Get Zone-Based Price
    Zone-->>Pricing: Zone Price
    Pricing-->>Order: Price Result
    
    Order->>DB: Save Order
    DB-->>Order: Order ID
    
    Order->>Payment: Process Payment
    Payment->>Payment: Charge Customer
    Payment-->>Order: Payment Result
    
    Order->>DB: Update Order Status
    
    Order->>Notification: Send Confirmation
    Notification->>Notification: Generate Notification
    Notification-->>Client: Order Confirmation
    
    Order-->>API: Order Result
    API-->>Client: Order Response with Tracking
```

### Order Processing Flow Details

1. **Order Creation**
   - Client submits order with pickup/delivery details
   - API Gateway validates request format and authentication
   - Order Service receives the request for processing

2. **Order Validation**
   - Order Validation Service checks data completeness and validity
   - Validates addresses, time windows, service requirements
   - Returns validation results to Order Service

3. **Zone and Pricing Determination**
   - Zone Service identifies origin/destination zones
   - Pricing Service calculates price based on:
     - Zone-based pricing
     - Distance calculation
     - Service type and requirements
     - Applicable modifiers (weight, dimensions, etc.)
     - Time-based factors (rush hour, weekend, etc.)

4. **Order Persistence**
   - Order is saved to database with initial status
   - Order ID and tracking information generated

5. **Payment Processing**
   - Payment Service processes payment based on calculated price
   - Handles different payment methods (credit card, account, etc.)
   - Updates order with payment status

6. **Notification**
   - Notification Service sends confirmation to customer
   - Notifications may include SMS, email, or in-app messages
   - Contains order details and tracking information

7. **Response**
   - Order details and tracking information returned to client
   - Includes estimated pickup/delivery times

## 6. Data Flow: Authentication and Authorization

```mermaid
sequenceDiagram
    participant Client
    participant API as API Gateway
    participant Auth as Auth Service
    participant RBAC as RBAC Service
    participant Tenant as Tenant Service
    participant DB as Database
    
    Client->>API: Login Request (username/password)
    API->>Auth: Authenticate User
    Auth->>DB: Verify Credentials
    DB-->>Auth: User Data
    
    Auth->>Tenant: Get Tenant Context
    Tenant-->>Auth: Tenant Information
    
    Auth->>RBAC: Get User Permissions
    RBAC->>DB: Fetch Roles & Permissions
    DB-->>RBAC: Role Assignments
    RBAC-->>Auth: User Permissions
    
    Auth->>Auth: Generate JWT with Claims
    Auth-->>API: Auth Token + Refresh Token
    API-->>Client: Auth Response
    
    Note over Client,API: Later: Protected Request
    
    Client->>API: Protected Request + Token
    API->>Auth: Validate Token
    Auth->>Auth: Verify Signature & Expiry
    Auth->>Tenant: Set Tenant Context
    Auth->>RBAC: Check Required Permissions
    RBAC-->>Auth: Permission Result
    Auth-->>API: Authorization Result
    
    alt Authorized
        API->>API: Process Request
        API-->>Client: Success Response
    else Unauthorized
        API-->>Client: 403 Forbidden
    end
```

### Authentication Flow Details

1. **Login Request**
   - Client submits credentials (username/password)
   - API Gateway forwards to Auth Service

2. **Authentication**
   - Auth Service verifies credentials against database
   - Retrieves user data and associated tenant

3. **Tenant Context**
   - Tenant Service provides tenant-specific information
   - Sets up multi-tenant context for the user

4. **Permission Resolution**
   - RBAC Service retrieves user's roles and permissions
   - Resolves effective permissions based on role assignments

5. **Token Generation**
   - Auth Service generates JWT with user claims:
     - User identity
     - Tenant information
     - Roles and permissions
     - Expiration time
   - Refresh token generated for token renewal

6. **Protected Request Flow**
   - Client includes token in subsequent requests
   - API Gateway extracts and forwards token to Auth Service
   - Auth Service validates token signature and expiry
   - Tenant context is established
   - RBAC Service verifies required permissions
   - Request is processed if authorized

7. **Token Renewal**
   - Client uses refresh token to obtain new access token
   - Prevents frequent re-authentication

## 7. External Integrations

```mermaid
flowchart LR
    App[Transport Application] --> Payment[Payment Processing]
    App --> Notification[Notification Delivery]
    App --> Storage[File Storage]
    App --> Analytics[Analytics Tracking]
    App --> ErrorTracking[Error Tracking]
    App --> Email[Email Delivery]
    App --> Maps[Maps & Geocoding]
    
    Payment --> Stripe[Stripe API]
    Payment --> PayPal[PayPal API]
    
    Notification --> Twilio[Twilio SMS]
    Notification --> FCM[Firebase Cloud Messaging]
    Notification --> APNS[Apple Push Notification]
    
    Storage --> S3[AWS S3]
    
    Analytics --> Posthog[Posthog API]
    Analytics --> GA[Google Analytics]
    
    ErrorTracking --> Sentry[Sentry API]
    
    Email --> SMTP[SMTP Provider]
    
    Maps --> Google[Google Maps API]
    Maps --> Here[HERE Maps API]
    
    classDef app fill:#bbf,stroke:#333,stroke-width:2px
    classDef service fill:#dfd,stroke:#333,stroke-width:1px
    classDef external fill:#fdb,stroke:#333,stroke-width:1px
    
    class App app
    class Payment,Notification,Storage,Analytics,ErrorTracking,Email,Maps service
    class Stripe,PayPal,Twilio,FCM,APNS,S3,Posthog,GA,Sentry,SMTP,Google,Here external
```

### External Integrations Details

1. **Payment Processing**
   - **Stripe API**: Primary payment processor
     - Credit card processing
     - Subscription management
     - Payment method storage
   - **PayPal API**: Alternative payment method
     - PayPal account payments
     - Express checkout

2. **Notification Delivery**
   - **Twilio SMS**: SMS notifications
     - Order confirmations
     - Status updates
     - Delivery notifications
   - **Firebase Cloud Messaging**: Android push notifications
   - **Apple Push Notification Service**: iOS push notifications

3. **File Storage**
   - **AWS S3**: Cloud storage for:
     - Delivery confirmation photos
     - Signature captures
     - User profile images
     - Document storage

4. **Analytics Tracking**
   - **Posthog API**: Primary analytics platform
     - User behavior tracking
     - Conversion funnels
     - Feature usage
   - **Google Analytics**: Web traffic analysis

5. **Error Tracking**
   - **Sentry API**: Error monitoring and reporting
     - Exception capture
     - Performance monitoring
     - Issue tracking

6. **Email Delivery**
   - **SMTP Provider**: Email delivery service
     - Transactional emails
     - Marketing communications
     - System notifications

7. **Maps & Geocoding**
   - **Google Maps API**: Primary mapping provider
     - Address geocoding
     - Route optimization
     - Distance calculation
   - **HERE Maps API**: Alternative mapping provider

## 8. Database Schema Overview

```mermaid
erDiagram
    TENANT {
        uuid id PK
        string name
        string domain
        boolean active
        jsonb settings
    }
    
    USER {
        uuid id PK
        uuid tenant_id FK
        string email
        string password_hash
        string first_name
        string last_name
        boolean active
    }
    
    ROLE {
        uuid id PK
        uuid tenant_id FK
        string name
        string description
    }
    
    ROLE_ASSIGNMENT {
        uuid id PK
        uuid user_id FK
        uuid role_id FK
    }
    
    PERMISSION {
        uuid id PK
        string name
        string description
    }
    
    ROLE_PERMISSION {
        uuid id PK
        uuid role_id FK
        uuid permission_id FK
    }
    
    ORDER {
        uuid id PK
        uuid tenant_id FK
        uuid customer_id FK
        string status
        timestamp created_at
        timestamp updated_at
        decimal total_price
        jsonb metadata
    }
    
    ORDER_ITEM {
        uuid id PK
        uuid order_id FK
        string description
        int quantity
        decimal price
    }
    
    ADDRESS {
        uuid id PK
        uuid tenant_id FK
        string street
        string city
        string state
        string postal_code
        string country
        float latitude
        float longitude
    }
    
    ZONE {
        uuid id PK
        uuid tenant_id FK
        string name
        string description
    }
    
    ZONE_POSTAL_CODE {
        uuid id PK
        uuid zone_id FK
        string postal_code
    }
    
    PRICE_SET {
        uuid id PK
        uuid tenant_id FK
        string name
        string internal_name
        boolean active
        jsonb availability
    }
    
    PRICE_MODIFIER {
        uuid id PK
        uuid tenant_id FK
        string name
        string calculation_type
        string calculation_field
        decimal value
        boolean is_group
        string group_behavior
    }
    
    TENANT ||--o{ USER : "has"
    TENANT ||--o{ ROLE : "has"
    TENANT ||--o{ ORDER : "has"
    TENANT ||--o{ ADDRESS : "has"
    TENANT ||--o{ ZONE : "has"
    TENANT ||--o{ PRICE_SET : "has"
    TENANT ||--o{ PRICE_MODIFIER : "has"
    
    USER }o--o{ ROLE : "assigned via"
    ROLE }o--o{ PERMISSION : "has"
    
    ORDER ||--o{ ORDER_ITEM : "contains"
    ORDER ||--o{ ADDRESS : "has pickup/delivery"
    
    ZONE ||--o{ ZONE_POSTAL_CODE : "contains"
    
    PRICE_SET ||--o{ PRICE_MODIFIER : "uses"
```

### Key Database Entities

1. **Multi-tenant Structure**
   - Tenant table as the root of tenant isolation
   - Tenant-specific data segregation across entities
   - Shared tables with tenant_id for data isolation

2. **User Management**
   - User accounts with authentication data
   - Role-based permission system
   - Flexible permission assignments

3. **Order Management**
   - Orders with items and status tracking
   - Address associations for pickup/delivery
   - Price and payment information

4. **Pricing Structure**
   - Price sets with availability rules
   - Price modifiers with calculation types
   - Zone-based pricing tables

5. **Geographic Data**
   - Address storage with geocoding data
   - Zone definitions with postal code mappings
   - Distance and time calculations

This comprehensive architecture documentation provides an in-depth view of the Transport Application system, breaking down complex components into understandable modules while maintaining the relationships between them.