# Transport Application API Documentation

Welcome to the Transport Application API documentation. This section contains detailed information about the various modules and components of the system.

## Modules

- [Payment Module](payment-module.md) - Documentation for the flexible payment processing system
- [Pricing System](pricing-guide.md) - Comprehensive guide to the pricing system including price sets, modifiers, and zone-based pricing
- [Tenant Management](tenant-management.md) - Explanation of the multi-tenant architecture and tenant isolation
- [Core Modules](core-modules.md) - Overview of authentication, infrastructure, and cross-cutting concerns

## Architecture

The Transport Application API is built using NestJS and follows a modular architecture with clear separation of concerns. The application is designed to be scalable, maintainable, and extensible.

## Getting Started

For information on how to set up and run the application, please refer to the main [README.md](../README.md) file.