pipelines:
  branches:
    develop:
      - step:
          name: Deploy to Development Server
          script:
            - pipe: atlassian/ssh-run:0.2.2
              variables:
                SSH_USER: $SSH_USER
                SERVER: $SSH_HOST
                COMMAND: >
                  cd /root/transportapp-api/transportapplication_api;
                  git fetch;
                  git checkout develop;
                  git reset --hard origin/develop;
                  npm install;
                  npm run build;
                  docker compose -f docker-compose.prod.yml down || true;
                  docker compose -f docker-compose.prod.yml up --build -d;
