flowchart TD
    %% Entities
    Tenant[Tenant]
    Customer[Customer]
    Contact[Contact]
    Order[Order]
    PriceSet[Price Set]
    PackageTemplate[Package Template]
    Address[Address]
    Driver[Driver]
    Vehicle[Vehicle]
    
    %% Registration Flow
    Register[Register Tenant] --> Tenant
    Tenant --> |Creates| CreateCustomer[Create Customer]
    CreateCustomer --> Customer
    Customer --> |Has| CreateContact[Create Contact]
    CreateContact --> Contact
    
    %% Login Flow
    Contact --> |Logs in| Login[Contact Login]
    Login --> |Authenticated| Portal[Customer Portal]
    
    %% Order Creation Flow
    Portal --> |Enter| Locations[Pickup & Delivery Locations]
    Portal --> |Select| Packages[Package Details]
    Packages --> |May use| PackageTemplate
    
    %% Service Selection Flow
    Locations --> |Search for| Services[Available Services]
    Services --> |Based on| PriceSet
    PriceSet --> |Calculate| Prices[Price Breakdown]
    
    %% Order Placement Flow
    Prices --> |Select service| PlaceOrder[Place Order]
    PlaceOrder --> |Creates| Order
    
    %% Order Details
    Order --> |Has| OrderItems[Order Items]
    Order --> |Has| StopHistory[Stop History]
    Order --> |May be assigned to| Driver
    Order --> |May use| Vehicle
    
    %% Data Relationships
    Tenant --- |Owns| Customer
    Customer --- |Has| Contact
    Contact --- |Places| Order
    Order --- |Uses| Address
    Address --- |For pickup/delivery| Order
    
    %% Controllers and Services
    TenantController[Tenants Controller] --- Tenant
    CustomerController[Users Controller] --- Customer
    ContactController[Contacts Controller] --- Contact
    OrderController[Orders Controller] --- Order
    PriceSetController[Price Sets Controller] --- PriceSet
    
    %% Services
    TenantService[Tenants Service] --- Tenant
    CustomerService[Users Service] --- Customer
    ContactService[Contacts Service] --- Contact
    OrderService[Orders Service] --- Order
    PriceCalculatorService[Price Calculator Service] --- PriceSet
