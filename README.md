# Transport Application API

A comprehensive API for transport and logistics management built with NestJS.

## Description

The Transport Application API is a robust backend system designed to handle various aspects of transport and logistics operations. It provides a set of RESTful endpoints for managing orders, payments, users, and more.

## Table of Contents

- [Description](#description)
- [Table of Contents](#table-of-contents)
- [Features](#features)
- [Architecture](#architecture)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Running the Application](#running-the-application)
    - [Development Mode](#development-mode)
    - [Using Docker Compose](#using-docker-compose)
- [Docker Components](#docker-components)
  - [Core Application](#core-application)
  - [Observability Stack](#observability-stack)
  - [Development Tools](#development-tools)
- [Documentation](#documentation)
- [Development Notes](#development-notes)
  - [Tenant Module Implementation Note](#tenant-module-implementation-note)
- [Testing](#testing)
  - [Running Tests](#running-tests)
  - [CI/CD](#cicd)

## Features

- **Core Infrastructure**
  - Robust error handling and exception tracking
  - Correlation ID tracking for request tracing
  - Comprehensive logging system
  - Monitoring and metrics collection
  - Distributed tracing with OpenTelemetry

- **Authentication & Authorization**
  - JWT-based authentication
  - Role-based access control
  - Tenant isolation for multi-tenant support

- **Payment Processing**
  - Flexible payment module that can integrate with any entity
  - Support for multiple payment providers (currently Stripe)
  - Webhook handling for asynchronous payment events

- **Business Logic**
  - Address management
  - Zone management and driver assignment
  - Pricing calculation with various strategies

- **Admin Features**
  - Master admin management
  - System settings configuration
  - Tenant management

## Architecture

The application follows a modular architecture with clear separation of concerns:

- **Core Modules**: Handle cross-cutting concerns like authentication, infrastructure, and payment processing
- **Business Modules**: Implement specific business logic for different domains
- **Admin Modules**: Provide administrative capabilities

The codebase is organized using Domain-Driven Design principles, with each module containing:
- Controllers: Handle HTTP requests
- Services: Implement business logic
- Domain models: Define the core entities and types
- DTOs: Define data transfer objects for API requests/responses
- Infrastructure: Handle persistence and external integrations

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn
- Docker and Docker Compose
- PostgreSQL

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/transportapplication_api.git
   cd transportapplication_api
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp env-example-relational .env
   ```
   Edit the `.env` file with your configuration.

### Running the Application

#### Development Mode

```bash
npm run start:dev
```

#### Using Docker Compose

```bash
docker-compose up
```

## Docker Components

The application includes several Docker components for a complete development and production environment:

### Core Application

- **API Service**: The main NestJS application
- **PostgreSQL**: Primary database
- **Redis**: Used for caching and session management

### Observability Stack

- **OpenTelemetry Collector**: Collects and processes telemetry data
- **Jaeger**: Distributed tracing system
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and monitoring

### Development Tools

- **MailDev**: Local SMTP server for email testing
- **PgAdmin**: PostgreSQL administration tool

## Documentation

Comprehensive documentation is available in the [docs](./docs/readme.md) directory, including:

- [Payment Module Documentation](./docs/payment-module.md)
- [Pricing System Documentation](./docs/pricing-guide.md)
- [Tenant Management Documentation](./docs/tenant-management.md)
- [Core Modules Documentation](./docs/core-modules.md)

API documentation is available at `/docs` when the application is running (Swagger UI).

## Development Notes

### Tenant Module Implementation Note

We initially discussed removing the tenant concept with Jigneshbhai, and while we agreed it made sense, the tenant system was already too deeply integrated into the codebase. Removing it would have required extensive refactoring.

As a compromise, the development team (Yash, Pranjal, Abhishek) kept the "tenant" term in the internal code but renamed it to "admin user" in the external interfaces. This approach preserved the existing code structure while simplifying the user-facing aspects.

In the future, we plan to implement proper multi-tenant functionality with support for multiple companies, but currently, we only need a single register API for onboarding.

## Testing

### Running Tests

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### CI/CD

The project uses Bitbucket Pipelines for continuous integration and deployment. The pipeline configuration is defined in `bitbucket-pipelines.yml`.
