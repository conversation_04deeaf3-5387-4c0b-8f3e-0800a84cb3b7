{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": true, "baseUrl": "./", "paths": {"@app/*": ["src/*"], "@core/*": ["src/core/*"], "@infrastructure/*": ["src/infrastructure/*"], "@modules/*": ["src/modules/*"], "@auth/*": ["src/modules/auth/*"], "@users/*": ["src/modules/users/*"], "@files/*": ["src/modules/files/*"], "@tenants/*": ["src/modules/tenants/*"], "@mail/*": ["src/modules/mail/*"], "@config/*": ["src/config/*"], "@database/*": ["src/database/*"], "@utils/*": ["src/utils/*"], "@common/*": ["src/common/*"]}}}