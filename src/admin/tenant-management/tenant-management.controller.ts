import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { TenantManagementService } from './tenant-management.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { TenantListResponseDto } from './dto/tenant-list.dto';

@ApiTags('Admin - Tenant Management')
@Controller({
  path: 'admin/tenants',
  version: '1',
})
@UseGuards(JwtAuthGuard)
export class TenantManagementController {
  constructor(
    private readonly tenantManagementService: TenantManagementService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all tenants' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'OK',
    type: TenantListResponseDto,
  })
  async findAll(
    @Query() filter: BaseFilterDto,
  ): Promise<TenantListResponseDto> {
    try {
      return (await this.tenantManagementService.findAll(
        filter,
      )) as TenantListResponseDto;
    } catch (error) {
      throw error;
    }
  }
}
