import { Injectable } from '@nestjs/common';
import { TenantsService } from '@app/business/user/tenants/tenants.service';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { InfinityPaginationResponseDto } from '@utils/dto/infinity-pagination-response.dto';
import { TenantDto } from '@app/business/user/tenants/dto/tenant.dto';
import { infinityPagination } from '@utils/infinity-pagination';

@Injectable()
export class TenantManagementService {
  constructor(private readonly tenantsService: TenantsService) {}

  async findAll(
    filter: BaseFilterDto,
  ): Promise<InfinityPaginationResponseDto<TenantDto>> {
    const page = filter.pageNumber || 1;
    const limit = filter.pageSize || 10;

    const { items, total } = await this.tenantsService.findAllWithPagination({
      paginationOptions: {
        page,
        limit,
      },
    });

    return infinityPagination(items, {
      page,
      limit,
      total,
    });
  }
}
