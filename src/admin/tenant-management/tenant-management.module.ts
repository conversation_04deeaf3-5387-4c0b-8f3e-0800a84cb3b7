import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { TenantManagementController } from './tenant-management.controller';
import { TenantManagementService } from './tenant-management.service';
import { TenantsModule } from '@app/business/user/tenants/tenants.module';

@Module({
  imports: [TenantsModule, MonitoringModule],
  controllers: [TenantManagementController],
  providers: [TenantManagementService],
})
export class TenantManagementModule {}
