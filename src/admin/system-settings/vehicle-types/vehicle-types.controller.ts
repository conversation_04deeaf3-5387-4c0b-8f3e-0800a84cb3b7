import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { SystemVehicleTypesService } from './vehicle-types.service';
import { CreateVehicleTypeDto } from '@app/business/vehicle/vehicle-types/dto/create-vehicle-type.dto';
import { GetVehicleTypeDto } from '@app/business/vehicle/vehicle-types/dto/get-vehicle-type.dto';
import { PageParamsRequest } from '@utils/page-params-request';
import { VehicleTypesListResponseDto } from './dto/vehicle-types-list.dto';

@ApiTags('Admin - System Settings - Vehicle Types')
@Controller({
  path: 'admin/system-settings/vehicle-types',
  version: '1',
})
@UseGuards(JwtAuthGuard)
export class VehicleTypesController {
  constructor(
    private readonly vehicleTypesService: SystemVehicleTypesService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Vehicle Type' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created', type: GetVehicleTypeDto })
  async create(
    @Body() createVehicleTypeDto: CreateVehicleTypeDto,
  ): Promise<GetVehicleTypeDto> {
    try {
      return await this.vehicleTypesService.create(createVehicleTypeDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all Vehicle Types' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: VehicleTypesListResponseDto })
  async findAll(
    @Query() queryParams: PageParamsRequest,
  ): Promise<VehicleTypesListResponseDto> {
    try {
      return await this.vehicleTypesService.findAll(queryParams);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get Vehicle Type by ID' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetVehicleTypeDto })
  async findOne(@Param('id') id: string): Promise<GetVehicleTypeDto> {
    try {
      return await this.vehicleTypesService.findOne(id);
    } catch (error) {
      throw error;
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update Vehicle Type' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async update(
    @Param('id') id: string,
    @Body() updateVehicleTypeDto: CreateVehicleTypeDto,
  ): Promise<void> {
    try {
      await this.vehicleTypesService.update(id, updateVehicleTypeDto);
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete Vehicle Type' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async remove(@Param('id') id: string): Promise<void> {
    try {
      await this.vehicleTypesService.remove(id);
    } catch (error) {
      throw error;
    }
  }
}
