import { Injectable } from '@nestjs/common';
import { VehicleTypesService as BusinessVehicleTypesService } from '@app/business/vehicle/vehicle-types/vehicle-types.service';
import { CreateVehicleTypeDto } from '@app/business/vehicle/vehicle-types/dto/create-vehicle-type.dto';
import { GetVehicleTypeDto } from '@app/business/vehicle/vehicle-types/dto/get-vehicle-type.dto';
import { PageParamsRequest } from '@utils/page-params-request';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { VehicleTypeDomain } from '@app/business/vehicle/vehicle-types/domain/vehicle-type';
import { VehicleTypesListResponseDto } from './dto/vehicle-types-list.dto';

@Injectable()
export class SystemVehicleTypesService {
  constructor(
    private readonly businessVehicleTypesService: BusinessVehicleTypesService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(
    createVehicleTypeDto: CreateVehicleTypeDto,
  ): Promise<GetVehicleTypeDto> {
    // Create a system-wide vehicle type (using a special system tenant ID)
    const systemTenantId =
      process.env.SYSTEM_TENANT_ID || '00000000-0000-0000-0000-000000000000';

    const vehicleTypeDomain = this.mapper.map(
      createVehicleTypeDto,
      CreateVehicleTypeDto,
      VehicleTypeDomain,
    );

    vehicleTypeDomain.tenantId = systemTenantId;

    const result =
      await this.businessVehicleTypesService.create(vehicleTypeDomain);

    return this.mapper.map(result, VehicleTypeDomain, GetVehicleTypeDto);
  }

  async findAll(
    queryParams: PageParamsRequest,
  ): Promise<VehicleTypesListResponseDto> {
    const result =
      await this.businessVehicleTypesService.getVehicleTypeList(queryParams);

    const mappedData = this.mapper.mapArray(
      result.data,
      VehicleTypeDomain,
      GetVehicleTypeDto,
    );

    return {
      totalCount: result.totalCount,
      data: mappedData,
    };
  }

  async findOne(id: string): Promise<GetVehicleTypeDto> {
    const result =
      await this.businessVehicleTypesService.getVehicleTypeDetails(id);

    return this.mapper.map(result, VehicleTypeDomain, GetVehicleTypeDto);
  }

  async update(
    id: string,
    updateVehicleTypeDto: CreateVehicleTypeDto,
  ): Promise<void> {
    const vehicleType =
      await this.businessVehicleTypesService.getVehicleTypeDetails(id);

    const updatedVehicleType = {
      ...vehicleType,
      ...this.mapper.map(
        updateVehicleTypeDto,
        CreateVehicleTypeDto,
        VehicleTypeDomain,
      ),
      id,
    };

    await this.businessVehicleTypesService.updateVehicleTypeDetails(
      updatedVehicleType,
    );
  }

  async remove(id: string): Promise<void> {
    await this.businessVehicleTypesService.deleteVehicleType(id);
  }
}
