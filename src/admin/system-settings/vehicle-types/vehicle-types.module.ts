import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { VehicleTypesController } from './vehicle-types.controller';
import { SystemVehicleTypesService } from './vehicle-types.service';
import { VehicleTypesModule as BusinessVehicleTypesModule } from '@app/business/vehicle/vehicle-types/vehicle-types.module';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';

@Module({
  imports: [
    BusinessVehicleTypesModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [VehicleTypesController],
  providers: [SystemVehicleTypesService],
})
export class VehicleTypesModule {}
