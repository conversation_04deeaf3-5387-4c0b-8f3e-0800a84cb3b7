/**
 * Price set modifier migration handler
 */
import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MigrationHandler, MigrationResult } from '../types/migration.types';
import { DataConversionUtil } from '../utils/data-conversion.util';
import { PriceSetModifierEntity } from '../../../business/pricing/price-sets/infrastructure/entities/price-set-modifier.entity';

@Injectable()
export class PriceSetModifierMigrationHandler implements MigrationHandler {
  private readonly logger = new Logger(PriceSetModifierMigrationHandler.name);
  private readonly dataConversionUtil = new DataConversionUtil();

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Migrate price set modifiers from source database to target database
   * @param sqliteDataSource DataSource connected to the SQLite database
   * @param tenantId ID of the tenant to associate the migrated data with
   * @returns Migration result
   */
  public async migrate(
    sqliteDataSource: DataSource,
    tenantId: string,
  ): Promise<MigrationResult> {
    try {
      // Track migration statistics
      let totalProcessed = 0;
      let migrated = 0;
      let skipped = 0;
      let errors = 0;

      // Log start of migration
      this.logger.log(
        `Starting migration of price set modifiers for tenant ${tenantId}`,
      );

      // Get all price set modifiers from source database
      const sourcePriceSetModifiers = await sqliteDataSource.query(
        `SELECT * FROM PriceSetModifiers;`,
      );

      // If no price set modifiers found, return early
      if (!sourcePriceSetModifiers || sourcePriceSetModifiers.length === 0) {
        return {
          success: true,
          message: 'No price set modifiers found to migrate',
          totalProcessed: 0,
          migrated: 0,
          skipped: 0,
          errors: 0,
        };
      }

      // Log number of price set modifiers found
      this.logger.log(
        `Found ${sourcePriceSetModifiers.length} price set modifiers to migrate`,
      );

      // Get repository for price set modifiers
      const priceSetModifierRepository = this.dataSource.getRepository(
        PriceSetModifierEntity,
      );

      // Get all price sets to map IDs
      const priceSets = await this.dataSource.query(
        `SELECT id, metadata FROM price_set WHERE tenant_id = $1;`,
        [tenantId],
      );

      // Create a map of source price set IDs to target price set IDs
      const priceSetIdMap = new Map<string, string>();
      for (const priceSet of priceSets) {
        try {
          const metadata = JSON.parse(priceSet.metadata);
          if (metadata && metadata.sourceId) {
            let sourceId = metadata.sourceId;
            if (typeof sourceId === 'object' && sourceId.type === 'Buffer') {
              sourceId = this.dataConversionUtil.bufferDataToUuid(
                sourceId.data,
              );
            }
            priceSetIdMap.set(sourceId.toString(), priceSet.id);
          }
        } catch (error) {
          this.logger.error(
            `Error parsing price set metadata: ${error.message}`,
          );
        }
      }

      // Get all modifiers to map IDs
      const modifiers = await this.dataSource.query(
        `SELECT id, metadata FROM price_modifier WHERE tenant_id = $1;`,
        [tenantId],
      );

      // Create a map of source modifier IDs to target modifier IDs
      const modifierIdMap = new Map<string, string>();
      for (const modifier of modifiers) {
        try {
          const metadata = JSON.parse(modifier.metadata);
          if (metadata && metadata.sourceId) {
            let sourceId = metadata.sourceId;
            if (typeof sourceId === 'object' && sourceId.type === 'Buffer') {
              sourceId = this.dataConversionUtil.bufferDataToUuid(
                sourceId.data,
              );
            }
            modifierIdMap.set(sourceId.toString(), modifier.id);
          }
        } catch (error) {
          this.logger.error(
            `Error parsing modifier metadata: ${error.message}`,
          );
        }
      }

      // Get all modifier groups to map IDs
      const modifierGroups = await this.dataSource.query(
        `SELECT id, metadata FROM price_modifier_group WHERE tenant_id = $1;`,
        [tenantId],
      );

      // Create a map of source group IDs to target group IDs
      const groupIdMap = new Map<string, string>();
      for (const group of modifierGroups) {
        try {
          const metadata = JSON.parse(group.metadata);
          if (metadata && metadata.sourceId) {
            let sourceId = metadata.sourceId;
            if (typeof sourceId === 'object' && sourceId.type === 'Buffer') {
              sourceId = this.dataConversionUtil.bufferDataToUuid(
                sourceId.data,
              );
            }
            groupIdMap.set(sourceId.toString(), group.id);
          }
        } catch (error) {
          this.logger.error(`Error parsing group metadata: ${error.message}`);
        }
      }

      // Process each price set modifier
      for (const sourcePriceSetModifier of sourcePriceSetModifiers) {
        totalProcessed++;

        try {
          // Get the source price set ID
          let sourcePriceSetId = sourcePriceSetModifier.PriceSetID;
          if (
            typeof sourcePriceSetId === 'object' &&
            sourcePriceSetId.type === 'Buffer'
          ) {
            sourcePriceSetId = this.dataConversionUtil.bufferDataToUuid(
              sourcePriceSetId.data,
            );
          } else if (Buffer.isBuffer(sourcePriceSetId)) {
            sourcePriceSetId =
              this.dataConversionUtil.bufferToUuid(sourcePriceSetId);
          }

          // Get the source modifier ID
          let sourceModifierId = sourcePriceSetModifier.ModifierID;
          if (
            typeof sourceModifierId === 'object' &&
            sourceModifierId.type === 'Buffer'
          ) {
            sourceModifierId = this.dataConversionUtil.bufferDataToUuid(
              sourceModifierId.data,
            );
          } else if (Buffer.isBuffer(sourceModifierId)) {
            sourceModifierId =
              this.dataConversionUtil.bufferToUuid(sourceModifierId);
          }

          // Look up the target price set ID
          const targetPriceSetId = priceSetIdMap.get(
            sourcePriceSetId.toString(),
          );

          // If we can't find the target price set, skip this price set modifier
          if (!targetPriceSetId) {
            this.logger.warn(
              `Could not find target price set for source ID ${sourcePriceSetId}`,
            );
            skipped++;
            continue;
          }

          // Look up the target modifier ID or group ID
          let targetModifierId =
            modifierIdMap.get(sourceModifierId.toString()) || null;
          let targetGroupId: string | null = null;

          // If we can't find the target modifier, check if it's a group
          if (!targetModifierId) {
            const groupId = groupIdMap.get(sourceModifierId.toString());
            if (groupId) {
              targetGroupId = groupId;
            } else {
              // If we can't find the target group either, skip this price set modifier
              this.logger.warn(
                `Could not find target modifier or group for source ID ${sourceModifierId}`,
              );
              skipped++;
              continue;
            }
          }

          // Create the target price set modifier
          const targetPriceSetModifier = {
            id: this.dataConversionUtil.generateUuid(),
            priceSetId: targetPriceSetId,
            modifierId: targetModifierId,
            modifierGroupId: targetGroupId,
            metadata: {
              sourceId: sourcePriceSetModifier.ID,
              sourcePriceSetId: sourcePriceSetId,
              sourceModifierId: sourceModifierId,
              migrationDate: new Date().toISOString(),
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // Save the price set modifier
          await priceSetModifierRepository.save(targetPriceSetModifier);

          migrated++;
          this.logger.debug(
            `Successfully migrated price set modifier ${sourcePriceSetModifier.ID}`,
          );
        } catch (error) {
          errors++;
          this.logger.error(
            `Error migrating price set modifier ${sourcePriceSetModifier.ID}: ${error.message}`,
          );
        }
      }

      // Return the migration results
      return {
        success: true,
        message: `Migration completed with ${migrated} price set modifiers migrated, ${skipped} skipped, and ${errors} errors`,
        totalProcessed,
        migrated,
        skipped,
        errors,
      };
    } catch (error) {
      this.logger.error(
        `Error migrating price set modifiers: ${error.message}`,
      );
      return {
        success: false,
        message: `Error migrating price set modifiers: ${error.message}`,
        totalProcessed: 0,
        migrated: 0,
        skipped: 0,
        errors: 1,
      };
    }
  }
}
