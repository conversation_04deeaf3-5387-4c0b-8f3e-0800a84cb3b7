/**
 * Price set migration handler
 */
import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MigrationHandler, MigrationResult } from '../types/migration.types';
import { DataConversionUtil } from '../utils/data-conversion.util';
import { PriceSetEntity } from '../../../business/pricing/price-sets/infrastructure/entities/price-set.entity';
import { ScheduleEntity } from '../../../business/pricing/price-sets/infrastructure/entities/schedule.entity';
import {
  ConfigurationType,
  PriceSetPaymentOption,
} from '../../../business/pricing/price-sets/domain/price-set.types';

@Injectable()
export class PriceSetMigrationHandler implements MigrationHandler {
  private readonly logger = new Logger(PriceSetMigrationHandler.name);
  private readonly dataConversionUtil = new DataConversionUtil();

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Migrate price sets from source database to target database
   * @param sqliteDataSource DataSource connected to the SQLite database
   * @param tenantId ID of the tenant to associate the migrated data with
   * @returns Migration result
   */
  public async migrate(
    sqliteDataSource: DataSource,
    tenantId: string,
  ): Promise<MigrationResult> {
    try {
      // Track migration statistics
      let totalProcessed = 0;
      let migrated = 0;
      const skipped = 0;
      let errors = 0;

      // Log start of migration
      this.logger.log(
        `Starting migration of price sets for tenant ${tenantId}`,
      );

      // Get repositories for price sets and schedules
      const priceSetRepository = this.dataSource.getRepository(PriceSetEntity);
      const scheduleRepository = this.dataSource.getRepository(ScheduleEntity);

      // Get all price sets from source database
      const sourcePriceSets = await sqliteDataSource.query(
        `SELECT * FROM PriceSets WHERE Archived = 0 OR Archived IS NULL;`,
      );

      // If no price sets found, return early
      if (!sourcePriceSets || sourcePriceSets.length === 0) {
        return {
          success: true,
          message: 'No price sets found to migrate',
          totalProcessed: 0,
          migrated: 0,
          skipped: 0,
          errors: 0,
        };
      }

      // Log number of price sets found
      this.logger.log(`Found ${sourcePriceSets.length} price sets to migrate`);

      // Process each price set
      for (const sourcePriceSet of sourcePriceSets) {
        totalProcessed++;

        try {
          console.log('woho', sourcePriceSet.Schedule.toString('utf-8'));
          // Parse schedule data if available
          const { availabilityType, offsetType, offsetData } =
            this.dataConversionUtil.parseSchedule(sourcePriceSet.Schedule);
          // Convert Buffer ID to string if needed
          let id = sourcePriceSet.ID;
          if (id && typeof id === 'object' && Buffer.isBuffer(id)) {
            // Convert Buffer to UUID string format
            id = this.dataConversionUtil.bufferToUuid(id);
            this.logger.debug(`Converted Buffer ID to UUID: ${id}`);
          } else if (
            id &&
            typeof id === 'object' &&
            id.type === 'Buffer' &&
            Array.isArray(id.data)
          ) {
            // Handle serialized Buffer object
            id = this.dataConversionUtil.bufferDataToUuid(id.data);
            this.logger.debug(`Converted serialized Buffer ID to UUID: ${id}`);
          }

          // Create the target price set object
          const targetPriceSet = {
            id: id || this.dataConversionUtil.generateUuid(),
            tenantId,
            name: sourcePriceSet.Name || 'Unnamed Price Set',
            description: sourcePriceSet.Description || '',
            configurationType: ConfigurationType.None,
            paymentOption: PriceSetPaymentOption.None,
            isActive: !sourcePriceSet.Archived,
            metadata: {
              sourceId: sourcePriceSet.ID,
              sourceName: sourcePriceSet.Name,
              sourceDescription: sourcePriceSet.Description,
              sourceArchived: sourcePriceSet.Archived,
              sourceSchedule: sourcePriceSet.Schedule,
              migrationDate: new Date().toISOString(),
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // Save the price set
          const savedPriceSet = await priceSetRepository.save(targetPriceSet);

          // Create schedule if available
          if (offsetData) {
            try {
              const schedule = {
                id: this.dataConversionUtil.generateUuid(),
                priceSetId: savedPriceSet.id,
                availabilityType,
                offsetType,
                offsetValue: offsetData.OffsetValue || 0,
                daysOfWeek: offsetData.DaysOfWeek || null,
                startTime: offsetData.StartTime || null,
                endTime: offsetData.EndTime || null,
                createdAt: new Date(),
                updatedAt: new Date(),
              };

              await scheduleRepository.save(schedule);
            } catch (scheduleError) {
              // Log the error but continue with the migration - the price set is still valid
              this.logger.error(
                `Error creating schedule for price set ${savedPriceSet.id}: ${scheduleError.message}`,
              );
            }
          }

          migrated++;
          this.logger.debug(
            `Successfully migrated price set ${sourcePriceSet.ID}`,
          );
        } catch (error) {
          errors++;
          this.logger.error(
            `Error migrating price set ${sourcePriceSet.ID}: ${error.message}`,
          );
        }
      }

      // Return the migration results
      return {
        success: true,
        message: `Migration completed with ${migrated} price sets migrated, ${skipped} skipped, and ${errors} errors`,
        totalProcessed,
        migrated,
        skipped,
        errors,
      };
    } catch (error) {
      this.logger.error(`Error migrating price sets: ${error.message}`);
      return {
        success: false,
        message: `Error migrating price sets: ${error.message}`,
        totalProcessed: 0,
        migrated: 0,
        skipped: 0,
        errors: 1,
      };
    }
  }
}
