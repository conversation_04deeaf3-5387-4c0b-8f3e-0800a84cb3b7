/**
 * Modifier migration handler
 */
import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { Migration<PERSON>and<PERSON>, MigrationResult } from '../types/migration.types';
import { DataConversionUtil } from '../utils/data-conversion.util';
import { PriceModifierEntity } from '../../../business/pricing/price-modifiers/infrastructure/entities/price-modifier.entity';
import { TieredRange } from '../../../business/pricing/price-modifiers/domain/price-modifier.types';

@Injectable()
export class ModifierMigrationHandler implements MigrationHandler {
  private readonly logger = new Logger(ModifierMigrationHandler.name);
  private readonly dataConversionUtil = new DataConversionUtil();

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Migrate price modifiers from source database to target database
   * @param sqliteDataSource DataSource connected to the SQLite database
   * @param tenantId ID of the tenant to associate the migrated data with
   * @returns Migration result
   */
  public async migrate(
    sqliteDataSource: DataSource,
    tenantId: string,
  ): Promise<MigrationResult> {
    try {
      // Track migration statistics
      let totalProcessed = 0;
      let migrated = 0;
      let skipped = 0;
      let errors = 0;

      // Log start of migration
      this.logger.log(
        `Starting migration of price modifiers for tenant ${tenantId}`,
      );

      // Get all modifiers from source database that aren't archived and aren't group types
      const sourceModifiers = await sqliteDataSource.query(
        `SELECT * FROM Modifiers WHERE
         (Archived = 0 OR Archived IS NULL) AND
         (IsGroup = 0 OR IsGroup IS NULL) AND
         (Type != 100 AND Type != 102);`,
      );

      // If no modifiers found, return early
      if (!sourceModifiers || sourceModifiers.length === 0) {
        return {
          success: true,
          message: 'No modifiers found to migrate',
          totalProcessed: 0,
          migrated: 0,
          skipped: 0,
          errors: 0,
        };
      }

      // Log number of modifiers found
      this.logger.log(`Found ${sourceModifiers.length} modifiers to migrate`);

      // Get repository for price modifiers
      const priceModifierRepository =
        this.dataSource.getRepository(PriceModifierEntity);

      // Process each modifier
      for (const sourceModifier of sourceModifiers) {
        totalProcessed++;

        try {
          // Convert Buffer ID to string if needed
          let id = sourceModifier.ID;
          if (id && typeof id === 'object' && Buffer.isBuffer(id)) {
            // Convert Buffer to UUID string format
            id = this.dataConversionUtil.bufferToUuid(id);
            this.logger.debug(`Converted Buffer ID to UUID: ${id}`);
          } else if (
            id &&
            typeof id === 'object' &&
            id.type === 'Buffer' &&
            Array.isArray(id.data)
          ) {
            // Handle serialized Buffer object
            id = this.dataConversionUtil.bufferDataToUuid(id.data);
            this.logger.debug(`Converted serialized Buffer ID to UUID: ${id}`);
          }

          // Parse tiered ranges if available
          let tieredRanges: TieredRange[] = [];
          if (sourceModifier.TieredRanges) {
            try {
              const parsedRanges = JSON.parse(sourceModifier.TieredRanges);
              if (Array.isArray(parsedRanges)) {
                tieredRanges = parsedRanges.map((range) => ({
                  fromValue: range.From || 0,
                  fromOperator: range.FromOperator || 0,
                  toValue: range.To || 0,
                  toOperator: range.ToOperator || 0,
                  value: range.Value || 0,
                }));
              }
            } catch (error) {
              this.logger.error(
                `Error parsing tiered ranges for modifier ${sourceModifier.ID}: ${error.message}`,
              );
            }
          }

          // Create metadata object
          const metadata = {
            sourceId: sourceModifier.ID,
            sourceName: sourceModifier.Name,
            sourceDescription: sourceModifier.Description,
            sourceType: sourceModifier.Type,
            sourceCategory: sourceModifier.Category,
            sourceArchived: sourceModifier.Archived,
            migrationDate: new Date().toISOString(),
          };

          // Create the target modifier object
          const targetModifier = {
            id: id || this.dataConversionUtil.generateUuid(),
            tenantId,
            name: sourceModifier.Name || 'Unnamed Modifier',
            calculationType: this.dataConversionUtil.mapModifierType(
              sourceModifier.Type,
            ),
            fieldName: this.dataConversionUtil.mapCategoryToFieldName(
              sourceModifier.Category,
            ),
            applicableRangeMin: sourceModifier.Threshold || null,
            applicableRangeMax: sourceModifier.RangeUpper || null,
            calculationBase: sourceModifier.CalculatedThreshold || null,
            increment: sourceModifier.IncrementalStep || null,
            amount: sourceModifier.Value || 0,
            tieredRanges: tieredRanges,
            tieredDefaultValue: 0, // Default value, adjust if source data has this
            isActive: !sourceModifier.Archived,
            metadata: metadata,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // Save the modifier
          await priceModifierRepository.save(targetModifier);

          migrated++;
          this.logger.debug(
            `Successfully migrated modifier ${sourceModifier.ID}`,
          );
        } catch (error) {
          errors++;
          this.logger.error(
            `Error migrating modifier ${sourceModifier.ID}: ${error.message}`,
          );
        }
      }

      // Return the migration results
      return {
        success: true,
        message: `Migration completed with ${migrated} modifiers migrated, ${skipped} skipped, and ${errors} errors`,
        totalProcessed,
        migrated,
        skipped,
        errors,
      };
    } catch (error) {
      this.logger.error(`Error migrating modifiers: ${error.message}`);
      return {
        success: false,
        message: `Error migrating modifiers: ${error.message}`,
        totalProcessed: 0,
        migrated: 0,
        skipped: 0,
        errors: 1,
      };
    }
  }
}
