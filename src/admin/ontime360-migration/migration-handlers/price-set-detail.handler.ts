/**
 * Price set detail migration handler
 */
import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MigrationHandler, MigrationResult } from '../types/migration.types';
import { DataConversionUtil } from '../utils/data-conversion.util';
import { ZoneTableValueEntity } from '../../../business/zone/zone-tables/infrastructure/entities/zone-table-value.entity';

@Injectable()
export class PriceSetDetailMigrationHandler implements MigrationHandler {
  private readonly logger = new Logger(PriceSetDetailMigrationHandler.name);
  private readonly dataConversionUtil = new DataConversionUtil();

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Migrate price set details (zone prices) from source database to target database
   * @param sqliteDataSource DataSource connected to the SQLite database
   * @param tenantId ID of the tenant to associate the migrated data with
   * @returns Migration result
   */
  public async migrate(
    sqliteDataSource: DataSource,
    tenantId: string,
  ): Promise<MigrationResult> {
    try {
      // Track migration statistics
      let totalProcessed = 0;
      let migrated = 0;
      let skipped = 0;
      let errors = 0;

      // Log start of migration
      this.logger.log(
        `Starting migration of price set details for tenant ${tenantId}`,
      );

      // Get repository for zone table values
      const zoneTableValueRepository =
        this.dataSource.getRepository(ZoneTableValueEntity);

      // Get all price set details from source database
      const sourcePriceSetDetails = await sqliteDataSource.query(
        `SELECT * FROM PriceSetDetails;`,
      );

      // If no price set details found, return early
      if (!sourcePriceSetDetails || sourcePriceSetDetails.length === 0) {
        return {
          success: true,
          message: 'No price set details found to migrate',
          totalProcessed: 0,
          migrated: 0,
          skipped: 0,
          errors: 0,
        };
      }

      // Log number of price set details found
      this.logger.log(
        `Found ${sourcePriceSetDetails.length} price set details to migrate`,
      );

      // Get all price sets to map IDs
      const priceSets = await this.dataSource.query(
        `SELECT id, metadata FROM price_set WHERE tenant_id = $1;`,
        [tenantId],
      );

      // Create a map of source price set IDs to target price set IDs
      const priceSetIdMap = new Map<string, string>();
      for (const priceSet of priceSets) {
        try {
          const metadata = JSON.parse(priceSet.metadata);
          if (metadata && metadata.sourceId) {
            let sourceId = metadata.sourceId;
            if (typeof sourceId === 'object' && sourceId.type === 'Buffer') {
              sourceId = this.dataConversionUtil.bufferDataToUuid(
                sourceId.data,
              );
            }
            priceSetIdMap.set(sourceId.toString(), priceSet.id);
          }
        } catch (error) {
          this.logger.error(
            `Error parsing price set metadata: ${error.message}`,
          );
        }
      }

      // Get all zone tables for this tenant
      const zoneTables = await this.dataSource.query(
        `SELECT id, name FROM zone_table WHERE tenant_id = $1;`,
        [tenantId],
      );

      // If no zone tables found, we can't migrate price set details
      if (!zoneTables || zoneTables.length === 0) {
        return {
          success: false,
          message:
            'No zone tables found for this tenant. Create zone tables first.',
          totalProcessed: 0,
          migrated: 0,
          skipped: 0,
          errors: 0,
        };
      }

      // Use the first zone table as the default
      const defaultZoneTableId = zoneTables[0].id;

      // Process each price set detail
      for (const sourcePriceSetDetail of sourcePriceSetDetails) {
        totalProcessed++;

        try {
          // Get the source price set ID
          let sourcePriceSetId = sourcePriceSetDetail.PriceSetID;
          if (
            typeof sourcePriceSetId === 'object' &&
            sourcePriceSetId.type === 'Buffer'
          ) {
            sourcePriceSetId = this.dataConversionUtil.bufferDataToUuid(
              sourcePriceSetId.data,
            );
          } else if (Buffer.isBuffer(sourcePriceSetId)) {
            sourcePriceSetId =
              this.dataConversionUtil.bufferToUuid(sourcePriceSetId);
          }

          // Look up the target price set ID
          const targetPriceSetId = priceSetIdMap.get(
            sourcePriceSetId.toString(),
          );

          // If we can't find the target price set, skip this detail
          if (!targetPriceSetId) {
            this.logger.warn(
              `Could not find target price set for source ID ${sourcePriceSetId}`,
            );
            skipped++;
            continue;
          }

          // Create the target zone table value
          const targetZoneTableValue = {
            id: this.dataConversionUtil.generateUuid(),
            zoneTableId: defaultZoneTableId,
            fromZone: sourcePriceSetDetail.FromZone || 'A',
            toZone: sourcePriceSetDetail.ToZone || 'A',
            value: sourcePriceSetDetail.Price || 0,
            priceSetId: targetPriceSetId,
            metadata: {
              sourceId: sourcePriceSetDetail.ID,
              sourcePriceSetId: sourcePriceSetId,
              sourceFromZone: sourcePriceSetDetail.FromZone,
              sourceToZone: sourcePriceSetDetail.ToZone,
              sourcePrice: sourcePriceSetDetail.Price,
              migrationDate: new Date().toISOString(),
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // Save the zone table value
          await zoneTableValueRepository.save(targetZoneTableValue);

          migrated++;
          this.logger.debug(
            `Successfully migrated price set detail ${sourcePriceSetDetail.ID}`,
          );
        } catch (error) {
          errors++;
          this.logger.error(
            `Error migrating price set detail ${sourcePriceSetDetail.ID}: ${error.message}`,
          );
        }
      }

      // Return the migration results
      return {
        success: true,
        message: `Migration completed with ${migrated} price set details migrated, ${skipped} skipped, and ${errors} errors`,
        totalProcessed,
        migrated,
        skipped,
        errors,
      };
    } catch (error) {
      this.logger.error(`Error migrating price set details: ${error.message}`);
      return {
        success: false,
        message: `Error migrating price set details: ${error.message}`,
        totalProcessed: 0,
        migrated: 0,
        skipped: 0,
        errors: 1,
      };
    }
  }
}
