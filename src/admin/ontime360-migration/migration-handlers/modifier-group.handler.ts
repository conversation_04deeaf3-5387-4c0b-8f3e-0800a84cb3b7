/**
 * Modifier group migration handler
 */
import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MigrationHand<PERSON>, MigrationResult } from '../types/migration.types';
import { DataConversionUtil } from '../utils/data-conversion.util';
import { PriceModifierGroupEntity } from '../../../business/pricing/price-modifiers/infrastructure/entities/price-modifier-group.entity';
import { PriceModifierGroupMemberEntity } from '../../../business/pricing/price-modifiers/infrastructure/entities/price-modifier-group-member.entity';

@Injectable()
export class ModifierGroupMigrationHandler implements MigrationHandler {
  private readonly logger = new Logger(ModifierGroupMigrationHandler.name);
  private readonly dataConversionUtil = new DataConversionUtil();

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Migrate modifier groups from source database to target database
   * @param sqliteDataSource DataSource connected to the SQLite database
   * @param tenantId ID of the tenant to associate the migrated data with
   * @returns Migration result
   */
  public async migrate(
    sqliteDataSource: DataSource,
    tenantId: string,
  ): Promise<MigrationResult> {
    try {
      // Track migration statistics
      let totalProcessed = 0;
      let migrated = 0;
      let skipped = 0;
      let errors = 0;

      // Log start of migration
      this.logger.log(
        `Starting migration of modifier groups for tenant ${tenantId}`,
      );

      // Get all modifier groups from source database
      const sourceGroups = await sqliteDataSource.query(
        `SELECT * FROM Modifiers WHERE 
         (Archived = 0 OR Archived IS NULL) AND 
         IsGroup = 1;`,
      );

      // If no groups found, return early
      if (!sourceGroups || sourceGroups.length === 0) {
        return {
          success: true,
          message: 'No modifier groups found to migrate',
          totalProcessed: 0,
          migrated: 0,
          skipped: 0,
          errors: 0,
        };
      }

      // Log number of groups found
      this.logger.log(
        `Found ${sourceGroups.length} modifier groups to migrate`,
      );

      // Get repositories
      const groupRepository = this.dataSource.getRepository(
        PriceModifierGroupEntity,
      );
      const groupMemberRepository = this.dataSource.getRepository(
        PriceModifierGroupMemberEntity,
      );

      // Get all modifiers to map IDs
      const modifiers = await this.dataSource.query(
        `SELECT id, metadata FROM price_modifier WHERE tenant_id = $1;`,
        [tenantId],
      );

      // Create a map of source modifier IDs to target modifier IDs
      const modifierIdMap = new Map<string, string>();
      for (const modifier of modifiers) {
        try {
          const metadata = JSON.parse(modifier.metadata);
          if (metadata && metadata.sourceId) {
            let sourceId = metadata.sourceId;
            if (typeof sourceId === 'object' && sourceId.type === 'Buffer') {
              sourceId = this.dataConversionUtil.bufferDataToUuid(
                sourceId.data,
              );
            }
            modifierIdMap.set(sourceId.toString(), modifier.id);
          }
        } catch (error) {
          this.logger.error(
            `Error parsing modifier metadata: ${error.message}`,
          );
        }
      }

      // Process each group
      for (const sourceGroup of sourceGroups) {
        totalProcessed++;

        try {
          // Convert Buffer ID to string if needed
          let groupId = sourceGroup.ID;
          if (
            groupId &&
            typeof groupId === 'object' &&
            Buffer.isBuffer(groupId)
          ) {
            // Convert Buffer to UUID string format
            groupId = this.dataConversionUtil.bufferToUuid(groupId);
            this.logger.debug(`Converted Buffer ID to UUID: ${groupId}`);
          } else if (
            groupId &&
            typeof groupId === 'object' &&
            groupId.type === 'Buffer' &&
            Array.isArray(groupId.data)
          ) {
            // Handle serialized Buffer object
            groupId = this.dataConversionUtil.bufferDataToUuid(groupId.data);
            this.logger.debug(
              `Converted serialized Buffer ID to UUID: ${groupId}`,
            );
          }

          // Create the target modifier group object
          const targetGroup = {
            id: groupId || this.dataConversionUtil.generateUuid(),
            tenantId,
            name: sourceGroup.Name || 'Unnamed Group',
            description: sourceGroup.Description || '',
            behavior: this.dataConversionUtil.mapGroupBehavior(
              sourceGroup.ControlType || 0,
            ),
            metadata: {
              sourceId: sourceGroup.ID,
              sourceName: sourceGroup.Name,
              sourceDescription: sourceGroup.Description,
              sourceControlType: sourceGroup.ControlType,
              sourceArchived: sourceGroup.Archived,
              migrationDate: new Date().toISOString(),
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // Save the group
          const savedGroup = await groupRepository.save(targetGroup);

          // Now get all members of this group from ModifierGroups table
          const groupMembers = await sqliteDataSource.query(
            `SELECT * FROM ModifierGroups WHERE ModifierID = $1 AND (Archived = 0 OR Archived IS NULL);`,
            [sourceGroup.ID],
          );

          // Process each group member
          if (groupMembers && groupMembers.length > 0) {
            for (const member of groupMembers) {
              try {
                // Get the member modifier ID
                let memberModifierId = member.ChildModifierID;
                if (
                  typeof memberModifierId === 'object' &&
                  memberModifierId.type === 'Buffer'
                ) {
                  memberModifierId = this.dataConversionUtil.bufferDataToUuid(
                    memberModifierId.data,
                  );
                } else if (Buffer.isBuffer(memberModifierId)) {
                  memberModifierId =
                    this.dataConversionUtil.bufferToUuid(memberModifierId);
                }

                // Look up the target modifier ID
                const targetModifierId = modifierIdMap.get(
                  memberModifierId.toString(),
                );

                // If we can't find the target modifier, skip this member
                if (!targetModifierId) {
                  this.logger.warn(
                    `Could not find target modifier for source ID ${memberModifierId}`,
                  );
                  continue;
                }

                // Create the group member
                const groupMember = {
                  id: this.dataConversionUtil.generateUuid(),
                  groupId: savedGroup.id,
                  modifierId: targetModifierId,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                };

                // Save the group member
                await groupMemberRepository.save(groupMember);
              } catch (memberError) {
                this.logger.error(
                  `Error migrating group member: ${memberError.message}`,
                );
              }
            }
          }

          migrated++;
          this.logger.debug(
            `Successfully migrated modifier group ${sourceGroup.ID}`,
          );
        } catch (error) {
          errors++;
          this.logger.error(
            `Error migrating modifier group ${sourceGroup.ID}: ${error.message}`,
          );
        }
      }

      // Return the migration results
      return {
        success: true,
        message: `Migration completed with ${migrated} modifier groups migrated, ${skipped} skipped, and ${errors} errors`,
        totalProcessed,
        migrated,
        skipped,
        errors,
      };
    } catch (error) {
      this.logger.error(`Error migrating modifier groups: ${error.message}`);
      return {
        success: false,
        message: `Error migrating modifier groups: ${error.message}`,
        totalProcessed: 0,
        migrated: 0,
        skipped: 0,
        errors: 1,
      };
    }
  }
}
