/**
 * Migration types and interfaces
 */
import { DataSource } from 'typeorm';

/**
 * Migration type enum
 */
export enum MigrationType {
  PRICE_SETS = 'priceSets',
  PRICE_SET_DETAILS = 'priceSetDetails',
  MODIFIERS = 'modifiers',
  MODIFIER_GROUPS = 'modifierGroups',
  PRICE_SET_MODIFIERS = 'priceSetModifiers',
}

/**
 * Migration result interface
 */
export interface MigrationResult {
  success: boolean;
  message: string;
  totalProcessed: number;
  migrated: number;
  skipped: number;
  errors: number;
}

/**
 * Migration file result interface
 */
export interface MigrationFileResult {
  success: boolean;
  message: string;
  filePath?: string;
}

/**
 * Migration status interface
 */
export interface MigrationStatus {
  success: boolean;
  message: string;
  fileExists: boolean;
  fileName?: string;
  fileSize?: number;
}

/**
 * Migration run result interface
 */
export interface MigrationRunResult {
  success: boolean;
  message: string;
  results: {
    [MigrationType.PRICE_SETS]: MigrationResult;
    [MigrationType.PRICE_SET_DETAILS]: MigrationResult;
    [MigrationType.MODIFIERS]: MigrationResult;
    [MigrationType.MODIFIER_GROUPS]: MigrationResult;
    [MigrationType.PRICE_SET_MODIFIERS]: MigrationResult;
  };
}

/**
 * Database analysis result interface
 */
export interface DatabaseAnalysisResult {
  success: boolean;
  message: string;
  tables?: {
    name: string;
    rowCount: number;
    columns: {
      name: string;
      type: string;
    }[];
  }[];
}

/**
 * Migration handler interface
 */
export interface MigrationHandler {
  /**
   * Migrate data from source database to target database
   * @param sqliteDataSource DataSource connected to the SQLite database
   * @param tenantId ID of the tenant to associate the migrated data with
   * @returns Migration result
   */
  migrate(
    sqliteDataSource: DataSource,
    tenantId: string,
  ): Promise<MigrationResult>;
}
