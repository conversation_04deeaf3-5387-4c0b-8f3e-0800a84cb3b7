/**
 * Migration service
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
import * as path from 'path';
import {
  DatabaseAnalysisResult,
  MigrationFileResult,
  MigrationResult,
  MigrationRunResult,
  MigrationType,
  MigrationStatus,
} from '../types/migration.types';
import { FileManagementUtil } from '../utils/file-management.util';
import { SqliteConnectionUtil } from '../utils/sqlite-connection.util';
import {
  PriceSetMigrationHandler,
  PriceSetDetailMigrationHandler,
  ModifierMigrationHandler,
  ModifierGroupMigrationHandler,
  PriceSetModifierMigrationHandler,
} from '../migration-handlers';

@Injectable()
export class MigrationService implements OnModuleInit {
  private readonly logger = new Logger(MigrationService.name);
  private readonly fileManagementUtil: FileManagementUtil;
  private readonly sqliteConnectionUtil: SqliteConnectionUtil;

  // Migration handlers
  private readonly migrationHandlers: Map<MigrationType, any> = new Map();

  constructor(private readonly dataSource: DataSource) {
    // Initialize utilities
    this.fileManagementUtil = new FileManagementUtil(
      path.join(process.cwd(), 'migration_of_onetime'),
    );
    this.sqliteConnectionUtil = new SqliteConnectionUtil();
  }

  /**
   * Initialize migration handlers on module initialization
   */
  async onModuleInit() {
    // Initialize migration handlers
    //  this.initializeMigrationHandlers();

    // Check for migration file and run automatic migration if found
    const result = await this.readMigrationFile();
    if (result.success && result.filePath) {
      console.log('\n\n===== MIGRATION FILE FOUND =====');
      console.log(`File: ${result.filePath}`);
      console.log('Reading SQLite database structure...');
      await this.readSqliteDatabase(result.filePath);

      // Run migration for specific tenant
      const tenantId = 'cf1be353-2e5c-4098-93ae-03be900a865a';
      console.log(
        `\n\n===== STARTING AUTOMATIC MIGRATION FOR TENANT ${tenantId} =====`,
      );

      // Run specific migrations by default
      const migrationResult = await this.runMigration(
        result.filePath,
        tenantId,
        [MigrationType.PRICE_SETS],
      );

      // Generate and log migration summary
      const summary = this.generateMigrationSummary(migrationResult);
      console.log(summary);

      // Add a message with the API endpoint for manual migration
      console.log(
        '\nTo run a manual migration for a different tenant, use the API endpoint:',
      );
      console.log('POST /admin/migration/run');
      console.log(
        'With request body: { "tenantId": "your-tenant-id", "migrateOnly": ["priceSets", "modifiers"] }',
      );
      console.log(
        'Available migration types: priceSets, priceSetDetails, modifiers, modifierGroups, priceSetModifiers',
      );
    } else {
      console.log('\n\n===== NO MIGRATION FILE FOUND =====');
      console.log(
        'Place a SQLite database file in the migration_of_onetime folder to analyze it.',
      );
    }
  }

  /**
   * Initialize migration handlers
   */
  private initializeMigrationHandlers(): void {
    // Create and register migration handlers
    this.migrationHandlers.set(
      MigrationType.PRICE_SETS,
      new PriceSetMigrationHandler(this.dataSource),
    );
    this.migrationHandlers.set(
      MigrationType.PRICE_SET_DETAILS,
      new PriceSetDetailMigrationHandler(this.dataSource),
    );
    this.migrationHandlers.set(
      MigrationType.MODIFIERS,
      new ModifierMigrationHandler(this.dataSource),
    );
    this.migrationHandlers.set(
      MigrationType.MODIFIER_GROUPS,
      new ModifierGroupMigrationHandler(this.dataSource),
    );
    this.migrationHandlers.set(
      MigrationType.PRICE_SET_MODIFIERS,
      new PriceSetModifierMigrationHandler(this.dataSource),
    );
  }

  /**
   * Read migration file from the migration folder
   * @returns Migration file result
   */
  async readMigrationFile(): Promise<MigrationFileResult> {
    return this.fileManagementUtil.readMigrationFile();
  }

  /**
   * Read SQLite database structure
   * @param filePath Path to the SQLite database file
   * @returns Database analysis result
   */
  async readSqliteDatabase(filePath: string): Promise<DatabaseAnalysisResult> {
    return this.sqliteConnectionUtil.readDatabaseStructure(filePath);
  }

  /**
   * Generate a summary of the migration results
   * @param migrationResult Migration run result
   * @returns Summary string
   */
  generateMigrationSummary(migrationResult: MigrationRunResult): string {
    if (!migrationResult.success) {
      return `Migration failed: ${migrationResult.message}`;
    }

    let summary = '\n===== MIGRATION SUMMARY =====\n';

    // Add a section for each migration type
    for (const [type, result] of Object.entries(migrationResult.results)) {
      if (result.totalProcessed > 0) {
        summary += `\n${type}:\n`;
        summary += `  Total processed: ${result.totalProcessed}\n`;
        summary += `  Migrated: ${result.migrated}\n`;
        summary += `  Skipped: ${result.skipped}\n`;
        summary += `  Errors: ${result.errors}\n`;
      }
    }

    // Add overall status
    summary +=
      '\nOverall status: ' + (migrationResult.success ? 'SUCCESS' : 'FAILED');
    summary += '\nMessage: ' + migrationResult.message;

    return summary;
  }

  /**
   * Run the migration process for selected tables
   * @param filePath Path to the SQLite database file
   * @param tenantId ID of the tenant to associate the migrated data with
   * @param migrateOnly Optional array of migration types to run. If not provided, all migrations will run.
   * @returns Migration run result
   */
  async runMigration(
    filePath: string,
    tenantId: string,
    migrateOnly?: string[],
  ): Promise<MigrationRunResult> {
    try {
      // Define empty result object
      const emptyResult: MigrationResult = {
        totalProcessed: 0,
        migrated: 0,
        skipped: 0,
        errors: 0,
        success: false,
        message: '',
      };

      // Initialize results with empty values
      const results = {
        [MigrationType.PRICE_SETS]: { ...emptyResult },
        [MigrationType.PRICE_SET_DETAILS]: { ...emptyResult },
        [MigrationType.MODIFIERS]: { ...emptyResult },
        [MigrationType.MODIFIER_GROUPS]: { ...emptyResult },
        [MigrationType.PRICE_SET_MODIFIERS]: { ...emptyResult },
      };

      // Check which migrations to run
      const shouldRunMigration = (type: string): boolean => {
        if (!migrateOnly || migrateOnly.length === 0) {
          return true; // Run all if not specified
        }
        return migrateOnly.includes(type);
      };

      this.logger.log(`Starting migration process for tenant ${tenantId}`);
      if (migrateOnly && migrateOnly.length > 0) {
        this.logger.log(
          `Running only specified migrations: ${migrateOnly.join(', ')}`,
        );
      } else {
        this.logger.log('Running all migrations');
      }

      // Create a new DataSource for the SQLite file
      const sqliteDataSource =
        await this.sqliteConnectionUtil.createDataSource(filePath);

      try {
        // Run migrations in the correct order
        const migrationOrder = [
          MigrationType.PRICE_SETS,
          MigrationType.PRICE_SET_DETAILS,
          MigrationType.MODIFIERS,
          MigrationType.MODIFIER_GROUPS,
          MigrationType.PRICE_SET_MODIFIERS,
        ];

        // Process each migration type in order
        for (const migrationType of migrationOrder) {
          if (shouldRunMigration(migrationType)) {
            const handler = this.migrationHandlers.get(migrationType);
            if (handler) {
              const migrationResult = await handler.migrate(
                sqliteDataSource,
                tenantId,
              );

              this.logger.log(migrationResult.message);
              results[migrationType] = migrationResult;
            } else {
              this.logger.warn(
                `No handler found for migration type: ${migrationType}`,
              );
            }
          }
        }

        // Close the connection
        await sqliteDataSource.destroy();

        return {
          success: true,
          message:
            migrateOnly && migrateOnly.length > 0
              ? `Migration of ${migrateOnly.join(', ')} completed successfully`
              : 'Migration completed successfully',
          results,
        };
      } catch (error) {
        // Make sure to close the connection in case of error
        await sqliteDataSource.destroy();
        throw error;
      }
    } catch (error) {
      this.logger.error(`Error running migration: ${error.message}`);
      return {
        success: false,
        message: `Error running migration: ${error.message}`,
        results: {
          [MigrationType.PRICE_SETS]: {
            totalProcessed: 0,
            migrated: 0,
            skipped: 0,
            errors: 0,
            success: false,
            message: '',
          },
          [MigrationType.PRICE_SET_DETAILS]: {
            totalProcessed: 0,
            migrated: 0,
            skipped: 0,
            errors: 0,
            success: false,
            message: '',
          },
          [MigrationType.MODIFIERS]: {
            totalProcessed: 0,
            migrated: 0,
            skipped: 0,
            errors: 0,
            success: false,
            message: '',
          },
          [MigrationType.MODIFIER_GROUPS]: {
            totalProcessed: 0,
            migrated: 0,
            skipped: 0,
            errors: 0,
            success: false,
            message: '',
          },
          [MigrationType.PRICE_SET_MODIFIERS]: {
            totalProcessed: 0,
            migrated: 0,
            skipped: 0,
            errors: 0,
            success: false,
            message: '',
          },
        },
      };
    }
  }

  /**
   * Get migration status
   * @returns Migration status
   */
  async getMigrationStatus(): Promise<MigrationStatus> {
    return this.fileManagementUtil.getMigrationStatus();
  }
}
