import { Controller, Post, Body, Get, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MigrationService } from '../services/migration.service';

/**
 * DTO for migration request
 */
class RunMigrationDto {
  /**
   * ID of the tenant to associate the migrated data with
   */
  tenantId: string;

  /**
   * Optional array of migration types to run. If not provided, all migrations will run.
   * Possible values: 'priceSets', 'priceSetDetails', 'modifiers', 'modifierGroups', 'priceSetModifiers'
   */
  migrateOnly?: string[];
}

@ApiTags('Admin - OnTime 360 Migration')
@Controller('admin/migration')
export class MigrationController {
  constructor(private readonly migrationService: MigrationService) {}

  /**
   * Get the status of the migration file
   */
  @Get('status')
  @ApiOperation({
    summary: 'Check migration file status',
    description:
      'Returns information about the migration file in the migration folder',
  })
  @ApiResponse({ status: HttpStatus.OK, description: 'Migration file status' })
  async getMigrationStatus() {
    return this.migrationService.getMigrationStatus();
  }

  /**
   * Run the migration process
   */
  @Post('run')
  @ApiOperation({
    summary: 'Run migration for a tenant',
    description:
      'Migrates data from the SQLite database file to the target database for the specified tenant ID. ' +
      'You can optionally specify which migrations to run using the migrateOnly parameter.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Migration completed',
    schema: {
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        results: {
          type: 'object',
          properties: {
            priceSets: {
              type: 'object',
              properties: {
                totalProcessed: { type: 'number' },
                migrated: { type: 'number' },
                skipped: { type: 'number' },
                errors: { type: 'number' },
              },
            },
            priceSetDetails: {
              type: 'object',
              properties: {
                totalProcessed: { type: 'number' },
                migrated: { type: 'number' },
                skipped: { type: 'number' },
                errors: { type: 'number' },
              },
            },
            modifiers: {
              type: 'object',
              properties: {
                totalProcessed: { type: 'number' },
                migrated: { type: 'number' },
                skipped: { type: 'number' },
                errors: { type: 'number' },
              },
            },
            modifierGroups: {
              type: 'object',
              properties: {
                totalProcessed: { type: 'number' },
                migrated: { type: 'number' },
                skipped: { type: 'number' },
                errors: { type: 'number' },
              },
            },
            priceSetModifiers: {
              type: 'object',
              properties: {
                totalProcessed: { type: 'number' },
                migrated: { type: 'number' },
                skipped: { type: 'number' },
                errors: { type: 'number' },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Migration file not found or invalid tenant ID',
  })
  async runMigration(@Body() runMigrationDto: RunMigrationDto) {
    const migrationResult = await this.migrationService.readMigrationFile();

    if (!migrationResult.success || !migrationResult.filePath) {
      return {
        success: false,
        message: migrationResult.message || 'Migration file not found',
        results: null,
      };
    }

    return this.migrationService.runMigration(
      migrationResult.filePath,
      runMigrationDto.tenantId,
      runMigrationDto.migrateOnly,
    );
  }

  /**
   * Analyze the migration file
   */
  @Get('analyze')
  @ApiOperation({
    summary: 'Analyze SQLite database file',
    description:
      'Analyzes the SQLite database file and returns the table structure and row counts',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Database analysis completed',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Migration file not found',
  })
  async analyzeMigrationFile() {
    const migrationResult = await this.migrationService.readMigrationFile();

    if (!migrationResult.success || !migrationResult.filePath) {
      return {
        success: false,
        message: migrationResult.message || 'Migration file not found',
        tables: null,
      };
    }

    return this.migrationService.readSqliteDatabase(migrationResult.filePath);
  }
}
