/**
 * Data conversion utility
 */
import { v4 as uuidv4 } from 'uuid';
import {
  ECalculationType,
  EModifierGroupBehavior,
  ERangeFromOperator,
  ERangeToOperator,
  TieredRange,
} from '../../../business/pricing/price-modifiers/domain/price-modifier.types';
import {
  AvailabilityType,
  OffsetType,
} from '../../../business/pricing/price-sets/domain/price-set.types';

/**
 * Data conversion utility class
 */
export class DataConversionUtil {
  /**
   * Generate a new UUID
   * @returns UUID string
   */
  public generateUuid(): string {
    return uuidv4();
  }

  /**
   * Convert Buffer to UUID string
   * @param buffer Buffer containing UUID bytes
   * @returns UUID string
   */
  public bufferToUuid(buffer: Buffer): string {
    if (!buffer || !Buffer.isBuffer(buffer)) {
      return this.generateUuid();
    }

    try {
      // Format the buffer as a UUID string
      const hex = buffer.toString('hex');
      return [
        hex.substring(0, 8),
        hex.substring(8, 12),
        hex.substring(12, 16),
        hex.substring(16, 20),
        hex.substring(20, 32),
      ].join('-');
    } catch (error) {
      console.error('Error converting buffer to UUID:', error);
      return this.generateUuid();
    }
  }

  /**
   * Convert Buffer data array to UUID string
   * @param data Array of numbers representing UUID bytes
   * @returns UUID string
   */
  public bufferDataToUuid(data: number[]): string {
    if (!data || !Array.isArray(data)) {
      return this.generateUuid();
    }

    try {
      // Convert the array to a Buffer and then to a UUID string
      const buffer = Buffer.from(data);
      return this.bufferToUuid(buffer);
    } catch (error) {
      console.error('Error converting buffer data to UUID:', error);
      return this.generateUuid();
    }
  }

  /**
   * Map modifier type from source to target
   * @param sourceType Source modifier type
   * @returns Target calculation type
   */
  public mapModifierType(sourceType: number): ECalculationType {
    switch (sourceType) {
      case 0: // Flat
        return ECalculationType.FlatAmount;
      case 1: // Percentage
        return ECalculationType.FlatPercentage;
      case 2: // Per Mile
        return ECalculationType.FlatOverageAmount; // Closest match
      case 3: // Per Pound
        return ECalculationType.FlatOverageAmount; // Closest match
      case 4: // Per Piece
        return ECalculationType.FlatOverageAmount; // Closest match
      case 5: // Tiered
        return ECalculationType.TieredFixedOverageAmount;
      default:
        return ECalculationType.FlatAmount;
    }
  }

  /**
   * Map category to field name
   * @param category Source category
   * @returns Target field name
   */
  public mapCategoryToFieldName(category: number): string {
    switch (category) {
      case 0: // Distance
        return 'distance';
      case 1: // Weight
        return 'weight';
      case 2: // Pieces
        return 'pieces';
      case 3: // Declared Value
        return 'declaredValue';
      case 4: // COD Amount
        return 'codAmount';
      case 5: // Wait Time
        return 'waitTime';
      default:
        return 'distance';
    }
  }

  /**
   * Map group behavior from source to target
   * @param controlType Source control type
   * @returns Target group behavior
   */
  public mapGroupBehavior(controlType: number): EModifierGroupBehavior {
    switch (controlType) {
      case 0: // Use Sum
        return EModifierGroupBehavior.UseSum;
      case 1: // Use Highest
        return EModifierGroupBehavior.UseHighest;
      case 2: // Use Lowest
        return EModifierGroupBehavior.UseLowest;
      default:
        return EModifierGroupBehavior.UseSum;
    }
  }

  /**
   * Parse schedule data from JSON string
   * @param scheduleJson Schedule JSON string
   * @returns Parsed schedule data
   */
  public parseSchedule(scheduleJson: string): {
    availabilityType: AvailabilityType;
    offsetType: OffsetType | null;
    offsetData: any;
  } {
    try {
      if (!scheduleJson) {
        return {
          availabilityType: AvailabilityType.Always,
          offsetType: null,
          offsetData: null,
        };
      }

      const schedule = JSON.parse(scheduleJson);
      return {
        availabilityType: this.mapAvailabilityType(
          schedule.AvailabilityType || 0,
        ),
        offsetType: this.mapOffsetType(schedule.OffsetType || 0),
        offsetData: schedule,
      };
    } catch (error) {
      console.error('Error parsing schedule:', error);
      return {
        availabilityType: AvailabilityType.Always,
        offsetType: null,
        offsetData: null,
      };
    }
  }

  /**
   * Map availability type from source to target
   * @param sourceType Source availability type
   * @returns Target availability type
   */
  private mapAvailabilityType(sourceType: number): AvailabilityType {
    switch (sourceType) {
      case 0: // Always
        return AvailabilityType.Always;
      case 1: // Custom
        return AvailabilityType.Weekly; // Closest match
      default:
        return AvailabilityType.Always;
    }
  }

  /**
   * Map offset type from source to target
   * @param sourceType Source offset type
   * @returns Target offset type
   */
  private mapOffsetType(sourceType: number): OffsetType | null {
    switch (sourceType) {
      case 0: // None
        return null;
      case 1: // Minutes
        return OffsetType.By;
      case 2: // Hours
        return OffsetType.By;
      case 3: // Days
        return OffsetType.By;
      default:
        return null;
    }
  }
}
