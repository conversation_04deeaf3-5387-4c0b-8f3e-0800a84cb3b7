/**
 * File management utility
 */
import { Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { MigrationFileResult } from '../types/migration.types';

/**
 * File management utility class
 */
export class FileManagementUtil {
  private readonly logger = new Logger(FileManagementUtil.name);
  private readonly migrationFolderPath: string;

  /**
   * Constructor
   * @param folderPath Path to the migration folder
   */
  constructor(folderPath: string) {
    this.migrationFolderPath = folderPath;
    this.ensureMigrationFolderExists();
  }

  /**
   * Ensure migration folder exists
   */
  private ensureMigrationFolderExists(): void {
    if (!fs.existsSync(this.migrationFolderPath)) {
      fs.mkdirSync(this.migrationFolderPath, { recursive: true });
      this.logger.log(
        `Created migration folder at ${this.migrationFolderPath}`,
      );
    }
  }

  /**
   * Read migration file from the migration folder
   * Only one file is allowed in the folder
   * @returns Migration file result
   */
  public async readMigrationFile(): Promise<MigrationFileResult> {
    try {
      // Check if the migration folder exists
      if (!fs.existsSync(this.migrationFolderPath)) {
        return {
          success: false,
          message: `Migration folder not found at ${this.migrationFolderPath}`,
        };
      }

      // Get all files in the migration folder
      const files = fs.readdirSync(this.migrationFolderPath);

      if (files.length === 0) {
        return {
          success: false,
          message: 'No files found in the migration folder',
        };
      }

      if (files.length > 1) {
        return {
          success: false,
          message:
            'Multiple files found in the migration folder. Only one file is allowed.',
        };
      }

      const filePath = path.join(this.migrationFolderPath, files[0]);
      const stats = fs.statSync(filePath);

      if (stats.isDirectory()) {
        return {
          success: false,
          message: `Found a directory instead of a file: ${files[0]}`,
        };
      }

      return {
        success: true,
        message: `Found migration file: ${files[0]}`,
        filePath,
      };
    } catch (error) {
      this.logger.error(`Error reading migration file: ${error.message}`);
      return {
        success: false,
        message: `Error reading migration file: ${error.message}`,
      };
    }
  }

  /**
   * Get migration status
   * @returns Migration status
   */
  public async getMigrationStatus(): Promise<{
    success: boolean;
    message: string;
    fileExists: boolean;
    fileName?: string;
    fileSize?: number;
  }> {
    try {
      // Check if the migration folder exists
      if (!fs.existsSync(this.migrationFolderPath)) {
        return {
          success: true,
          message: `Migration folder not found at ${this.migrationFolderPath}`,
          fileExists: false,
        };
      }

      // Get all files in the migration folder
      const files = fs.readdirSync(this.migrationFolderPath);

      if (files.length === 0) {
        return {
          success: true,
          message: 'No files found in the migration folder',
          fileExists: false,
        };
      }

      if (files.length > 1) {
        return {
          success: true,
          message: 'Multiple files found. Only one file is allowed.',
          fileExists: true,
          fileName: 'Multiple files',
          fileSize: 0,
        };
      }

      const filePath = path.join(this.migrationFolderPath, files[0]);
      const stats = fs.statSync(filePath);

      return {
        success: true,
        message: `Found migration file: ${files[0]}`,
        fileExists: true,
        fileName: files[0],
        fileSize: stats.size,
      };
    } catch (error) {
      this.logger.error(`Error getting migration status: ${error.message}`);
      return {
        success: false,
        message: `Error getting migration status: ${error.message}`,
        fileExists: false,
      };
    }
  }
}
