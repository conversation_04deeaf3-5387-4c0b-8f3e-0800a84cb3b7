/**
 * SQLite connection utility
 */
import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import * as fs from 'fs';

/**
 * SQLite connection utility class
 */
export class SqliteConnectionUtil {
  private readonly logger = new Logger(SqliteConnectionUtil.name);

  /**
   * Create a new SQLite data source
   * @param filePath Path to the SQLite database file
   * @returns DataSource instance
   */
  public async createDataSource(filePath: string): Promise<DataSource> {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`SQLite database file not found at ${filePath}`);
    }

    // Create a new DataSource for the SQLite file
    const sqliteDataSource = new DataSource({
      type: 'sqlite',
      database: filePath,
      synchronize: false,
      logging: false,
    });

    // Initialize the connection
    await sqliteDataSource.initialize();
    this.logger.log('Successfully connected to SQLite database');

    return sqliteDataSource;
  }

  /**
   * Read SQLite database structure
   * @param filePath Path to the SQLite database file
   * @returns Database analysis result
   */
  public async readDatabaseStructure(filePath: string): Promise<{
    success: boolean;
    message: string;
    tables?: Array<{
      name: string;
      rowCount: number;
      columns: Array<{
        name: string;
        type: string;
      }>;
    }>;
  }> {
    try {
      const sqliteDataSource = await this.createDataSource(filePath);

      try {
        // Get all tables in the database
        const tables = await sqliteDataSource.query(
          "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';",
        );

        if (!tables || tables.length === 0) {
          await sqliteDataSource.destroy();
          return {
            success: false,
            message: 'No tables found in the database',
          };
        }

        // Get structure and row count for each table
        const tableStructures: Array<{
          name: string;
          rowCount: number;
          columns: Array<{
            name: string;
            type: string;
          }>;
        }> = [];

        for (const table of tables) {
          const tableName = table.name;

          // Get columns for this table
          const columns = await sqliteDataSource.query(
            `PRAGMA table_info(${tableName});`,
          );

          // Get row count for this table
          const rowCountResult = await sqliteDataSource.query(
            `SELECT COUNT(*) as count FROM ${tableName};`,
          );
          const rowCount = rowCountResult[0].count;

          // Add table structure to the result
          tableStructures.push({
            name: tableName,
            rowCount,
            columns: columns.map((column) => ({
              name: column.name,
              type: column.type,
            })),
          });
        }

        await sqliteDataSource.destroy();
        return {
          success: true,
          message: `Found ${tables.length} tables in the database`,
          tables: tableStructures,
        };
      } catch (error) {
        await sqliteDataSource.destroy();
        throw error;
      }
    } catch (error) {
      this.logger.error(`Error reading SQLite database: ${error.message}`);
      return {
        success: false,
        message: `Error reading SQLite database: ${error.message}`,
      };
    }
  }
}
