import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { OrderAssignmentService } from '@app/business/order/orders/services/order-assignment.service';
import { OrdersService } from '@app/business/order/orders/orders.service';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { UsersService } from '@app/business/user/users/users.service';
import {
  UserType,
  UserStatus,
} from '@app/business/user/users/domain/user.types';
import { DataSource } from 'typeorm';

@ApiTags('Admin - Order Management')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'admin/order-management',
  version: '1',
})
export class OrderAssignmentController {
  constructor(
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly ordersService: OrdersService,
    private readonly usersService: UsersService,
    private readonly dataSource: DataSource,
  ) {}

  @Get('orders/unassigned')
  @ApiOperation({ summary: 'Get all unassigned orders for the tenant' })
  @ApiQuery({
    name: 'pageNumber',
    required: false,
    type: Number,
    description: 'Page number for pagination (default: 1)',
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of unassigned orders retrieved successfully',
  })
  async getUnassignedOrders(
    @CurrentUser() userData: JwtPayload,
    @Query('pageNumber') pageNumber: number = 1,
    @Query('pageSize') pageSize: number = 10,
  ) {
    // Get the tenant ID from the user data
    const user = await this.usersService.findById(userData.sub);
    if (!user || !user.tenantId) {
      throw new Error('User does not have a tenant ID');
    }

    // Use a raw query to get all unassigned orders for the tenant
    const qb = this.dataSource
      .createQueryBuilder()
      .select('orders')
      .from('orders', 'orders')
      .where('orders.tenant_id = :tenantId', { tenantId: user.tenantId })
      .andWhere('orders.assigned_driver_id IS NULL')
      .andWhere('orders.status IN (:...statuses)', {
        statuses: ['Pending', 'Submitted'],
      })
      .andWhere('orders.is_deleted = :isDeleted', { isDeleted: false })
      .orderBy('orders.created_at', 'DESC')
      .skip((pageNumber - 1) * pageSize)
      .take(pageSize);

    // Get the orders and count
    const [orders, total] = await Promise.all([qb.getMany(), qb.getCount()]);

    // Return the paginated result
    return {
      data: orders,
      meta: {
        page: pageNumber,
        limit: pageSize,
        total,
      },
    };
  }

  @Get('drivers/available')
  @ApiOperation({ summary: 'Get all available drivers for the tenant' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of available drivers retrieved successfully',
  })
  async getAvailableDrivers(@CurrentUser() userData: JwtPayload) {
    // Get the tenant ID from the user data
    const user = await this.usersService.findById(userData.sub);
    if (!user || !user.tenantId) {
      throw new Error('User does not have a tenant ID');
    }

    // Use a raw query to get all active drivers for the tenant
    const drivers = await this.dataSource
      .createQueryBuilder()
      .select('users')
      .from('users', 'users')
      .where('users.tenant_id = :tenantId', { tenantId: user.tenantId })
      .andWhere('users.user_type = :userType', { userType: UserType.Driver })
      .andWhere('users.status = :status', { status: UserStatus.Active })
      .andWhere('users.is_deleted = :isDeleted', { isDeleted: false })
      .getMany();
    // Return only the necessary driver information
    return drivers.map((driver: any) => ({
      id: driver.id,
      name: driver.name,
      email: driver.email,
      phoneNumber: driver.phoneNumber,
      status: driver.status,
    }));
  }

  @Post('orders/:id/assign/:driverId')
  @ApiOperation({ summary: 'Assign an order to a driver' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiParam({
    name: 'driverId',
    description: 'Driver ID to assign the order to',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order assigned successfully',
  })
  @HttpCode(HttpStatus.OK)
  async assignOrder(
    @CurrentUser() userData: JwtPayload,
    @Param('id') orderId: string,
    @Param('driverId') driverId: string,
  ) {
    // Get the tenant ID from the user data
    const user = await this.usersService.findById(userData.sub);
    if (!user || !user.tenantId) {
      throw new Error('User does not have a tenant ID');
    }

    // Verify the order exists and belongs to the tenant
    const order = await this.ordersService.findOne(user.tenantId, orderId);
    if (!order) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    // Assign the order to the driver
    const success = await this.orderAssignmentService.assignOrder(
      orderId,
      driverId,
      userData.sub, // Admin user ID as the assigner
      undefined, // No vehicle ID
      'Assigned by admin', // Default reason
    );

    if (!success) {
      throw new Error(
        `Failed to assign order ${orderId} to driver ${driverId}`,
      );
    }

    // Return the updated order
    return this.ordersService.findOne(user.tenantId, orderId);
  }

  @Post('orders/:id/unassign')
  @ApiOperation({ summary: 'Unassign an order from its current driver' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order unassigned successfully',
  })
  @HttpCode(HttpStatus.OK)
  async unassignOrder(
    @CurrentUser() userData: JwtPayload,
    @Param('id') orderId: string,
  ) {
    // Get the tenant ID from the user data
    const user = await this.usersService.findById(userData.sub);
    if (!user || !user.tenantId) {
      throw new Error('User does not have a tenant ID');
    }

    // Verify the order exists and belongs to the tenant
    const order = await this.ordersService.findOne(user.tenantId, orderId);
    if (!order) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    // Unassign the order from the driver
    const success = await this.orderAssignmentService.unassignOrder(
      orderId,
      userData.sub, // Admin user ID as the unassigner
      'Unassigned by admin', // Default reason
    );

    if (!success) {
      throw new Error(`Failed to unassign order ${orderId}`);
    }

    // Return the updated order
    return this.ordersService.findOne(user.tenantId, orderId);
  }
}
