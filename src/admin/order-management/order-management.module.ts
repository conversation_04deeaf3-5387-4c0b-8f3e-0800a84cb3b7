import { Module } from '@nestjs/common';
import { OrderAssignmentController } from './order-assignment.controller';
import { OrdersModule } from '@app/business/order/orders/orders.module';
import { UsersModule } from '@app/business/user/users/users.module';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [OrdersModule, UsersModule, TypeOrmModule.forFeature([])],
  controllers: [OrderAssignmentController],
})
export class OrderManagementModule {}
