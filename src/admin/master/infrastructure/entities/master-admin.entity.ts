import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { MasterAdminStatus } from '../../domain/master-admin.types';

@Entity('master_admins')
export class MasterAdminEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column({ length: 100 })
  name: string;

  @AutoMap()
  @Column()
  @Index({ unique: true })
  email: string;

  @AutoMap()
  @Column({ length: 255 })
  password: string;

  @AutoMap()
  @Column({ name: 'email_verified', default: false })
  emailVerified: boolean;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: MasterAdminStatus,
    default: MasterAdminStatus.Active,
  })
  status: MasterAdminStatus;

  @AutoMap()
  @Column({
    name: 'last_login_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  lastLoginAt?: Date;

  @AutoMap()
  @Column({ name: 'login_count', default: 0 })
  loginCount: number;

  @AutoMap()
  @Column({ name: 'failed_attempts', default: 0 })
  failedAttempts: number;

  @AutoMap()
  @Column({
    name: 'locked_until',
    type: 'timestamp with time zone',
    nullable: true,
  })
  lockedUntil?: Date;

  @AutoMap()
  @Column({ name: 'is_deleted', default: false })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({
    name: 'deleted_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  deletedAt?: Date;

  @AutoMap()
  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;
}
