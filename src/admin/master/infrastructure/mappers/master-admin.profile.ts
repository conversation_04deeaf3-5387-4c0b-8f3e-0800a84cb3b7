import { createMap, forM<PERSON>ber, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateMasterAdminDto } from '../../dto/create-master-admin.dto';
import { MasterAdminEntity } from '../entities/master-admin.entity';
import { MasterAdminDomain } from '../../domain/master-admin';
import { GetMasterAdminDto } from '../../dto/get-master-admin.dto';

@Injectable()
export class MasterAdminProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, CreateMasterAdminDto, MasterAdminDomain);
      createMap(mapper, MasterAdminDomain, MasterAdminEntity);
      createMap(mapper, MasterAdminEntity, MasterAdminDomain);
      createMap(mapper, MasterAdminDomain, GetMasterAdminDto);
    };
  }
}
