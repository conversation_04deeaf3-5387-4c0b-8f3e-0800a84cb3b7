import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MasterAdminEntity } from './entities/master-admin.entity';
import { MasterAdminRepository } from './repositories/master-admin.repository';

@Module({
  imports: [TypeOrmModule.forFeature([MasterAdminEntity])],
  providers: [MasterAdminRepository],
  exports: [MasterAdminRepository],
})
export class RelationalMasterAdminPersistenceModule {}
