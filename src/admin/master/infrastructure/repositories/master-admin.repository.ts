import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { MasterAdminEntity } from '../entities/master-admin.entity';
import { MasterAdminDomain } from '../../domain/master-admin';
import { NullableType } from '@utils/types/nullable.type';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';

@Injectable()
export class MasterAdminRepository {
  constructor(
    @InjectRepository(MasterAdminEntity)
    private readonly masterAdminRepository: Repository<MasterAdminEntity>,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(data: MasterAdminDomain): Promise<MasterAdminDomain> {
    const requestEntity = this.mapper.map(
      data,
      MasterAdminDomain,
      MasterAdminEntity,
    );
    const masterAdminEntity = await this.masterAdminRepository.save(
      this.masterAdminRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      masterAdminEntity,
      MasterAdminEntity,
      MasterAdminDomain,
    );
    return responseDomain;
  }

  async find(
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<MasterAdminDomain>> {
    const queryBuilder = this.masterAdminRepository
      .createQueryBuilder('masterAdmin')
      .where('masterAdmin.isDeleted = false');

    const page = filter.pageNumber || 1;
    const limit = filter.pageSize || 10;
    const skip = (page - 1) * limit;

    const [items, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const data = this.mapper.mapArray(
      items,
      MasterAdminEntity,
      MasterAdminDomain,
    );

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      hasNextPage: page < Math.ceil(total / limit),
      hasPreviousPage: page > 1,
    };
  }

  async findOne(
    fields: EntityCondition<MasterAdminDomain>,
  ): Promise<NullableType<MasterAdminDomain>> {
    const requestEntity: Partial<MasterAdminEntity> = this.mapper.map(
      fields,
      MasterAdminDomain,
      MasterAdminEntity,
    );
    const masterAdminEntity = await this.masterAdminRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<MasterAdminEntity>),
        isDeleted: false,
      },
    });
    if (masterAdminEntity) {
      const responseDomain = this.mapper.map(
        masterAdminEntity,
        MasterAdminEntity,
        MasterAdminDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: MasterAdminDomain): Promise<MasterAdminDomain> {
    const requestEntity = this.mapper.map(
      payload,
      MasterAdminDomain,
      MasterAdminEntity,
    );
    const masterAdminEntity =
      await this.masterAdminRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      masterAdminEntity,
      MasterAdminEntity,
      MasterAdminDomain,
    );
    return responseDomain;
  }
}
