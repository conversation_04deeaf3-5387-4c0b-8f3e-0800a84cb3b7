import { AutoMap } from '@automapper/classes';
import { MasterAdminStatus } from './master-admin.types';

export class MasterAdminDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  name: string;

  @AutoMap()
  email: string;

  @AutoMap()
  password: string;

  @AutoMap()
  emailVerified: boolean;

  @AutoMap()
  status: MasterAdminStatus;

  @AutoMap()
  lastLoginAt?: Date;

  @AutoMap()
  loginCount: number;

  @AutoMap()
  failedAttempts: number;

  @AutoMap()
  lockedUntil?: Date;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt?: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;
}
