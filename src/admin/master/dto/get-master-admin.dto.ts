import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { MasterAdminStatus } from '../domain/master-admin.types';

export class GetMasterAdminDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: 'John Doe' })
  name: string;

  @AutoMap()
  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @AutoMap()
  @ApiProperty({ enum: MasterAdminStatus, example: MasterAdminStatus.Active })
  status: MasterAdminStatus;

  @AutoMap()
  @ApiProperty()
  lastLoginAt?: Date;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;
}

export class GetAllMasterAdminsDto {
  @ApiProperty({ type: [GetMasterAdminDto] })
  data: GetMasterAdminDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;
}
