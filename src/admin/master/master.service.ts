import { Injectable, HttpStatus } from '@nestjs/common';
import { MasterAdminRepository } from './infrastructure/repositories/master-admin.repository';
import { MasterAdminDomain } from './domain/master-admin';
import { CreateMasterAdminDto } from './dto/create-master-admin.dto';
import { hash, compare } from 'bcrypt';
import { JwtService } from '@nestjs/jwt';
import {
  LoginMasterAdminDto,
  MasterAdminAuthResponseDto,
} from './dto/login-master-admin.dto';
import { MasterAdminStatus } from './domain/master-admin.types';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';

@Injectable()
export class MasterService {
  constructor(
    private readonly masterAdminRepository: MasterAdminRepository,
    private readonly jwtService: JwtService,
  ) {}

  async create(
    createMasterAdminDto: CreateMasterAdminDto,
  ): Promise<MasterAdminDomain> {
    // Check if admin with this email already exists
    const existingAdmin = await this.masterAdminRepository.findOne({
      email: createMasterAdminDto.email,
    });

    if (existingAdmin) {
      throw new AppException(
        'Master admin with this email already exists',
        ErrorCode.USER_ALREADY_EXISTS,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Hash the password
    const hashedPassword = await hash(createMasterAdminDto.password, 10);

    // Create the master admin
    const masterAdmin = await this.masterAdminRepository.create({
      ...createMasterAdminDto,
      password: hashedPassword,
      emailVerified: true,
      status: MasterAdminStatus.Active,
      loginCount: 0,
      failedAttempts: 0,
      isDeleted: false,
    } as MasterAdminDomain);

    return masterAdmin;
  }

  async login(
    loginDto: LoginMasterAdminDto,
  ): Promise<MasterAdminAuthResponseDto> {
    const admin = await this.masterAdminRepository.findOne({
      email: loginDto.email,
    });

    if (!admin) {
      throw new AppException(
        'Invalid credentials',
        ErrorCode.INVALID_CREDENTIALS,
        HttpStatus.UNAUTHORIZED,
      );
    }

    const isPasswordValid = await compare(loginDto.password, admin.password);
    if (!isPasswordValid) {
      throw new AppException(
        'Invalid credentials',
        ErrorCode.INVALID_CREDENTIALS,
        HttpStatus.UNAUTHORIZED,
      );
    }

    if (admin.status !== MasterAdminStatus.Active) {
      throw new AppException(
        'Account is not active',
        ErrorCode.USER_INACTIVE,
        HttpStatus.FORBIDDEN,
      );
    }

    // Update login info
    await this.masterAdminRepository.update({
      ...admin,
      lastLoginAt: new Date(),
      loginCount: admin.loginCount + 1,
    });

    // Generate JWT token
    const payload = {
      sub: admin.id,
      email: admin.email,
      name: admin.name,
      role: 'master-admin',
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: process.env.MASTER_ADMIN_JWT_SECRET || 'master-admin-secret',
      expiresIn: '1d',
    });

    return {
      id: admin.id,
      name: admin.name,
      email: admin.email,
      accessToken,
    };
  }

  async findAll(
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<MasterAdminDomain>> {
    return this.masterAdminRepository.find(filter);
  }

  async findOne(id: string): Promise<MasterAdminDomain> {
    const admin = await this.masterAdminRepository.findOne({ id });
    if (!admin) {
      throw new AppException(
        'Master admin not found',
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
    return admin;
  }
}
