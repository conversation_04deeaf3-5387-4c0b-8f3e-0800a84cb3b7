import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { MasterService } from './master.service';
import { CreateMasterAdminDto } from './dto/create-master-admin.dto';
import { MasterAdminDomain } from './domain/master-admin';
import {
  GetAllMasterAdminsDto,
  GetMasterAdminDto,
} from './dto/get-master-admin.dto';
import {
  LoginMasterAdminDto,
  MasterAdminAuthResponseDto,
} from './dto/login-master-admin.dto';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';

@ApiTags('Admin - Master')
@Controller({
  path: 'admin/master',
  version: '1',
})
export class MasterController {
  constructor(
    private readonly masterService: MasterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Master Admin' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created', type: GetMasterAdminDto })
  async create(
    @Body() createMasterAdminDto: CreateMasterAdminDto,
  ): Promise<GetMasterAdminDto> {
    try {
      const masterAdmin = await this.masterService.create(createMasterAdminDto);
      return this.mapper.map(masterAdmin, MasterAdminDomain, GetMasterAdminDto);
    } catch (error) {
      throw error;
    }
  }

  @Post('login')
  @ApiOperation({ summary: 'Login as Master Admin' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: MasterAdminAuthResponseDto })
  async login(
    @Body() loginDto: LoginMasterAdminDto,
  ): Promise<MasterAdminAuthResponseDto> {
    try {
      return await this.masterService.login(loginDto);
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all Master Admins' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllMasterAdminsDto })
  async findAll(
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllMasterAdminsDto> {
    try {
      const result = await this.masterService.findAll(filter);
      const mappedData = this.mapper.mapArray(
        result.data,
        MasterAdminDomain,
        GetMasterAdminDto,
      );

      return {
        data: mappedData,
        total: result.total,
        page: result.page,
        limit: result.limit,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get Master Admin by ID' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetMasterAdminDto })
  async findOne(@Param('id') id: string): Promise<GetMasterAdminDto> {
    try {
      const masterAdmin = await this.masterService.findOne(id);
      return this.mapper.map(masterAdmin, MasterAdminDomain, GetMasterAdminDto);
    } catch (error) {
      throw error;
    }
  }
}
