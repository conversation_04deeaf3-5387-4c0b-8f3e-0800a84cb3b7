import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { JwtModule } from '@nestjs/jwt';
import { RelationalMasterAdminPersistenceModule } from './infrastructure/relational-persistence.module';
import { MasterController } from './master.controller';
import { MasterService } from './master.service';
import { MasterAdminProfile } from './infrastructure/mappers/master-admin.profile';

@Module({
  imports: [
    RelationalMasterAdminPersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    JwtModule.register({
      secret: process.env.MASTER_ADMIN_JWT_SECRET || 'master-admin-secret',
      signOptions: { expiresIn: '1d' },
    }),
  ],
  controllers: [MasterController],
  providers: [MasterService, MasterAdminProfile],
  exports: [MasterService],
})
export class MasterModule {}
