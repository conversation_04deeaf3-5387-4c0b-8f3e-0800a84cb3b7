import { Module } from '@nestjs/common';
import { UsersModule } from '@app/business/user/users/users.module';
import databaseConfig from './database/config/database.config';
import appConfig from './config/app.config';
import mailConfig from '@core/mail/config/mail.config';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TypeOrmConfigService } from '@database/typeorm-config.service';
import { MailModule } from '@core/mail/mail.module';
import { HomeModule } from '@app/business/root/home/<USER>';
import { DataSource, DataSourceOptions } from 'typeorm';
import { MailerModule } from '@core/mailer/mailer.module';
import tracingConfig from '@core/infrastructure/tracing/config/tracing.config';
import { InfrastructureModule } from '@core/infrastructure/infrastructure.module';
import paymentConfig from '@core/payment/config/payment.config';
import analyticsConfig from '@core/analytics/config/analytics.config';
import { AnalyticsModule } from '@core/analytics/analytics.module';
import { FileUploadModule } from '@core/file-upload/file-upload.module';
import { TenantsModule } from '@app/business/user/tenants/tenants.module';
import { VehiclesModule } from '@app/business/vehicle/vehicles/vehicles.module';
import { TimeClockSessionModule } from '@app/business/vehicle/time-clock-session/time-clock-session.module';
import { VehicleTypesModule } from '@app/business/vehicle/vehicle-types/vehicle-types.module';
import { ZonesModule } from '@app/business/zone/zones/zones.module';
import { ZoneTableModule } from '@app/business/zone/zone-tables/zone-table.module';
import { DriverZoneAssignmentModule } from '@app/business/zone/driver-assignment/driver-zone-assignment.module';
import { NotificationModule } from '@core/notification/notification.module';
import notificationConfig from '@core/notification/config/notification.config';

const infrastructureDatabaseModule = TypeOrmModule.forRootAsync({
  useClass: TypeOrmConfigService,
  dataSourceFactory: async (options: DataSourceOptions) => {
    return new DataSource(options).initialize();
  },
});

import { CustomerCategoriesModule } from '@app/business/user/customer-categories/customer-categories.module';
import { AddressModule } from '@app/business/address/addresses/address.module';
import { AuthModule } from '@core/auth/auth.module';
import { PriceModifiersModule } from '@app/business/pricing/price-modifiers/price-modifiers.module';
import { PriceCalculatorModule } from '@core/pricing/price-calculator.module';
import { MobileModule } from '@app/business/mobile/mobile.module';
import { PriceSetsModule } from '@app/business/pricing/price-sets/price-sets.module';
import { RbacModule } from '@core/rbac/rbac.module';
import { ContactsModule } from '@app/business/user/contacts/contacts.module';
import { SettingModule } from '@core/settings/settings.module';
import { DriverModule } from './business/user/drivers/driver.module';
import { PackageTemplatesModule } from '@app/business/order/package-templates/package-templates.module';
import { OrdersModule } from '@app/business/order/orders/orders.module';
import { CustomerPortalModule } from '@app/business/customer-portal/customer-portal.module';
import { AdminModule } from '@app/admin/admin.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { SeederModule } from './core/seeder/seeder.module';
import { PricingModule } from '@app/business/pricing/pricing/pricing.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        appConfig,
        mailConfig,
        tracingConfig,
        paymentConfig,
        notificationConfig,
        analyticsConfig,
      ],
      envFilePath: ['.env'],
    }),
    EventEmitterModule.forRoot(),
    InfrastructureModule,
    infrastructureDatabaseModule,

    ServeStaticModule.forRoot({
      rootPath: join(process.cwd(), 'uploads'), // path to your uploads folder
      serveRoot: '/uploads', // URL prefix
    }),

    AuthModule,
    UsersModule,
    DriverModule,
    RbacModule,
    TenantsModule,

    ContactsModule,
    CustomerCategoriesModule,
    AddressModule,
    CustomerPortalModule,

    VehiclesModule,
    VehicleTypesModule,

    ZonesModule,
    ZoneTableModule,
    DriverZoneAssignmentModule,

    PriceCalculatorModule,
    PriceSetsModule,
    PriceModifiersModule,
    PricingModule,

    TimeClockSessionModule,

    SettingModule,

    MailModule,
    MailerModule,
    NotificationModule,
    AnalyticsModule,
    FileUploadModule,

    HomeModule,
    MobileModule,

    PackageTemplatesModule,
    OrdersModule,

    AdminModule,
    SeederModule,
  ],
})
export class AppModule {}
