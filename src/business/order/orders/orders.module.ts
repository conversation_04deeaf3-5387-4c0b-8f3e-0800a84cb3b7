import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { OrdersService } from './orders.service';
import { OrderStatusService } from './services/order-status.service';
import { OrderAssignmentService } from './services/order-assignment.service';
import { TrackingNumberService } from './services/tracking-number.service';
import { OrderPaymentService } from './services/order-payment.service';
import { OrderSeederService } from './services/order-seeder.service';
import { OrderRepository } from './infrastructure/repositories/order.repository';
import { OrderItemRepository } from './infrastructure/repositories/order-item.repository';
import { OrderStatusHistoryRepository } from './infrastructure/repositories/order-status-history.repository';
import { OrderAssignmentHistoryRepository } from './infrastructure/repositories/order-assignment-history.repository';
import { OrderStopHistoryRepository } from './infrastructure/repositories/order-stop-history.repository';
import { OrderEntity } from './infrastructure/entities/order.entity';
import { OrderItemEntity } from './infrastructure/entities/order-item.entity';
import { OrderStatusHistoryEntity } from './infrastructure/entities/order-status-history.entity';
import { OrderAssignmentHistoryEntity } from './infrastructure/entities/order-assignment-history.entity';
import { OrderStopHistoryEntity } from './infrastructure/entities/order-stop-history.entity';
import { OrdersController } from './orders.controller';
import { OrderSeederController } from './controllers/order-seeder.controller';
import { AdminOrdersController } from './controllers/admin-orders.controller';
import { TenantsModule } from '@app/business/user/tenants/tenants.module';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { OrderFilterConfig } from './order-filter.config';
import { QueryService } from '@utils/query-creator/query.service';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { AddressEntity } from '@app/business/address/addresses/infrastructure/entities/address.entity';
import { PackageTemplateEntity } from '@app/business/order/package-templates/infrastructure/entities/package-template.entity';
import { PackageTemplatesModule } from '../package-templates/package-templates.module';
import { PaymentModule } from '@core/payment/payment.module';
import { UsersModule } from '@app/business/user/users/users.module';
import { AddressModule } from '@app/business/address/addresses/address.module';
import { PriceSetsModule } from '@app/business/pricing/price-sets/price-sets.module';
import { PriceCalculatorModule } from '@core/pricing/price-calculator.module';
import { PriceModifiersModule } from '@app/business/pricing/price-modifiers/price-modifiers.module';
import { VehicleTypesModule } from '@app/business/vehicle/vehicle-types/vehicle-types.module';
import { ContactsModule } from '@app/business/user/contacts/contacts.module';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';
import { PriceSetEntity } from '@app/business/pricing/price-sets/infrastructure/entities/price-set.entity';
import { VehicleTypeEntity } from '@app/business/vehicle/vehicle-types/infrastructure/entities/vehicle-type.entity';
import { ContactEntity } from '@app/business/user/contacts/infrastructure/persistence/relational/entities/contact.entity';
import { PublicOrderController } from './public-order.controller';
import { FileUploadModule } from '@core/file-upload/file-upload.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OrderEntity,
      OrderItemEntity,
      OrderStatusHistoryEntity,
      OrderAssignmentHistoryEntity,
      OrderStopHistoryEntity,
      UserEntity,
      ContactEntity,
      AddressEntity,
      PackageTemplateEntity,
      VehicleEntity,
      VehicleTypeEntity,
      PriceSetEntity,
      ZoneEntity,
    ]),
    EventEmitterModule.forRoot(),
    TenantsModule,
    forwardRef(() => UsersModule),
    forwardRef(() => ContactsModule),
    AddressModule, // Changed from forwardRef to direct import
    forwardRef(() => PackageTemplatesModule),
    PaymentModule,
    PriceSetsModule,
    PriceCalculatorModule,
    forwardRef(() => PriceModifiersModule),
    forwardRef(() => VehicleTypesModule),
    FileUploadModule,
  ],
  controllers: [
    OrdersController,
    OrderSeederController,
    AdminOrdersController,
    PublicOrderController,
  ],
  providers: [
    OrdersService,
    OrderStatusService,
    OrderAssignmentService,
    TrackingNumberService,
    OrderPaymentService,
    OrderSeederService,
    OrderRepository,
    OrderItemRepository,
    OrderStatusHistoryRepository,
    OrderAssignmentHistoryRepository,
    OrderStopHistoryRepository,
    QueryService,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(OrderFilterConfig()),
    },
  ],
  exports: [
    OrdersService,
    OrderStatusService,
    OrderAssignmentService,
    OrderPaymentService,
    OrderSeederService,
  ],
})
export class OrdersModule {}
