import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiCookieAuth,
} from '@nestjs/swagger';
import { JwtContactAuthGuard } from '@core/auth/guards/jwt-contact-auth.guard';
import { ContactPermissionGuard } from '@app/business/user/contacts/guards/contact-permission.guard';
import { RequireContactPermission } from '@app/business/user/contacts/decorators/require-contact-permission.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { ContactsService } from '@app/business/user/contacts/contacts.service';
import {
  OrderSeederService,
  SeedOrderOptions,
} from '../services/order-seeder.service';
import { OrderStatus } from '../domain/order.types';

export class SeedOrdersDto {
  /**
   * Number of orders to create (1-20)
   * @example 5
   * @description Controls how many test orders will be generated in a single request
   */
  count: number;

  /**
   * Customer ID to associate with the orders
   * @example "550e8400-e29b-41d4-a716-446655440000"
   * @description Optional. If not provided, the contact's customer ID will be used
   */
  customerId?: string;

  /**
   * Whether to use existing addresses or create new ones
   * @example true
   * @description
   * - true: Use addresses already in the system for this customer
   * - false: Generate new random addresses for testing
   */
  useExistingAddresses?: boolean;

  /**
   * Whether to use existing package templates or create new ones
   * @example true
   * @description
   * - true: Use package templates already in the system for this tenant
   * - false: Generate new random package templates for testing
   */
  useExistingPackageTemplates?: boolean;

  /**
   * Status to set for the created orders
   * @example "Draft"
   * @description Available options: "Draft", "Submitted", "Confirmed", "InProgress", "Completed", "Cancelled", "OnHold"
   */
  status?: OrderStatus;

  /**
   * Days from now for scheduled collection (1-30)
   * @example 3
   * @description Controls how far in the future the orders will be scheduled
   */
  scheduledDays?: number;
}

@ApiTags('Order Seeder')
@ApiBearerAuth()
@ApiCookieAuth('contact_session_token')
@Controller({
  path: '/orders/seed',
  version: '1',
})
@UseGuards(JwtContactAuthGuard, ContactPermissionGuard)
@RequireContactPermission('orders')
export class OrderSeederController {
  constructor(
    private readonly orderSeederService: OrderSeederService,
    private readonly contactsService: ContactsService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Seed orders for testing',
    description: 'Creates test orders with all necessary related data',
  })
  @ApiBody({
    type: SeedOrdersDto,
    description: 'Options for generating test orders',
    examples: {
      basic: {
        summary: 'Basic example',
        description: 'Create 3 orders with default settings',
        value: {
          count: 3,
        },
      },
      complete: {
        summary: 'Complete example',
        description: 'Create 5 orders with specific settings',
        value: {
          count: 5,
          useExistingAddresses: true,
          useExistingPackageTemplates: true,
          status: 'Draft',
          scheduledDays: 7,
        },
      },
      newData: {
        summary: 'Generate new data',
        description:
          'Create orders with new generated addresses and package templates',
        value: {
          count: 2,
          useExistingAddresses: false,
          useExistingPackageTemplates: false,
          status: 'Submitted',
          scheduledDays: 3,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Orders created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Successfully created 5 orders' },
        orders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                example: '550e8400-e29b-41d4-a716-446655440000',
                description: 'The unique ID of the created order',
              },
              trackingNumber: {
                type: 'string',
                example: 'TRK-12345678',
                description: 'The tracking number assigned to the order',
              },
              status: {
                type: 'string',
                example: 'Draft',
                description: 'The current status of the order',
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async seedOrders(
    @CurrentUser() contactData: JwtPayload,
    @Body() seedOrdersDto: SeedOrdersDto,
  ) {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // Validate input
    const count = Math.min(Math.max(1, seedOrdersDto.count || 1), 20); // Limit between 1-20
    const scheduledDays = Math.min(
      Math.max(1, seedOrdersDto.scheduledDays || 3),
      30,
    ); // Limit between 1-30

    const options: SeedOrderOptions = {
      count,
      tenantId: contact.tenantId,
      contactId: contactData.sub,
      customerId: seedOrdersDto.customerId,
      useExistingAddresses: seedOrdersDto.useExistingAddresses,
      useExistingPackageTemplates: seedOrdersDto.useExistingPackageTemplates,
      status: seedOrdersDto.status || OrderStatus.Draft,
      scheduledDays,
    };

    const orders = await this.orderSeederService.seedOrders(options);

    return {
      success: true,
      message: `Successfully created ${orders.length} orders`,
      orders: orders.map((order) => ({
        id: order.id,
        trackingNumber: order.trackingNumber,
        status: order.status,
      })),
    };
  }
}
