import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderEntity } from './entities/order.entity';
import { OrderItemEntity } from './entities/order-item.entity';
import { OrderStatusHistoryEntity } from './entities/order-status-history.entity';
import { OrderAssignmentHistoryEntity } from './entities/order-assignment-history.entity';
import { OrderStopHistoryEntity } from './entities/order-stop-history.entity';
import { OrderRepository } from './repositories/order.repository';
import { OrderItemRepository } from './repositories/order-item.repository';
import { OrderStatusHistoryRepository } from './repositories/order-status-history.repository';
import { OrderAssignmentHistoryRepository } from './repositories/order-assignment-history.repository';
import { OrderStopHistoryRepository } from './repositories/order-stop-history.repository';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { OrderFilterConfig } from '../order-filter.config';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { AddressEntity } from '@app/business/address/addresses/infrastructure/entities/address.entity';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { VehicleTypeEntity } from '@app/business/vehicle/vehicle-types/infrastructure/entities/vehicle-type.entity';
import { PriceSetEntity } from '@app/business/pricing/price-sets/infrastructure/entities/price-set.entity';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';
import { PackageTemplateEntity } from '@app/business/order/package-templates/infrastructure/entities/package-template.entity';
import { ContactEntity } from '@app/business/user/contacts/infrastructure/persistence/relational/entities/contact.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OrderEntity,
      OrderItemEntity,
      OrderStatusHistoryEntity,
      OrderAssignmentHistoryEntity,
      OrderStopHistoryEntity,
      UserEntity,
      ContactEntity,
      AddressEntity,
      VehicleEntity,
      VehicleTypeEntity,
      PriceSetEntity,
      ZoneEntity,
      PackageTemplateEntity,
    ]),
  ],
  providers: [
    OrderRepository,
    OrderItemRepository,
    OrderStatusHistoryRepository,
    OrderAssignmentHistoryRepository,
    OrderStopHistoryRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(OrderFilterConfig()),
    },
  ],
  exports: [
    OrderRepository,
    OrderItemRepository,
    OrderStatusHistoryRepository,
    OrderAssignmentHistoryRepository,
    OrderStopHistoryRepository,
    TypeOrmModule,
  ],
})
export class RelationalOrderPersistenceModule {}
