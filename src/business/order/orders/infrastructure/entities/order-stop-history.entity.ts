import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { OrderEntity } from './order.entity';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { OrderStopType } from '../../domain/order.types';

@Entity('order_stop_history')
export class OrderStopHistoryEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  orderId: string;

  @ManyToOne(() => OrderEntity, (order) => order.stopHistory, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'order_id' })
  order: OrderEntity;

  @AutoMap()
  @Column({ length: 50 })
  stopType: OrderStopType;

  @AutoMap()
  @Column({ type: 'timestamptz' })
  scheduledTime: Date;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true })
  actualTime: Date;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true })
  locationData: Record<string, any>;

  @AutoMap()
  @Column({ nullable: true })
  success: boolean;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  failureReason: string;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true })
  signatureData: Record<string, any>;

  @AutoMap()
  @Column({ type: 'text', array: true, nullable: true })
  photoUrls: string[];

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  notes: string;

  @AutoMap()
  @Column({ type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  @AutoMap()
  @Column('uuid')
  createdBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;
}
