import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { OrderEntity } from './order.entity';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';

@Entity('order_assignment_history')
export class OrderAssignmentHistoryEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid', { name: 'order_id' })
  orderId: string;

  @ManyToOne(() => OrderEntity, (order) => order.assignmentHistory, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'order_id' })
  order: OrderEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'previous_assignee_id' })
  previousAssigneeId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'previous_assignee_id' })
  previousAssignee: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'new_assignee_id' })
  newAssigneeId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'new_assignee_id' })
  newAssignee: UserEntity;

  @AutoMap()
  @Column('uuid', { name: 'assigned_by_id' })
  assignedById: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'assigned_by_id' })
  assignedBy: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'assigned_vehicle_id' })
  assignedVehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'assigned_vehicle_id' })
  assignedVehicle: VehicleEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'previous_vehicle_id' })
  previousVehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'previous_vehicle_id' })
  previousVehicle: VehicleEntity;

  @AutoMap()
  @Column({ length: 50, name: 'assignment_type' })
  assignmentType: string; // 'Assign', 'Unassign', 'Reassign'

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  reason: string;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @AutoMap()
  @Column({ type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;
}
