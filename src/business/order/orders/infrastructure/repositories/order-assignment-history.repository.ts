import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrderAssignmentHistoryEntity } from '../entities/order-assignment-history.entity';
import { OrderAssignmentHistory } from '../../domain/order-assignment-history';

@Injectable()
export class OrderAssignmentHistoryRepository {
  constructor(
    @InjectRepository(OrderAssignmentHistoryEntity)
    private readonly orderAssignmentHistoryRepository: Repository<OrderAssignmentHistoryEntity>,
  ) {}

  async create(
    orderId: string,
    assignmentType: string,
    assignedById: string,
    newAssigneeId?: string,
    previousAssigneeId?: string,
    assignedVehicleId?: string,
    previousVehicleId?: string,
    reason?: string,
    metadata?: Record<string, any>,
  ): Promise<OrderAssignmentHistory> {
    const assignmentHistory = this.orderAssignmentHistoryRepository.create({
      orderId,
      assignmentType,
      assignedById,
      newAssigneeId,
      previousAssigneeId,
      assignedVehicleId,
      previousVehicleId,
      reason,
      metadata,
    });

    const savedAssignmentHistory =
      await this.orderAssignmentHistoryRepository.save(assignmentHistory);

    return this.mapToOrderAssignmentHistory(savedAssignmentHistory);
  }

  async findByOrderId(orderId: string): Promise<OrderAssignmentHistory[]> {
    const assignmentHistories =
      await this.orderAssignmentHistoryRepository.find({
        where: { orderId },
        order: { createdAt: 'DESC' },
        relations: [
          'assignedBy',
          'newAssignee',
          'previousAssignee',
          'assignedVehicle',
          'previousVehicle',
        ],
      });

    return assignmentHistories.map((history) =>
      this.mapToOrderAssignmentHistory(history),
    );
  }

  async findLatestByOrderId(
    orderId: string,
  ): Promise<OrderAssignmentHistory | null> {
    const latestAssignment =
      await this.orderAssignmentHistoryRepository.findOne({
        where: { orderId },
        order: { createdAt: 'DESC' },
        relations: [
          'assignedBy',
          'newAssignee',
          'previousAssignee',
          'assignedVehicle',
          'previousVehicle',
        ],
      });

    return latestAssignment
      ? this.mapToOrderAssignmentHistory(latestAssignment)
      : null;
  }

  async findLatestAssigneeByOrderId(orderId: string): Promise<string | null> {
    const latestAssignment =
      await this.orderAssignmentHistoryRepository.findOne({
        where: { orderId },
        order: { createdAt: 'DESC' },
        select: ['newAssigneeId'],
      });

    return latestAssignment?.newAssigneeId || null;
  }

  async findLatestVehicleByOrderId(orderId: string): Promise<string | null> {
    const latestAssignment =
      await this.orderAssignmentHistoryRepository.findOne({
        where: { orderId },
        order: { createdAt: 'DESC' },
        select: ['assignedVehicleId'],
      });

    return latestAssignment?.assignedVehicleId || null;
  }

  async deleteByOrderId(orderId: string): Promise<boolean> {
    const result = await this.orderAssignmentHistoryRepository.delete({
      orderId,
    });
    if (!result) {
      return true;
    }
    const isDeleted = result.affected;
    if (isDeleted) {
      return !!isDeleted;
    } else {
      return false;
    }
  }

  private mapToOrderAssignmentHistory(
    entity: OrderAssignmentHistoryEntity,
  ): OrderAssignmentHistory {
    const assignmentHistory = new OrderAssignmentHistory();
    assignmentHistory.id = entity.id;
    assignmentHistory.orderId = entity.orderId;
    assignmentHistory.previousAssigneeId = entity.previousAssigneeId;
    assignmentHistory.newAssigneeId = entity.newAssigneeId;
    assignmentHistory.assignedById = entity.assignedById;
    assignmentHistory.assignedVehicleId = entity.assignedVehicleId;
    assignmentHistory.previousVehicleId = entity.previousVehicleId;
    assignmentHistory.assignmentType = entity.assignmentType;
    assignmentHistory.reason = entity.reason;
    assignmentHistory.createdAt = entity.createdAt;
    assignmentHistory.metadata = entity.metadata;
    return assignmentHistory;
  }
}
