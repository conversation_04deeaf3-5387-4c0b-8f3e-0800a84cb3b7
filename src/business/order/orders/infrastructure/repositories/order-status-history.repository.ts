import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrderStatusHistoryEntity } from '../entities/order-status-history.entity';
import { OrderStatusHistory } from '../../domain/order-status-history';
import { OrderStatus } from '../../domain/order.types';

@Injectable()
export class OrderStatusHistoryRepository {
  constructor(
    @InjectRepository(OrderStatusHistoryEntity)
    private readonly orderStatusHistoryRepository: Repository<OrderStatusHistoryEntity>,
  ) {}

  async create(
    orderId: string,
    previousStatus: OrderStatus | null,
    newStatus: OrderStatus,
    changedBy: string,
    reason?: string,
    comments?: string,
    locationData?: Record<string, any>,
    metadata?: Record<string, any>,
  ): Promise<OrderStatusHistory> {
    const statusHistory = this.orderStatusHistoryRepository.create({
      orderId,
      previousStatus,
      newStatus,
      reason,
      comments,
      locationData,
      metadata,
      changedBy,
    });

    const savedStatusHistory =
      await this.orderStatusHistoryRepository.save(statusHistory);

    return this.mapToOrderStatusHistory(savedStatusHistory);
  }

  async findByOrderId(orderId: string): Promise<OrderStatusHistory[]> {
    const statusHistories = await this.orderStatusHistoryRepository.find({
      where: { orderId },
      order: { changedAt: 'DESC' },
      relations: ['changedByUser'],
    });

    return statusHistories.map((history) =>
      this.mapToOrderStatusHistory(history),
    );
  }

  async findLatestByOrderId(
    orderId: string,
  ): Promise<OrderStatusHistory | null> {
    const latestStatus = await this.orderStatusHistoryRepository.findOne({
      where: { orderId },
      order: { changedAt: 'DESC' },
      relations: ['changedByUser'],
    });

    return latestStatus ? this.mapToOrderStatusHistory(latestStatus) : null;
  }

  async deleteByOrderId(orderId: string): Promise<boolean> {
    const result = await this.orderStatusHistoryRepository.delete({ orderId });
    if (!result) {
      return true;
    }
    const isDeleted = result.affected;
    if (isDeleted) {
      return !!isDeleted;
    } else {
      return false;
    }
  }

  private mapToOrderStatusHistory(
    entity: OrderStatusHistoryEntity,
  ): OrderStatusHistory {
    const statusHistory = new OrderStatusHistory();
    statusHistory.id = entity.id;
    statusHistory.orderId = entity.orderId;
    statusHistory.previousStatus = entity.previousStatus || undefined;
    statusHistory.newStatus = entity.newStatus;
    statusHistory.reason = entity.reason;
    statusHistory.comments = entity.comments;
    statusHistory.locationData = entity.locationData;
    statusHistory.metadata = entity.metadata;
    statusHistory.changedBy = entity.changedBy;
    statusHistory.changedAt = entity.changedAt;
    return statusHistory;
  }
}
