import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrderStopHistoryEntity } from '../entities/order-stop-history.entity';
import { OrderStopHistory } from '../../domain/order-stop-history';
import { OrderStopType } from '../../domain/order.types';

@Injectable()
export class OrderStopHistoryRepository {
  constructor(
    @InjectRepository(OrderStopHistoryEntity)
    private readonly orderStopHistoryRepository: Repository<OrderStopHistoryEntity>,
  ) {}

  async create(
    orderId: string,
    stopType: OrderStopType,
    scheduledTime: Date,
    createdBy: string,
    actualTime?: Date,
    locationData?: Record<string, any>,
    success?: boolean,
    failureReason?: string,
    signatureData?: Record<string, any>,
    photoUrls?: string[],
    notes?: string,
    metadata?: Record<string, any>,
  ): Promise<OrderStopHistory> {
    const stopHistory = this.orderStopHistoryRepository.create({
      orderId,
      stopType,
      scheduledTime,
      actualTime,
      locationData,
      success,
      failureReason,
      signatureData,
      photoUrls,
      notes,
      metadata,
      createdBy,
    });

    const savedStopHistory =
      await this.orderStopHistoryRepository.save(stopHistory);

    return this.mapToOrderStopHistory(savedStopHistory);
  }

  async findByOrderId(orderId: string): Promise<OrderStopHistory[]> {
    const stopHistories = await this.orderStopHistoryRepository.find({
      where: { orderId },
      order: { createdAt: 'DESC' },
      relations: ['createdByUser'],
    });

    return stopHistories.map((history) => this.mapToOrderStopHistory(history));
  }

  async findByOrderIdAndStopType(
    orderId: string,
    stopType: OrderStopType,
  ): Promise<OrderStopHistory[]> {
    const stopHistories = await this.orderStopHistoryRepository.find({
      where: { orderId, stopType },
      order: { createdAt: 'DESC' },
      relations: ['createdByUser'],
    });

    return stopHistories.map((history) => this.mapToOrderStopHistory(history));
  }

  async findLatestByOrderIdAndStopType(
    orderId: string,
    stopType: OrderStopType,
  ): Promise<OrderStopHistory | null> {
    const latestStop = await this.orderStopHistoryRepository.findOne({
      where: { orderId, stopType },
      order: { createdAt: 'DESC' },
      relations: ['createdByUser'],
    });

    return latestStop ? this.mapToOrderStopHistory(latestStop) : null;
  }

  async updateStop(
    id: string,
    actualTime?: Date,
    locationData?: Record<string, any>,
    success?: boolean,
    failureReason?: string,
    signatureData?: Record<string, any>,
    photoUrls?: string[],
    notes?: string,
  ): Promise<OrderStopHistory | null> {
    const stop = await this.orderStopHistoryRepository.findOne({
      where: { id },
    });

    if (!stop) {
      return null;
    }

    if (actualTime !== undefined) stop.actualTime = actualTime;
    if (locationData !== undefined) stop.locationData = locationData;
    if (success !== undefined) stop.success = success;
    if (failureReason !== undefined) stop.failureReason = failureReason;
    if (signatureData !== undefined) stop.signatureData = signatureData;
    if (photoUrls !== undefined) stop.photoUrls = photoUrls;
    if (notes !== undefined) stop.notes = notes;

    const updatedStop = await this.orderStopHistoryRepository.save(stop);

    return this.mapToOrderStopHistory(updatedStop);
  }

  async deleteByOrderId(orderId: string): Promise<boolean> {
    const result = await this.orderStopHistoryRepository.delete({ orderId });
    if (!result) {
      return true;
    }
    const isDeleted = result.affected;
    if (isDeleted) {
      return !!isDeleted;
    } else {
      return false;
    }
  }

  private mapToOrderStopHistory(
    entity: OrderStopHistoryEntity,
  ): OrderStopHistory {
    const stopHistory = new OrderStopHistory();
    stopHistory.id = entity.id;
    stopHistory.orderId = entity.orderId;
    stopHistory.stopType = entity.stopType;
    stopHistory.scheduledTime = entity.scheduledTime;
    stopHistory.actualTime = entity.actualTime;
    stopHistory.locationData = entity.locationData;
    stopHistory.success = entity.success;
    stopHistory.failureReason = entity.failureReason;
    stopHistory.signatureData = entity.signatureData;
    stopHistory.photoUrls = entity.photoUrls;
    stopHistory.notes = entity.notes;
    stopHistory.metadata = entity.metadata;
    stopHistory.createdBy = entity.createdBy;
    stopHistory.createdAt = entity.createdAt;
    return stopHistory;
  }
}
