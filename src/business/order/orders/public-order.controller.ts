import { <PERSON>, Get, Param } from "@nestjs/common";
import { ApiForbiddenResponse, ApiNotFoundResponse, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import { OrderDetailResponseDto } from "./dto/order-detail-response.dto";
import { OrdersService } from "./orders.service";
import { NullableType } from "../../../utils/types/nullable.type";
import { Order } from "./domain/order";

@ApiTags('Public - Order')
@Controller({
  path: '/orders/customer/',
  version: '1',
})
export class PublicOrderController {
    constructor(private readonly ordersService: OrdersService) { }
    @Get('tracking/:trackingNumber')
    @ApiOperation({ summary: 'Get an order by tracking number with no authentication' })
    @ApiOkResponse({
        description: 'Order details with complete history',
        type: OrderDetailResponseDto,
    })
    @ApiNotFoundResponse({ description: 'Order not found' })
    @ApiForbiddenResponse({
        description: 'Insufficient permissions',
    })
    @ApiParam({
        name: 'trackingNumber',
        description: 'Order tracking number',
        type: 'string',
        example: 'TRK-20250408-AB12C',
    })
    async findByTrackingNumber(@Param('trackingNumber') trackingNumber: string): Promise<NullableType<Order>> {
        return this.ordersService.findByTrackingNumberWithoutAuth(trackingNumber);
    }

}