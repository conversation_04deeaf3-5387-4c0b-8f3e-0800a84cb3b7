import { Injectable } from '@nestjs/common';
import { randomBytes } from 'crypto';
import { DataSource } from 'typeorm';
import { OrderEntity } from '../infrastructure/entities/order.entity';
@Injectable()
export class TrackingNumberService {

  constructor(
    private readonly dataSource: DataSource,
  ) { }

  /**
   * Generate a unique tracking number for an order
   * Format: TRK-YYYYMMDD-XXXXX (where XXXXX is a random alphanumeric string)
   */
  async generateTrackingNumber(): Promise<string> {
    const prefix = 'TRK';
    const datePart = this.getDatePart();
    const randomPart = this.generateRandomString(5);
    const trackingNumber = `${prefix}-${datePart}-${randomPart}`;

    const existing = await this.dataSource.getRepository(OrderEntity).findOne({
      where: { trackingNumber },
    });

    if (existing) {
      return this.generateTrackingNumber(); // 🔁 Try again if already exists
    }

    return trackingNumber;
  }

  /**
   * Generate date part of the tracking number in format YYYYMMDD
   */
  private getDatePart(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    return `${year}${month}${day}`;
  }

  /**
   * Generate a random alphanumeric string of given length
   */
  private generateRandomString(length: number): string {
    // Using only uppercase letters and numbers for better readability
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const randomBytesBuffer = randomBytes(length);
    let result = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = randomBytesBuffer[i] % chars.length;
      result += chars.charAt(randomIndex);
    }

    return result;
  }

  /**
   * Validate if a tracking number follows the correct format
   */
  validateTrackingNumber(trackingNumber: string): boolean {
    const regex = /^TRK-\d{8}-[A-Z0-9]{5}$/;
    return regex.test(trackingNumber);
  }
}
