import { Injectable, Logger } from '@nestjs/common';
import { PaymentService } from '@core/payment/services/payment.service';
import { OrderRepository } from '../infrastructure/repositories/order.repository';
import {
  PaymentStatus,
  PaymentTriggerType,
} from '@core/payment/domain/payment.types';
import {
  OrderStatus,
  PaymentStatus as OrderPaymentStatus,
} from '../domain/order.types';

@Injectable()
export class OrderPaymentService {
  private readonly logger = new Logger(OrderPaymentService.name);

  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly paymentService: PaymentService,
  ) {}

  /**
   * Create a payment for an order
   */
  async createOrderPayment(
    orderId: string,
    userId: string,
    paymentMethodId?: string,
    description?: string,
    metadata?: Record<string, any>,
    triggerType: PaymentTriggerType = PaymentTriggerType.Manual,
  ): Promise<any> {
    this.logger.log(`Creating payment for order ${orderId}`);

    // Get the order
    const order = await this.orderRepository.findById(orderId);
    if (!order) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    // Check if order is in a valid state for payment
    if (
      order.status === OrderStatus.Cancelled ||
      order.paymentStatus === OrderPaymentStatus.Paid
    ) {
      throw new Error(
        `Cannot create payment for order in status ${order.status} with payment status ${order.paymentStatus}`,
      );
    }

    // Create payment
    const payment = await this.paymentService.createPayment(
      order.tenantId,
      userId,
      order.totalPrice,
      'USD', // Default currency
      'order',
      orderId,
      order.customerId,
      '', // Customer email - would need to be fetched from customer record
      '', // Customer name - would need to be fetched from customer record
      paymentMethodId,
      description || `Payment for order ${order.trackingNumber}`,
      {
        orderTrackingNumber: order.trackingNumber,
        orderReferenceNumber: order.referenceNumber,
        ...metadata,
      },
      triggerType,
    );

    // If payment was processed immediately and completed, update order payment status
    if (payment.status === PaymentStatus.Completed) {
      await this.updateOrderPaymentStatus(orderId, OrderPaymentStatus.Paid);
    }

    return payment;
  }

  /**
   * Process a payment for an order
   */
  async processOrderPayment(
    orderId: string,
    paymentId: string,
    userId: string,
  ): Promise<any> {
    this.logger.log(`Processing payment ${paymentId} for order ${orderId}`);

    // Get the order
    const order = await this.orderRepository.findById(orderId);
    if (!order) {
      throw new Error(`Order with ID ${orderId} not found`);
    }

    // Process the payment
    const result = await this.paymentService.processPayment(paymentId, userId);

    // If payment was completed, update order payment status
    if (result.success && result.status === 'Completed') {
      await this.updateOrderPaymentStatus(orderId, OrderPaymentStatus.Paid);
    } else if (result.success && result.status === 'Processing') {
      // Payment is still processing, no status update needed
    } else {
      // Payment failed
      this.logger.error(
        `Payment ${paymentId} for order ${orderId} failed: ${result.message}`,
      );
    }

    return result;
  }

  /**
   * Get payments for an order
   */
  async getOrderPayments(orderId: string): Promise<any[]> {
    return this.paymentService.getPaymentsForEntity('order', orderId);
  }

  /**
   * Update order payment status
   */
  private async updateOrderPaymentStatus(
    orderId: string,
    paymentStatus: OrderPaymentStatus,
  ): Promise<void> {
    await this.orderRepository.update(orderId, '', {
      paymentStatus,
      updatedAt: new Date(),
    });

    this.logger.log(
      `Updated order ${orderId} payment status to ${paymentStatus}`,
    );
  }
}
