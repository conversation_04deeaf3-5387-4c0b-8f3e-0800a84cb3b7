import { AutoMap } from '@automapper/classes';
import { OrderStatus } from './order.types';

export class OrderStatusHistory {
  @AutoMap()
  id: string;

  @AutoMap()
  orderId: string;

  @AutoMap()
  previousStatus?: OrderStatus;

  @AutoMap()
  newStatus: OrderStatus;

  @AutoMap()
  reason?: string;

  @AutoMap()
  comments?: string;

  @AutoMap()
  locationData?: Record<string, any>;

  @AutoMap()
  metadata?: Record<string, any>;

  @AutoMap()
  changedBy: string;

  @AutoMap()
  changedAt: Date;
}
