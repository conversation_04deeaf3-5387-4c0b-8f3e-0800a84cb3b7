import { AutoMap } from '@automapper/classes';
import { OrderStopType } from './order.types';

export class OrderStopHistory {
  @AutoMap()
  id: string;

  @AutoMap()
  orderId: string;

  @AutoMap()
  stopType: OrderStopType;

  @AutoMap()
  scheduledTime: Date;

  @AutoMap()
  actualTime?: Date;

  @AutoMap()
  locationData?: Record<string, any>;

  @AutoMap()
  success?: boolean;

  @AutoMap()
  failureReason?: string;

  @AutoMap()
  signatureData?: Record<string, any>;

  @AutoMap()
  photoUrls?: string[];

  @AutoMap()
  notes?: string;

  @AutoMap()
  metadata?: Record<string, any>;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  createdAt: Date;
}
