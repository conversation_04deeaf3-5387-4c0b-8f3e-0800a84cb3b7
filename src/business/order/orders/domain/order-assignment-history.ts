import { AutoMap } from '@automapper/classes';

export class OrderAssignmentHistory {
  @AutoMap()
  id: string;

  @AutoMap()
  orderId: string;

  @AutoMap()
  previousAssigneeId?: string;

  @AutoMap()
  newAssigneeId?: string;

  @AutoMap()
  assignedById: string;

  @AutoMap()
  assignedVehicleId?: string;

  @AutoMap()
  previousVehicleId?: string;

  @AutoMap()
  assignmentType: string; // 'Assign', 'Unassign', 'Reassign'

  @AutoMap()
  reason?: string;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  metadata?: Record<string, any>;
}
