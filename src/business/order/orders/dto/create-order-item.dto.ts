import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsInt,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  Min,
} from 'class-validator';

export class CreateOrderItemDto {
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Package template ID',
  })
  @IsUUID('4')
  @IsOptional()
  packageTemplateId?: string;

  @ApiProperty({
    example: 'Box',
    description: 'Type of item',
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  itemType: string;

  @ApiProperty({
    example: 2,
    description: 'Quantity of this item',
    default: 1,
  })
  @IsInt()
  @Min(1)
  @Type(() => Number)
  quantity: number;

  @ApiPropertyOptional({
    example: 5.5,
    description: 'Weight of this item',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  totalWeight?: number;

  @ApiPropertyOptional({
    example: 'kg',
    description: 'Unit of weight measurement',
    default: 'kg',
  })
  @IsString()
  @IsOptional()
  @MaxLength(10)
  weightUnit?: string;

  @ApiPropertyOptional({
    example: 30,
    description: 'Length of this item',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  length?: number;

  @ApiPropertyOptional({
    example: 20,
    description: 'Width of this item',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  width?: number;

  @ApiPropertyOptional({
    example: 15,
    description: 'Height of this item',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  height?: number;

  @ApiPropertyOptional({
    example: 'cm',
    description: 'Unit of dimension measurement',
    default: 'cm',
  })
  @IsString()
  @IsOptional()
  @MaxLength(10)
  dimensionUnit?: string;

  @ApiPropertyOptional({
    example: 200,
    description: 'Declared value of this item',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  declaredValue?: number;

  @ApiPropertyOptional({
    example: 'Fragile electronics equipment',
    description: 'Description of this item',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    example: 'Handle with care',
    description: 'Notes for this item',
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    type: 'string',
    format: 'binary',
    description: 'File upload for item image',
  })
  imageUrl?: any;

  @ApiPropertyOptional({
    example: {
      barcode: '123456789',
      serialNumber: 'SN-ABCD-1234',
    },
    description: 'Additional metadata',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
