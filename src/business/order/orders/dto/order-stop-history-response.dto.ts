import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStopType } from '../domain/order.types';

export class OrderStopHistoryResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique ID for this stop history record',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Order ID',
  })
  orderId: string;

  @ApiProperty({
    enum: OrderStopType,
    example: OrderStopType.Collection,
    description: 'Type of stop (Collection, Delivery, Return)',
  })
  stopType: OrderStopType;

  @ApiProperty({
    example: '2025-04-08T12:00:00Z',
    description: 'Scheduled time for this stop',
  })
  scheduledTime: Date;

  @ApiPropertyOptional({
    example: '2025-04-08T12:15:30Z',
    description: 'Actual time when the stop occurred',
  })
  actualTime?: Date;

  @ApiPropertyOptional({
    example: {
      latitude: 40.7128,
      longitude: -74.006,
      accuracy: 10,
      address: '123 Main St, New York, NY 10001',
      timestamp: '2025-04-08T12:15:30Z',
    },
    description: 'Location data for this stop',
  })
  locationData?: Record<string, any>;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether the stop was successful',
  })
  success?: boolean;

  @ApiPropertyOptional({
    example: 'Customer not available at delivery location',
    description: 'Reason for failure if the stop was unsuccessful',
  })
  failureReason?: string;

  @ApiPropertyOptional({
    example: {
      name: 'John Smith',
      timestamp: '2025-04-08T12:15:30Z',
      signatureData: 'base64-encoded-signature-image-data',
    },
    description: 'Signature data collected at this stop',
  })
  signatureData?: Record<string, any>;

  @ApiPropertyOptional({
    example: [
      'https://example.com/photos/delivery1.jpg',
      'https://example.com/photos/delivery2.jpg',
    ],
    description: 'URLs of photos taken at this stop',
  })
  photoUrls?: string[];

  @ApiPropertyOptional({
    example: 'Package left with doorman',
    description: 'Additional notes about this stop',
  })
  notes?: string;

  @ApiPropertyOptional({
    example: {
      doorbell: 'rung',
      numberOfAttempts: 2,
      contactedCustomer: true,
    },
    description: 'Additional metadata about this stop',
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'ID of the user who created this stop record',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Driver',
    description: 'Name of the user who created this stop record',
  })
  createdByName?: string;

  @ApiProperty({
    example: '2025-04-08T12:16:00Z',
    description: 'When this stop record was created',
  })
  createdAt: Date;
}
