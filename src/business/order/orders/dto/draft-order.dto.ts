import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON>Length,
  Min,
} from 'class-validator';
import { PackageTemplate } from '../../package-templates/domain/package-template';
import { CreatePackageTemplateDto } from '../../package-templates/dto/create-package-template.dto';
import { OrderItemDto } from '../../../mobile/orders/dto/get-order.dto';
import { CreateOrderItemDto } from './create-order-item.dto';

export class DraftOrderDto {
  @ApiPropertyOptional({
    example: 'REF-12345',
    description: 'Optional reference number (customer supplied)',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  referenceNumber?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Customer ID',
  })
  @IsUUID('4')
  @IsOptional()
  customerId?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Collection/Pickup address ID',
  })
  @IsUUID('4')
  @IsNotEmpty()
  collectionAddressId: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'Contact name for collection',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  collectionContactName?: string;

  @ApiPropertyOptional({
    example: 'Ring doorbell and wait for security',
    description: 'Special instructions for collection',
  })
  @IsString()
  @IsOptional()
  collectionInstructions?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Delivery address ID',
  })
  @IsUUID('4')
  @IsNotEmpty()
  deliveryAddressId: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Contact name for delivery',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  deliveryContactName?: string;

  @ApiPropertyOptional({
    example: 'Leave at front desk if no answer',
    description: 'Special instructions for delivery',
  })
  @IsString()
  @IsOptional()
  deliveryInstructions?: string;

  @ApiPropertyOptional({
    example: 'Urgent delivery of medical supplies',
    description: 'Order description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    example: 'Customer requested expedited shipping',
    description: 'Comments about the order',
  })
  @IsString()
  @IsOptional()
  comments?: string;

  @ApiPropertyOptional({
    example: '',
    description: '',
  })
  @IsOptional()
  items?: CreateOrderItemDto[];

  @ApiProperty({
    example: true,
    description: 'Whether cash on delivery is required',
  })
  @IsBoolean()
  isCod: boolean;

  @ApiPropertyOptional({
    example: 100.0,
    description: 'Cash on delivery amount',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  codAmount?: number;

  @ApiProperty({
    example: true,
    description: 'Whether cash on delivery is required',
  })
  @IsBoolean()
  isInsurance: boolean;

  @ApiPropertyOptional({
    example: 500,
    description: 'Declared value of the shipment',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  declaredValue?: number;

  @ApiPropertyOptional({
    example: 'Cash on delivery amount',
    description: 'Internal notes',
  })
  @IsString()
  @IsOptional()
  internalNotes?: string;
}
