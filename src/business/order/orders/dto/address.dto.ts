import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class AddressDto {
  // Removed ID field - addresses will be created or referenced separately

  @ApiProperty({
    example: '<PERSON>',
    description: 'Contact name',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    example: 'ABC Corporation',
    description: 'Company name',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  companyName?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Email address',
  })
  @IsEmail()
  @IsOptional()
  @MaxLength(255)
  email?: string;

  @ApiPropertyOptional({
    example: '+1',
    description: 'Country code for phone number',
  })
  @IsString()
  @IsOptional()
  @MaxLength(10)
  countryCode?: string;

  @ApiPropertyOptional({
    example: '5551234567',
    description: 'Phone number',
  })
  @IsString()
  @IsOptional()
  @MaxLength(20)
  phoneNumber?: string;

  @ApiPropertyOptional({
    example: 123,
    description: 'Phone extension',
  })
  @IsNumber()
  @IsOptional()
  phoneExtension?: number;

  @ApiProperty({
    example: '123 Main St',
    description: 'Address line 1',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  addressLine1: string;

  @ApiPropertyOptional({
    example: 'Suite 100',
    description: 'Address line 2',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  addressLine2?: string;

  @ApiProperty({
    example: 'San Francisco',
    description: 'City',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  city: string;

  @ApiProperty({
    example: 'CA',
    description: 'Province/State',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  province: string;

  @ApiProperty({
    example: '94105',
    description: 'Postal/ZIP code',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  postalCode: string;

  @ApiProperty({
    example: 'USA',
    description: 'Country',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  country: string;

  @ApiPropertyOptional({
    example: 'Ring doorbell twice',
    description: 'Additional notes about the address',
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    example: 37.7749,
    description: 'Latitude coordinate',
  })
  @IsNumber()
  @IsOptional()
  latitude?: number;

  @ApiPropertyOptional({
    example: -122.4194,
    description: 'Longitude coordinate',
  })
  @IsNumber()
  @IsOptional()
  longitude?: number;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether this is a favorite pickup address',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isFavoriteForPickup?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether this is a favorite delivery address',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isFavoriteForDelivery?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether this is the default pickup address',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefaultForPickup?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether this is the default delivery address',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefaultForDelivery?: boolean;
}
