import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  IsOptional,
  IsEnum,
  IsObject,
} from 'class-validator';
import { PaymentTriggerType } from '@core/payment/domain/payment.types';

export class CreateOrderPaymentDto {
  @ApiPropertyOptional({
    description: 'Payment method ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Payment description',
    example: 'Payment for order #12345',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { orderNumber: '12345' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Payment trigger type',
    enum: PaymentTriggerType,
    default: PaymentTriggerType.Manual,
  })
  @IsOptional()
  @IsEnum(PaymentTriggerType)
  triggerType?: PaymentTriggerType;
}
