import { CreateOrderDto } from './create-order.dto';

/**
 * Enhanced version of CreateOrderDto that includes properties
 * added by the controller from JWT token and legacy properties
 * that are being phased out
 */
export interface EnhancedOrderDto extends CreateOrderDto {
  /**
   * Customer ID extracted from the JWT token
   */
  customerId: string;

  /**
   * Contact ID who requested the order (extracted from JW<PERSON> token)
   */
  requestedById: string;

  /**
   * Contact ID who submitted the order (extracted from JW<PERSON> token)
   */
  submittedById: string;

  /**
   * @deprecated Collection zone ID (being phased out)
   */
  collectionZoneId?: string;

  /**
   * @deprecated Delivery zone ID (being phased out)
   */
  deliveryZoneId?: string;

  /**
   * @deprecated Calculated from items
   * Total number of items
   */
  totalItems?: number;

  /**
   * @deprecated Calculated from items
   * Total weight of all items
   */
  totalWeight?: number;

  /**
   * @deprecated Calculated from items
   * Total volume of all items in cubic units
   */
  totalVolume?: number;
}
