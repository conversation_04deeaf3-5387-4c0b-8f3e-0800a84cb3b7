import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class AssignOrderDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'ID of the driver to assign to the order',
  })
  @IsUUID('4')
  @IsNotEmpty()
  driverId: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'ID of the vehicle to assign to the order (optional)',
  })
  @IsUUID('4')
  @IsOptional()
  vehicleId?: string;

  @ApiPropertyOptional({
    example: 'Closest available driver to collection point',
    description: 'Reason for this assignment',
  })
  @IsString()
  @IsOptional()
  reason?: string;
}
