import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class OrderAssignmentHistoryResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique ID for this assignment history record',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Order ID',
  })
  orderId: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'ID of the previous assignee (driver) if any',
  })
  previousAssigneeId?: string;

  @ApiPropertyOptional({
    example: '<PERSON>',
    description: 'Name of the previous assignee (driver) if any',
  })
  previousAssigneeName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655443333',
    description: 'ID of the new assignee (driver)',
  })
  newAssigneeId?: string;

  @ApiPropertyOptional({
    example: '<PERSON>',
    description: 'Name of the new assignee (driver)',
  })
  newAssigneeName?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655444444',
    description: 'ID of the user who made the assignment',
  })
  assignedById: string;

  @ApiPropertyOptional({
    example: 'Admin User',
    description: 'Name of the user who made the assignment',
  })
  assignedByName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655445555',
    description: 'ID of the assigned vehicle if any',
  })
  assignedVehicleId?: string;

  @ApiPropertyOptional({
    example: 'Ford Transit - ABC123',
    description: 'Details of the assigned vehicle if any',
  })
  assignedVehicleDetails?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655446666',
    description: 'ID of the previous vehicle if any',
  })
  previousVehicleId?: string;

  @ApiPropertyOptional({
    example: 'Toyota Hiace - XYZ789',
    description: 'Details of the previous vehicle if any',
  })
  previousVehicleDetails?: string;

  @ApiProperty({
    example: 'Assign',
    description: 'Type of assignment action (Assign, Unassign, Reassign)',
  })
  assignmentType: string;

  @ApiPropertyOptional({
    example: 'Driver requested reassignment due to vehicle breakdown',
    description: 'Reason for the assignment change',
  })
  reason?: string;

  @ApiProperty({
    example: '2025-04-08T12:34:56Z',
    description: 'When this assignment occurred',
  })
  createdAt: Date;

  @ApiPropertyOptional({
    example: {
      dispatcherNote: 'Urgent reassignment',
      priority: 'High',
    },
    description: 'Additional metadata about this assignment',
  })
  metadata?: Record<string, any>;
}
