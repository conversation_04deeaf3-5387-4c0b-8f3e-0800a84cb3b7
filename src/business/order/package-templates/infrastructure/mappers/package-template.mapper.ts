import { PackageTemplate } from '../../domain/package-template';
import { PackageTemplateEntity } from '../entities/package-template.entity';
import { PackageTemplateResponseDto } from '../../dto/package-template-response.dto';

export class PackageTemplateMapper {
  static toDomain(entity: PackageTemplateEntity): PackageTemplate {
    const domain = new PackageTemplate();
    domain.id = entity.id;
    domain.tenantId = entity.tenantId;
    domain.name = entity.name;
    domain.description = entity.description;
    domain.status = entity.status;
    domain.capabilities = entity.capabilities;
    domain.dimensionsRequired = entity.dimensionsRequired;
    domain.weightRequired = entity.weightRequired;
    domain.maxWeight = entity.maxWeight;
    domain.maxVolume = entity.maxVolume;
    domain.length = entity.length;
    domain.width = entity.width;
    domain.height = entity.height;
    domain.priceCalculationRules = entity.priceCalculationRules;
    domain.requiresSignature = entity.requiresSignature;
    domain.requiresInsurance = entity.requiresInsurance;
    domain.specialHandlingInstructions = entity.specialHandlingInstructions;
    domain.vehicleTypeRestrictions = entity.vehicleTypeRestrictions;
    domain.availableZones = entity.availableZones;
    domain.metadata = entity.metadata;
    domain.isDeleted = entity.isDeleted;
    domain.deletedAt = entity.deletedAt;
    domain.createdAt = entity.createdAt;
    domain.updatedAt = entity.updatedAt;
    domain.createdBy = entity.createdBy;
    domain.updatedBy = entity.updatedBy;
    return domain;
  }

  static toEntity(
    domain: Partial<PackageTemplate>,
  ): Partial<PackageTemplateEntity> {
    const entity = new PackageTemplateEntity();
    if (domain.id) entity.id = domain.id;
    if (domain.tenantId) entity.tenantId = domain.tenantId;
    if (domain.name) entity.name = domain.name;
    if (domain.description !== undefined)
      entity.description = domain.description;
    if (domain.status) entity.status = domain.status;
    if (domain.capabilities) entity.capabilities = domain.capabilities;
    if (domain.dimensionsRequired !== undefined)
      entity.dimensionsRequired = domain.dimensionsRequired;
    if (domain.weightRequired !== undefined)
      entity.weightRequired = domain.weightRequired;
    if (domain.maxWeight !== undefined) entity.maxWeight = domain.maxWeight;
    if (domain.maxVolume !== undefined) entity.maxVolume = domain.maxVolume;
    if (domain.length !== undefined) entity.length = domain.length;
    if (domain.width !== undefined) entity.width = domain.width;
    if (domain.height !== undefined) entity.height = domain.height; 
    if (domain.priceCalculationRules !== undefined)
      entity.priceCalculationRules = domain.priceCalculationRules;
    if (domain.requiresSignature !== undefined)
      entity.requiresSignature = domain.requiresSignature;
    if (domain.requiresInsurance !== undefined)
      entity.requiresInsurance = domain.requiresInsurance;
    if (domain.specialHandlingInstructions !== undefined)
      entity.specialHandlingInstructions = domain.specialHandlingInstructions;
    if (domain.vehicleTypeRestrictions !== undefined)
      entity.vehicleTypeRestrictions = domain.vehicleTypeRestrictions;
    if (domain.availableZones !== undefined)
      entity.availableZones = domain.availableZones;
    if (domain.metadata !== undefined) entity.metadata = domain.metadata;
    if (domain.isDeleted !== undefined) entity.isDeleted = domain.isDeleted;
    if (domain.deletedAt) entity.deletedAt = domain.deletedAt;
    if (domain.createdAt) entity.createdAt = domain.createdAt;
    if (domain.updatedAt) entity.updatedAt = domain.updatedAt;
    if (domain.createdBy) entity.createdBy = domain.createdBy;
    if (domain.updatedBy) entity.updatedBy = domain.updatedBy;
    return entity;
  }

  static toResponseDto(domain: PackageTemplate): PackageTemplateResponseDto {
    const dto = new PackageTemplateResponseDto();
    dto.id = domain.id;
    dto.tenantId = domain.tenantId;
    dto.name = domain.name;
    dto.description = domain.description;
    dto.status = domain.status;
    dto.capabilities = domain.capabilities;
    dto.dimensionsRequired = domain.dimensionsRequired;
    dto.weightRequired = domain.weightRequired;
    dto.maxWeight = domain.maxWeight;
    dto.maxVolume = domain.maxVolume;
    dto.length = domain.length;
    dto.width = domain.width;
    dto.height = domain.height;
    dto.priceCalculationRules = domain.priceCalculationRules;
    dto.requiresSignature = domain.requiresSignature;
    dto.requiresInsurance = domain.requiresInsurance;
    dto.specialHandlingInstructions = domain.specialHandlingInstructions;
    dto.vehicleTypeRestrictions = domain.vehicleTypeRestrictions;
    dto.availableZones = domain.availableZones;
    dto.metadata = domain.metadata;
    dto.createdAt = domain.createdAt;
    dto.updatedAt = domain.updatedAt;
    dto.createdBy = domain.createdBy;
    dto.updatedBy = domain.updatedBy;
    return dto;
  }
}
