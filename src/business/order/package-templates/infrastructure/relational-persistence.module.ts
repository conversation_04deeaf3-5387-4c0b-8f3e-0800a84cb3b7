import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PackageTemplateEntity } from './entities/package-template.entity';
import { PackageTemplateRepository } from './repositories/package-template.repository';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { PackageTemplateFilterConfig } from '../package-template-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([PackageTemplateEntity])],
  providers: [
    PackageTemplateRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(PackageTemplateFilterConfig()),
    },
  ],
  exports: [PackageTemplateRepository],
})
export class RelationalPackageTemplatePersistenceModule {}
