import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PackageTemplateEntity } from '../entities/package-template.entity';
import { PackageTemplate } from '../../domain/package-template';
import { PackageTemplateMapper } from '../mappers/package-template.mapper';
import { NullableType } from '@utils/types/nullable.type';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { PackageTemplateResponseDto } from '../../dto/package-template-response.dto';

@Injectable()
export class PackageTemplateRepository {
  constructor(
    @InjectRepository(PackageTemplateEntity)
    private readonly packageTemplateRepository: Repository<PackageTemplateEntity>,
    private readonly filterService: SecureFilterService,
  ) {}

  async create(data: Partial<PackageTemplate>): Promise<PackageTemplate> {
    const entityData = PackageTemplateMapper.toEntity(data);
    const newEntity = await this.packageTemplateRepository.save(entityData);
    return PackageTemplateMapper.toDomain(newEntity as PackageTemplateEntity);
  }

  async findById(id: string): Promise<NullableType<PackageTemplate>> {
    const entity = await this.packageTemplateRepository.findOne({
      where: { id, isDeleted: false },
    });

    return entity ? PackageTemplateMapper.toDomain(entity) : null;
  }

  async findByTenantId(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PackageTemplateResponseDto>> {
    const queryBuilder = this.packageTemplateRepository
      .createQueryBuilder('packageTemplate')
      .where('packageTemplate.tenantId = :tenantId', { tenantId })
      .andWhere('packageTemplate.isDeleted = false');

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = result.data.map((entity) =>
      PackageTemplateMapper.toResponseDto(
        PackageTemplateMapper.toDomain(entity),
      ),
    );

    return { ...result, data: mappedData };
  }

  async update(
    id: string,
    tenantId: string,
    data: Partial<PackageTemplate>,
  ): Promise<NullableType<PackageTemplate>> {
    const packageTemplate = await this.packageTemplateRepository.findOne({
      where: { id, tenantId, isDeleted: false },
    });

    if (!packageTemplate) {
      return null;
    }

    const updates = PackageTemplateMapper.toEntity(data);
    const updated = await this.packageTemplateRepository.save({
      ...packageTemplate,
      ...updates,
    });

    return PackageTemplateMapper.toDomain(updated);
  }

  async softDelete(
    id: string,
    tenantId: string,
    userId: string,
  ): Promise<boolean> {
    const packageTemplate = await this.packageTemplateRepository.findOne({
      where: { id, tenantId, isDeleted: false },
    });

    if (!packageTemplate) {
      return false;
    }

    packageTemplate.isDeleted = true;
    packageTemplate.deletedAt = new Date();
    packageTemplate.updatedBy = userId;

    await this.packageTemplateRepository.save(packageTemplate);
    return true;
  }

  async restore(
    id: string,
    tenantId: string,
    userId: string,
  ): Promise<boolean> {
    const packageTemplate = await this.packageTemplateRepository.findOne({
      where: { id, tenantId, isDeleted: true },
    });

    if (!packageTemplate) {
      return false;
    }

    packageTemplate.isDeleted = false;
    packageTemplate.deletedAt = new Date();
    packageTemplate.updatedBy = userId;

    await this.packageTemplateRepository.save(packageTemplate);
    return true;
  }

  async hardDelete(id: string, tenantId: string): Promise<boolean> {
    const result = await this.packageTemplateRepository.delete({
      id,
      tenantId,
    });

    return result.affected ? result.affected > 0 : false;
  }
}
