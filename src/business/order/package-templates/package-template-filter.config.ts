import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const PackageTemplateFilterConfig = () => {
  const fields = [
    {
      fieldName: 'name',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid package template name format',
      },
    },
    {
      fieldName: 'description',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
        validationMessage: 'Invalid description format',
      },
    },
    {
      fieldName: 'status',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid status value',
      },
    },
    {
      fieldName: 'dimensionsRequired',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'dimensionsRequired must be a boolean value',
      },
    },
    {
      fieldName: 'weightRequired',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'weightRequired must be a boolean value',
      },
    },
    {
      fieldName: 'maxWeight',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
        validationMessage: 'maxWeight must be a numeric value',
      },
    },
    {
      fieldName: 'maxVolume',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
        validationMessage: 'maxVolume must be a numeric value',
      },
    },
    {
      fieldName: 'length',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
      },
    },
    {
      fieldName: 'width',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
      },
    },
    {
      fieldName: 'height',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
      },
    },
    {
      fieldName: 'requiresSignature',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'requiresSignature must be a boolean value',
      },
    },
    {
      fieldName: 'requiresInsurance',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'requiresInsurance must be a boolean value',
      },
    },
    {
      fieldName: 'specialHandlingInstructions',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IS_NULL,
          FilterOperator.IS_NOT_NULL,
        ],
        validationMessage: 'Invalid special handling instructions format',
      },
    },
    {
      fieldName: 'createdAt',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        validationMessage: 'createdAt must be a valid date',
      },
    },
    {
      fieldName: 'updatedAt',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        validationMessage: 'updatedAt must be a valid date',
      },
    },
  ];

  const packageTemplateConfig = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    packageTemplateConfig.addField(fieldName, options);
  });

  return packageTemplateConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields(['name', 'description', 'specialHandlingInstructions'])
    .setSortableFields([
      'name',
      'status',
      'dimensionsRequired',
      'weightRequired',
      'maxWeight',
      'maxVolume',
      'requiresSignature',
      'requiresInsurance',
      'createdAt',
      'updatedAt',
    ])
    .build();
};
