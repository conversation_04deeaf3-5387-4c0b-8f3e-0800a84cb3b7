import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
  ApiParam,
  ApiBody,
  ApiCookieAuth,
} from '@nestjs/swagger';
import { PackageTemplatesService } from './package-templates.service';
import { CreatePackageTemplateDto } from './dto/create-package-template.dto';
import { UpdatePackageTemplateDto } from './dto/update-package-template.dto';
import { PackageTemplateResponseDto } from './dto/package-template-response.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { TenantValidationService } from '@app/business/user/tenants/tenant-validation.service';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { FindAllPackageTemplatesResponseDto } from './dto/find-all-package-templates.dto';

@ApiTags('Business - Order - Package Templates')
@ApiBearerAuth()
@ApiCookieAuth()
@Controller({
  path: '/package-templates',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
@ApiForbiddenResponse({ description: 'Insufficient tenant access permissions' })
export class PackageTemplatesController {
  constructor(
    private readonly packageTemplatesService: PackageTemplatesService,
    private readonly tenantValidationService: TenantValidationService,
    private readonly secureFilterService: SecureFilterService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new package template' })
  @ApiCreatedResponse({
    description: 'Package template created successfully',
    type: PackageTemplateResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiUnauthorizedResponse({ description: 'User not authenticated' })
  @ApiBody({ type: CreatePackageTemplateDto })
  async create(
    @Req() request: RequestWithUser,
    @Body() createPackageTemplateDto: CreatePackageTemplateDto,
  ): Promise<PackageTemplateResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);
    const userId = request.user.sub;

    return this.packageTemplatesService.create(
      tenant.id,
      userId,
      createPackageTemplateDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all package templates for the current tenant' })
  @ApiOkResponse({
    description: 'List of package templates with pagination info',
    type: FindAllPackageTemplatesResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  async findAll(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ) {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );

    return this.packageTemplatesService.findAll(tenant.id, combinedFilter);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a package template by ID' })
  @ApiOkResponse({
    description: 'Package template details',
    type: PackageTemplateResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Package template not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Package template ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findOne(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<PackageTemplateResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    return this.packageTemplatesService.findOne(tenant.id, id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a package template' })
  @ApiOkResponse({
    description: 'Package template updated successfully',
    type: PackageTemplateResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Package template not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Package template ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: UpdatePackageTemplateDto })
  async update(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() updatePackageTemplateDto: UpdatePackageTemplateDto,
  ): Promise<PackageTemplateResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);
    const userId = request.user.sub;

    return this.packageTemplatesService.update(
      tenant.id,
      id,
      userId,
      updatePackageTemplateDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete a package template' })
  @ApiNoContentResponse({
    description: 'Package template has been successfully soft-deleted',
  })
  @ApiNotFoundResponse({ description: 'Package template not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Package template ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async softDelete(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);
    const userId = request.user.sub;

    await this.packageTemplatesService.softDelete(tenant.id, id, userId);
  }

  @Post(':id/restore')
  @ApiOperation({ summary: 'Restore a soft-deleted package template' })
  @ApiOkResponse({
    description: 'Package template has been successfully restored',
    type: PackageTemplateResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Package template not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Package template ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async restore(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<PackageTemplateResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);
    const userId = request.user.sub;

    return this.packageTemplatesService.restore(tenant.id, id, userId);
  }

  @Delete(':id/permanent')
  @ApiOperation({
    summary: 'Permanently delete a package template',
    description:
      'WARNING: This operation cannot be undone. The package template will be permanently removed from the system.',
  })
  @ApiNoContentResponse({
    description: 'Package template has been permanently deleted',
  })
  @ApiNotFoundResponse({ description: 'Package template not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Package template ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async hardDelete(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.packageTemplatesService.hardDelete(tenant.id, id);
  }
}
