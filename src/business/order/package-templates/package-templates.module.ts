import { Module } from '@nestjs/common';
import { PackageTemplatesService } from './package-templates.service';
import { PackageTemplatesController } from './package-templates.controller';
import { TenantsModule } from '@app/business/user/tenants/tenants.module';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { PackageTemplateFilterConfig } from './package-template-filter.config';
import { QueryService } from '@utils/query-creator/query.service';
import { RelationalPackageTemplatePersistenceModule } from './infrastructure/relational-persistence.module';

@Module({
  imports: [RelationalPackageTemplatePersistenceModule, TenantsModule],
  controllers: [PackageTemplatesController],
  providers: [
    PackageTemplatesService,
    QueryService,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(PackageTemplateFilterConfig()),
    },
  ],
  exports: [PackageTemplatesService],
})
export class PackageTemplatesModule {}
