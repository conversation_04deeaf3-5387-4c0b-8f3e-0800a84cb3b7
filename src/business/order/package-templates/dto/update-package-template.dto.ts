import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
} from 'class-validator';
import { PackageStatus } from '../domain/package-template.types';
import { Type } from 'class-transformer';

export class UpdatePackageTemplateDto {
  @ApiPropertyOptional({
    example: 'Premium Box',
    description: 'Name of the package template',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  @ApiPropertyOptional({
    example: 'Premium box for delicate shipments',
    description: 'Description of the package template',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    enum: PackageStatus,
    description: 'Status of the package template',
  })
  @IsEnum(PackageStatus)
  @IsOptional()
  status?: PackageStatus;

  @ApiPropertyOptional({
    example: ['Box', 'Envelope', 'Pallet'],
    description: 'Array of supported package types',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  capabilities?: string[];

  @ApiPropertyOptional({
    example: true,
    description: 'Whether dimensions are required',
  })
  @IsBoolean()
  @IsOptional()
  dimensionsRequired?: boolean;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether weight is required',
  })
  @IsBoolean()
  @IsOptional()
  weightRequired?: boolean;

  @ApiPropertyOptional({
    example: 50.5,
    description: 'Maximum weight allowed',
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  maxWeight?: number;

  @ApiPropertyOptional({
    example: 100.5,
    description: 'Maximum volume allowed',
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  maxVolume?: number;

  @ApiPropertyOptional({
    example: 30.0,
    description: 'Length of the package',
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  length?: number;

  @ApiPropertyOptional({
    example: 20.0,
    description: 'Width of the package',
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  width?: number;

  @ApiPropertyOptional({
    example: 15.0,
    description: 'Height of the package',
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  height?: number;

  @ApiPropertyOptional({
    example: {
      basePrice: 10.0,
      weightFactor: 0.5,
      distanceFactor: 0.1,
    },
    description: 'Rules for price calculation',
  })
  @IsObject()
  @IsOptional()
  priceCalculationRules?: Record<string, any>;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether signature is required',
  })
  @IsBoolean()
  @IsOptional()
  requiresSignature?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether insurance is required',
  })
  @IsBoolean()
  @IsOptional()
  requiresInsurance?: boolean;

  @ApiPropertyOptional({
    example: 'Handle with care. Keep upright.',
    description: 'Special handling instructions',
  })
  @IsString()
  @IsOptional()
  specialHandlingInstructions?: string;

  @ApiPropertyOptional({
    example: ['550e8400-e29b-41d4-a716-************'],
    description: 'Array of allowed vehicle type IDs',
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  vehicleTypeRestrictions?: string[];

  @ApiPropertyOptional({
    example: ['550e8400-e29b-41d4-a716-************'],
    description: 'Array of zone IDs where this package type is available',
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  availableZones?: string[];

  @ApiPropertyOptional({
    example: {
      customField1: 'value1',
      customField2: 'value2',
    },
    description: 'Additional metadata',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
