import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PackageStatus } from '../domain/package-template.types';

export class PackageTemplateResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Tenant ID',
  })
  tenantId: string;

  @ApiProperty({
    example: 'Standard Box',
    description: 'Name of the package template',
  })
  name: string;

  @ApiPropertyOptional({
    example: 'Standard box for most shipments',
    description: 'Description of the package template',
  })
  description?: string;

  @ApiProperty({
    enum: PackageStatus,
    example: PackageStatus.Active,
    description: 'Status of the package template',
  })
  status: PackageStatus;

  @ApiProperty({
    example: ['Box', 'Envelope', 'Pallet'],
    description: 'Array of supported package types',
    type: [String],
  })
  capabilities: string[];

  @ApiProperty({
    example: true,
    description: 'Whether dimensions are required',
  })
  dimensionsRequired: boolean;

  @ApiProperty({
    example: true,
    description: 'Whether weight is required',
  })
  weightRequired: boolean;

  @ApiPropertyOptional({
    example: 50.5,
    description: 'Maximum weight allowed',
  })
  maxWeight?: number;

  @ApiPropertyOptional({
    example: 100.5,
    description: 'Maximum volume allowed',
  })
  maxVolume?: number;

  @ApiPropertyOptional({
    example: 30.0,
    description: 'Length of the package',
  })
  length?: number;

  @ApiPropertyOptional({
    example: 20.0,
    description: 'Width of the package',
  })
  width?: number;

  @ApiPropertyOptional({
    example: 15.0,
    description: 'Height of the package',
  })
  height?: number;

  @ApiPropertyOptional({
    example: {
      basePrice: 10.0,
      weightFactor: 0.5,
      distanceFactor: 0.1,
    },
    description: 'Rules for price calculation',
  })
  priceCalculationRules?: Record<string, any>;

  @ApiProperty({
    example: false,
    description: 'Whether signature is required',
  })
  requiresSignature: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether insurance is required',
  })
  requiresInsurance: boolean;

  @ApiPropertyOptional({
    example: 'Handle with care. Keep upright.',
    description: 'Special handling instructions',
  })
  specialHandlingInstructions?: string;

  @ApiPropertyOptional({
    example: ['550e8400-e29b-41d4-a716-446655440000'],
    description: 'Array of allowed vehicle type IDs',
    type: [String],
  })
  vehicleTypeRestrictions?: string[];

  @ApiPropertyOptional({
    example: ['550e8400-e29b-41d4-a716-446655440001'],
    description: 'Array of zone IDs where this package type is available',
    type: [String],
  })
  availableZones?: string[];

  @ApiPropertyOptional({
    example: {
      customField1: 'value1',
      customField2: 'value2',
    },
    description: 'Additional metadata',
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    example: '2023-01-15T08:30:00.000Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-15T08:30:00.000Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'ID of user who created the template',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'ID of user who last updated the template',
  })
  updatedBy?: string;
}
