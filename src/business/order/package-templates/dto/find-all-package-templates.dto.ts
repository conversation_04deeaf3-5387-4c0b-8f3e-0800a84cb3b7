import { ApiProperty } from '@nestjs/swagger';
import { PackageTemplateResponseDto } from './package-template-response.dto';

export class FindAllPackageTemplatesResponseDto {
  @ApiProperty({
    type: [PackageTemplateResponseDto],
    description: 'List of package templates',
  })
  data: PackageTemplateResponseDto[];

  @ApiProperty({
    example: 25,
    description: 'Total number of package templates',
  })
  total: number;

  @ApiProperty({
    example: 1,
    description: 'Current page number',
  })
  page: number;

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
  })
  limit: number;

  @ApiProperty({
    example: 3,
    description: 'Total number of pages',
  })
  totalPages: number;

  @ApiProperty({
    example: true,
    description: 'Whether there is a next page',
  })
  hasNextPage: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether there is a previous page',
  })
  hasPreviousPage: boolean;
}
