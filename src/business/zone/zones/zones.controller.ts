import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ZonesService } from './zones.service';
import { CreateZoneDto } from './dto/create-zone.dto';
import { ZoneDomain } from './domain/zone';
import { GetAllZoneDto, GetZoneDto } from './dto/get-zone.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { ZoneOperationNotAllowedException } from '../../../utils/errors/exceptions/zone-exceptions';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';

@ApiTags('Business - Zone')
@Controller({
  path: 'zones',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class ZonesController {
  constructor(
    private readonly zonesService: ZonesService,
    private readonly secureFilterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Zone' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createZoneDto: CreateZoneDto,
  ): Promise<ZoneDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new ZoneOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      const zoneDomain = this.mapper.map(
        createZoneDto,
        CreateZoneDto,
        ZoneDomain,
      );
      zoneDomain.tenantId = tenantId;
      const zone = await this.zonesService.create(zoneDomain);
      return zone;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all zones with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllZoneDto })
  async getZoneList(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllZoneDto> {
    try {
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );
      const tenantId = request.tenantContext?.tenantId;

      if (!tenantId) {
        throw new ZoneOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getZoneList',
          'Insufficient tenant access permissions',
        );
      }

      const result = await this.zonesService.getZoneList(
        combinedFilter,
        tenantId,
      );

      const mappedData = this.mapper.mapArray(
        result.data,
        ZoneDomain,
        GetZoneDto,
      );

      const response: GetAllZoneDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get(':zoneId')
  @ApiOperation({ summary: 'Find zone by zone Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetZoneDto })
  async getZoneDetails(@Param('zoneId') zoneId: string): Promise<GetZoneDto> {
    try {
      const responseDomain = await this.zonesService.getZoneDetails(zoneId);
      const response = this.mapper.map(responseDomain, ZoneDomain, GetZoneDto);
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('/postalCode/:postalCode')
  @ApiOperation({ summary: 'Find zone by postal code' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetZoneDto })
  async getZoneByPostalCode(
    @Req() request: RequestWithUser,
    @Param('postalCode') postalCode: string,
  ): Promise<GetZoneDto> {
    try {
      const tenantId = request.tenantContext?.tenantId;

      if (!tenantId) {
        throw new ZoneOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getZoneByPostalCode',
          'Insufficient tenant access permissions',
        );
      }

      const responseDomain = await this.zonesService.getZoneByPostalCode(
        postalCode,
        tenantId,
      );
      const response = this.mapper.map(responseDomain, ZoneDomain, GetZoneDto);
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':zoneId')
  @ApiOperation({ summary: 'Update zone by zone Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateZoneDetails(
    @Param('zoneId') zoneId: string,
    @Body() updateZoneDto: CreateZoneDto,
  ): Promise<void> {
    try {
      const zone = this.mapper.map(updateZoneDto, CreateZoneDto, ZoneDomain);
      zone.id = zoneId;
      await this.zonesService.updateZoneDetails(zone);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':zoneId')
  @ApiOperation({ summary: 'Soft-delete zone by zone Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteZone(@Param('zoneId') zoneId: string): Promise<void> {
    try {
      await this.zonesService.deleteZone(zoneId);
      return;
    } catch (error) {
      throw error;
    }
  }
}
