import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsArray,
  ArrayNotEmpty,
  ArrayUnique,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class CreateZoneDto {
  @AutoMap()
  @ApiProperty({ example: 'Zone A' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ example: ['K1A', 'M5A', 'H2X'] })
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsString({ each: true })
  @Transform(trimTransformer)
  postalCodes: string[];

  @AutoMap()
  @ApiProperty({ default: 'Zone notes' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  notes: string;
}
