import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ZoneEntity } from './entities/zone.entity';
import { ZoneRepository } from './repositories/zone.repository';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { ZoneFilterConfig } from '../zone-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([ZoneEntity])],
  providers: [
    ZoneRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(ZoneFilterConfig()),
    },
  ],
  exports: [ZoneRepository],
})
export class RelationalZonePersistenceModule {}
