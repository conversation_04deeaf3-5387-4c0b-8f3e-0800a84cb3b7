import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, In, Repository } from 'typeorm';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { ZoneEntity } from '../entities/zone.entity';
import { ZoneDomain } from '../../domain/zone';
import { BaseFilterDto } from '../../../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../../../utils/query-creator/interfaces';
import { SecureFilterService } from '../../../../../core/infrastructure/filtering/services/secure-filter.service';
import { ZoneFilterConfig } from '../../zone-filter.config';

@Injectable()
export class ZoneRepository {
  constructor(
    @InjectRepository(ZoneEntity)
    private readonly zoneRepository: Repository<ZoneEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(ZoneFilterConfig());
  }

  async create(data: ZoneDomain): Promise<ZoneDomain> {
    const requestEntity = this.mapper.map(data, ZoneDomain, ZoneEntity);
    const zoneEntity = await this.zoneRepository.save(
      this.zoneRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(zoneEntity, ZoneEntity, ZoneDomain);
    return responseDomain;
  }

  async find(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<ZoneDomain>> {
    const queryBuilder = this.zoneRepository
      .createQueryBuilder('zone')
      .where('zone.isDeleted = false')
      .andWhere('zone.tenantId = :tenantId', { tenantId });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      ZoneEntity,
      ZoneDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findOne(
    fields: EntityCondition<ZoneDomain>,
  ): Promise<NullableType<ZoneDomain>> {
    const requestEntity: Partial<ZoneEntity> = this.mapper.map(
      fields,
      ZoneDomain,
      ZoneEntity,
    );
    const zoneEntity = await this.zoneRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<ZoneEntity>),
        isDeleted: false,
      },
    });
    if (zoneEntity) {
      const responseDomain = this.mapper.map(
        zoneEntity,
        ZoneEntity,
        ZoneDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async findByIds(zoneIds: ZoneDomain['id'][]): Promise<ZoneDomain[]> {
    if (zoneIds.length === 0) return [];
    const zoneEntities = await this.zoneRepository.findBy({
      id: In(zoneIds),
      isDeleted: false,
    });

    return zoneEntities.map((entity) =>
      this.mapper.map(entity, ZoneEntity, ZoneDomain),
    );
  }

  async findLatestUpdatedZoneByPostalCode(
    postalCode: string,
    tenantId: string,
  ): Promise<NullableType<ZoneDomain>> {
    const zoneEntity = await this.zoneRepository
      .createQueryBuilder('zone')
      .where(':postalCode = ANY(zone.postalCodes)', { postalCode })
      .andWhere('zone.tenantId = :tenantId', { tenantId })
      .orderBy('zone.updatedAt', 'DESC')
      .getOne();

    if (zoneEntity) {
      const responseDomain = this.mapper.map(
        zoneEntity,
        ZoneEntity,
        ZoneDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: ZoneDomain): Promise<ZoneDomain> {
    const requestEntity = this.mapper.map(payload, ZoneDomain, ZoneEntity);
    const zoneEntity = await this.zoneRepository.save(requestEntity);
    const responseDomain = this.mapper.map(zoneEntity, ZoneEntity, ZoneDomain);
    return responseDomain;
  }

  /**
   * Find all zones for a tenant without using the filter service
   * @param tenantId The tenant ID
   * @returns Array of zone domains
   */
  async findAllByTenant(tenantId: string): Promise<ZoneDomain[]> {
    const zoneEntities = await this.zoneRepository.find({
      where: {
        tenantId,
        isDeleted: false,
      },
    });

    return this.mapper.mapArray(zoneEntities, ZoneEntity, ZoneDomain);
  }
}
