import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { ZoneDomain } from '../../domain/zone';
import { CreateZoneDto } from '../../dto/create-zone.dto';
import { ZoneEntity } from '../entities/zone.entity';
import { GetZoneDto } from '../../dto/get-zone.dto';

@Injectable()
export class ZoneProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        CreateZoneDto,
        ZoneDomain,
        forMember(
          (dest) => dest.postalCodes,
          mapFrom((src) => src.postalCodes),
        ),
      );

      createMap(
        mapper,
        ZoneDomain,
        ZoneEntity,
        forMember(
          (dest) => dest.postalCodes,
          mapFrom((src) => src.postalCodes),
        ),
      );

      createMap(
        mapper,
        ZoneEntity,
        ZoneDomain,
        forMember(
          (dest) => dest.postalCodes,
          mapFrom((src) => src.postalCodes),
        ),
      );

      createMap(
        mapper,
        ZoneDomain,
        GetZoneDto,
        forMember(
          (dest) => dest.postalCodes,
          mapFrom((src) => src.postalCodes),
        ),
      );
    };
  }
}
