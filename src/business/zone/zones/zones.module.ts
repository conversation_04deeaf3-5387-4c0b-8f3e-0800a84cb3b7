import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { ZonesService } from './zones.service';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { RelationalZonePersistenceModule } from './infrastructure/relational-persistence.module';
import { ZonesController } from './zones.controller';
import { ZoneProfile } from './infrastructure/mappers/zone.profile';
import { RelationalZoneTablePersistenceModule } from '../zone-tables/infrastructure/relational-persistence.module';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { ZoneFilterConfig } from './zone-filter.config';

@Module({
  imports: [
    RelationalZonePersistenceModule,
    RelationalZoneTablePersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [ZonesController],
  providers: [
    ZonesService,
    ZoneProfile,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(ZoneFilterConfig()),
    },
  ],
  exports: [ZonesService],
})
export class ZonesModule {}
