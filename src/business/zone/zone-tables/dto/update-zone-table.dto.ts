import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsArray,
  ArrayNotEmpty,
  IsUUID,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';
import { CreateZoneTableValueDto } from './create-zone-table.dto';

export class UpdateZoneTableDto {
  @AutoMap()
  @ApiProperty({ example: 'Transit app zone table' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ type: () => [UpdateZoneTableValueDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => UpdateZoneTableValueDto)
  zoneTableValues: UpdateZoneTableValueDto[];
}

export class UpdateZoneTableValueDto extends CreateZoneTableValueDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsOptional()
  @IsUUID()
  id: string;
}
