import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';
import { CreateZoneTableValueDto } from './create-zone-table.dto';

export class GetZoneTableValueDto extends CreateZoneTableValueDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  createdBy: string;

  @AutoMap()
  @ApiProperty({ example: 'User2' })
  updatedBy: string;
}

export class GetZoneTableWithoutValuesDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: 'Transit app zone table' })
  name: string;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  createdBy: string;

  @AutoMap()
  @ApiProperty({ example: 'User2' })
  updatedBy: string;
}

export class GetAllZoneTableDto extends PageResponse {
  @ApiProperty({ type: [GetZoneTableWithoutValuesDto] })
  data: GetZoneTableWithoutValuesDto[];
}

export class GetZoneTableDto extends GetZoneTableWithoutValuesDto {
  @AutoMap()
  @ApiProperty({ type: () => [GetZoneTableValueDto] })
  zoneTableValues: GetZoneTableValueDto[];
}
