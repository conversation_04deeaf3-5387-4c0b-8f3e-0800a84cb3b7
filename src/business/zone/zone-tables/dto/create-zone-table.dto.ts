import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsArray,
  ArrayNotEmpty,
  IsUUID,
  IsNumber,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class CreateZoneTableDto {
  @AutoMap()
  @ApiProperty({ example: 'Transit app zone table' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ type: () => [CreateZoneTableValueDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateZoneTableValueDto)
  zoneTableValues: CreateZoneTableValueDto[];
}

export class CreateZoneTableValueDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsUUID()
  @IsNotEmpty()
  originZoneId: string;

  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aadb' })
  @IsUUID()
  @IsNotEmpty()
  destinationZoneId: string;

  @AutoMap()
  @ApiProperty({ example: 33.5 })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => (value && value !== '' ? Number(value) : undefined))
  value: number;
}
