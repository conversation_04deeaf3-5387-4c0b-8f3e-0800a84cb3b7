import { Injectable } from '@nestjs/common';
import { PageParamsRequest } from '@utils/page-params-request';
import { ZoneTableRepository } from './infrastructure/repositories/zone-table.repository';
import { ZoneTableDomain } from './domain/zone-table';
import { ZoneRepository } from '../zones/infrastructure/repositories/zone.repository';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import {
  ZoneTableNotFoundException,
  ZoneTableAlreadyExistsException,
  ZoneTableOperationNotAllowedException,
  ZoneTableZoneNotFoundException,
} from '@app/utils/errors/exceptions/zone-exceptions';

@Injectable()
export class ZoneTableService {
  constructor(
    private readonly zoneTableRepository: ZoneTableRepository,
    private readonly zoneRepository: ZoneRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) { }

  async create(zoneTableDomain: ZoneTableDomain): Promise<ZoneTableDomain> {
    const existingTable = await this.zoneTableRepository.findOne({
      name: zoneTableDomain.name,
      tenantId: zoneTableDomain.tenantId,
    });
    if (existingTable) {
      throw new ZoneTableAlreadyExistsException(zoneTableDomain.name);
    }

    if (!zoneTableDomain.zoneTableValues?.length) {
      throw new ZoneTableOperationNotAllowedException(
        '',
        'create',
        'Zone table must have at least one value',
      );
    }

    const zoneIds = new Set<string>();
    zoneTableDomain.zoneTableValues.forEach((value) => {
      zoneIds.add(value.originZoneId);
      zoneIds.add(value.destinationZoneId);
    });

    const zoneValidationResult = await this.validateZones(Array.from(zoneIds));
    if (!zoneValidationResult) {
      throw new ZoneTableZoneNotFoundException();
    }

    const zoneTable = await this.zoneTableRepository.create(zoneTableDomain);
    return zoneTable;
  }

  async duplicateZoneTable(
    zoneTableId: ZoneTableDomain['id'],
  ): Promise<ZoneTableDomain> {
    const zoneTableDomain = await this.zoneTableRepository.findOne({
      id: zoneTableId,
    });
    if (!zoneTableDomain) {
      throw new ZoneTableNotFoundException(zoneTableId);
    }

    const duplicateZoneTable = this.mapper.map(
      zoneTableDomain,
      ZoneTableDomain,
      ZoneTableDomain,
    );

    const zoneTable = await this.zoneTableRepository.create(duplicateZoneTable);
    return zoneTable;
  }

  async getZoneTableList(
    queryParams: PageParamsRequest,
    name: string,
    tenantId: string,
  ): Promise<{ totalCount: number; data: ZoneTableDomain[] }> {
    const zoneTableDomain = await this.zoneTableRepository.find(
      queryParams,
      name,
      tenantId,
    );
    return zoneTableDomain;
  }

  async getZoneTableDetails(
    zoneTableId: ZoneTableDomain['id'],
  ): Promise<ZoneTableDomain> {
    const zoneTableDomain = await this.zoneTableRepository.findOne({
      id: zoneTableId,
    });
    if (!zoneTableDomain) {
      throw new ZoneTableNotFoundException(zoneTableId);
    }

    return zoneTableDomain;
  }

  async updateZoneTableDetails(
    zoneTableDomain: ZoneTableDomain,
  ): Promise<void> {
    const existingTable = await this.zoneTableRepository.findOne({
      id: zoneTableDomain.id,
    });
    if (!existingTable) {
      throw new ZoneTableNotFoundException(zoneTableDomain.id);
    }

    if (existingTable.name !== zoneTableDomain.name) {
      const tableWithSameName = await this.zoneTableRepository.findOne({
        name: zoneTableDomain.name,
      });
      if (tableWithSameName && tableWithSameName.id !== zoneTableDomain.id) {
        throw new ZoneTableAlreadyExistsException(zoneTableDomain.name);
      }
    }

    if (!zoneTableDomain.zoneTableValues?.length) {
      throw new ZoneTableOperationNotAllowedException(
        '',
        'update',
        'Zone table must have at least one value',
      );
    }

    const zoneIds = new Set<string>();
    zoneTableDomain.zoneTableValues.forEach((value) => {
      zoneIds.add(value.originZoneId);
      zoneIds.add(value.destinationZoneId);
    });

    const zoneValidationResult = await this.validateZones(Array.from(zoneIds));
    if (!zoneValidationResult) {
      throw new ZoneTableZoneNotFoundException();
    }

    await this.zoneTableRepository.update(zoneTableDomain);
    return;
  }

  async deleteZoneTable(zoneTableId: ZoneTableDomain['id']): Promise<void> {
    const zoneTable = await this.zoneTableRepository.findOne({
      id: zoneTableId,
    });
    if (!zoneTable) {
      throw new ZoneTableNotFoundException(zoneTableId);
    }

    zoneTable.isDeleted = true;
    zoneTable.deletedAt = new Date();
    await this.zoneTableRepository.update(zoneTable);
    return;
  }

  async validateZones(zoneIds: string[]): Promise<boolean> {
    const existingZones = await this.zoneRepository.findByIds(zoneIds);
    const existingZoneIds = new Set(existingZones.map((zone) => zone.id));

    const invalidZoneIds = zoneIds.filter((id) => !existingZoneIds.has(id));
    if (invalidZoneIds.length) {
      //To log (`Invalid Zone IDs: ${invalidZoneIds.join(', ')}`);
      return false;
    }
    return true;
  }
}
