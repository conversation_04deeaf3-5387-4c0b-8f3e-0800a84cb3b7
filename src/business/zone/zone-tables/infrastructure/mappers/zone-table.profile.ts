import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { ZoneTableDomain, ZoneTableValueDomain } from '../../domain/zone-table';
import {
  CreateZoneTableDto,
  CreateZoneTableValueDto,
} from '../../dto/create-zone-table.dto';
import {
  GetZoneTableWithoutValuesDto,
  GetZoneTableDto,
  GetZoneTableValueDto,
} from '../../dto/get-zone-table.dto';
import { ZoneTableEntity } from '../entities/zone-table.entity';
import { ZoneTableValueEntity } from '../entities/zone-table-value.entity';
import {
  UpdateZoneTableDto,
  UpdateZoneTableValueDto,
} from '../../dto/update-zone-table.dto';

@Injectable()
export class ZoneTableProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      //ZoneTable Value Mapping
      createMap(mapper, CreateZoneTableValueDto, ZoneTableValueDomain);
      createMap(mapper, UpdateZoneTableValueDto, ZoneTableValueDomain);
      createMap(mapper, ZoneTableValueEntity, ZoneTableValueDomain);
      createMap(mapper, ZoneTableValueDomain, GetZoneTableValueDto);
      createMap(
        mapper,
        ZoneTableValueDomain,
        ZoneTableValueDomain,
        forMember(
          (dest) => dest.id,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.createdAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.createdBy,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedBy,
          mapFrom(() => undefined),
        ),
      );

      //ZoneTable Mapping
      createMap(
        mapper,
        CreateZoneTableDto,
        ZoneTableDomain,
        forMember(
          (dest) => dest.zoneTableValues,
          mapFrom((src) =>
            mapper.mapArray(
              src.zoneTableValues,
              CreateZoneTableValueDto,
              ZoneTableValueDomain,
            ),
          ),
        ),
      );

      createMap(
        mapper,
        UpdateZoneTableDto,
        ZoneTableDomain,
        forMember(
          (dest) => dest.zoneTableValues,
          mapFrom((src) =>
            mapper.mapArray(
              src.zoneTableValues,
              UpdateZoneTableValueDto,
              ZoneTableValueDomain,
            ),
          ),
        ),
      );

      createMap(mapper, ZoneTableDomain, ZoneTableEntity);
      createMap(mapper, ZoneTableEntity, ZoneTableDomain);
      createMap(mapper, ZoneTableDomain, GetZoneTableWithoutValuesDto);
      createMap(
        mapper,
        ZoneTableDomain,
        GetZoneTableDto,
        forMember(
          (dest) => dest.zoneTableValues,
          mapFrom((src) =>
            mapper.mapArray(
              src.zoneTableValues,
              ZoneTableValueDomain,
              GetZoneTableValueDto,
            ),
          ),
        ),
      );

      createMap(
        mapper,
        ZoneTableDomain,
        ZoneTableDomain,
        forMember(
          (dest) => dest.id,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => `${src.name} - Copy`),
        ),
        forMember(
          (dest) => dest.createdAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.createdBy,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedBy,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.zoneTableValues,
          mapFrom((src) =>
            mapper.mapArray(
              src.zoneTableValues,
              ZoneTableValueDomain,
              ZoneTableValueDomain,
            ),
          ),
        ),
      );
    };
  }
}
