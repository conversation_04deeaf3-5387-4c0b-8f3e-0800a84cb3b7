import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import {
  FindOperator,
  FindOptionsWhere,
  ILike,
  In,
  IsNull,
  Repository,
} from 'typeorm';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { PageParamsRequest } from '@utils/page-params-request';
import { ZoneTableEntity } from '../entities/zone-table.entity';
import { ZoneTableDomain, ZoneTableValueDomain } from '../../domain/zone-table';
import { ZoneTableValueEntity } from '../entities/zone-table-value.entity';
import { ZoneDomain } from '../../../zones/domain/zone';

@Injectable()
export class ZoneTableRepository {
  constructor(
    @InjectRepository(ZoneTableEntity)
    private readonly zoneTableRepository: Repository<ZoneTableEntity>,
    @InjectRepository(ZoneTableValueEntity)
    private readonly zoneTableValueRepository: Repository<ZoneTableValueEntity>,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(data: ZoneTableDomain): Promise<ZoneTableDomain> {
    const requestEntity = this.mapper.map(
      data,
      ZoneTableDomain,
      ZoneTableEntity,
    );
    const zoneTableEntity = await this.zoneTableRepository.save(
      this.zoneTableRepository.create(requestEntity),
    );

    if (data.zoneTableValues?.length) {
      const zoneTableValues = data.zoneTableValues.map((value) => ({
        ...value,
        zoneTableId: zoneTableEntity.id,
      }));

      const zoneTableValueEntities =
        this.zoneTableValueRepository.create(zoneTableValues);
      await this.zoneTableValueRepository.save(zoneTableValueEntities);
    }

    const responseDomain = this.mapper.map(
      zoneTableEntity,
      ZoneTableEntity,
      ZoneTableDomain,
    );
    return responseDomain;
  }

  async find(
    queryParams: PageParamsRequest,
    name: string,
    tenantId: string,
  ): Promise<{ totalCount: number; data: ZoneTableDomain[] }> {
    const where: {
      isDeleted: boolean;
      priceSetId: FindOperator<any>;
      name?: FindOperator<string>;
      tenantId: string;
    } = {
      isDeleted: false,
      priceSetId: IsNull(),
      tenantId,
    };

    if (name) {
      where.name = ILike(`%${name}%`);
    }

    const [zoneEntities, totalCount] =
      await this.zoneTableRepository.findAndCount({
        where,
        skip: (queryParams.pageNumber - 1) * queryParams.pageSize,
        take: queryParams.pageSize,
      });
    const data = this.mapper.mapArray(
      zoneEntities,
      ZoneTableEntity,
      ZoneTableDomain,
    );
    return { totalCount, data };
  }

  async findOne(
    fields: EntityCondition<ZoneTableDomain>,
  ): Promise<NullableType<ZoneTableDomain>> {
    const requestEntity: Partial<ZoneTableEntity> = this.mapper.map(
      fields,
      ZoneTableDomain,
      ZoneTableEntity,
    );

    const zoneTableEntity = await this.zoneTableRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<ZoneTableEntity>),
        isDeleted: false,
      },
    });

    if (!zoneTableEntity) {
      return null;
    }

    const responseDomain = this.mapper.map(
      zoneTableEntity,
      ZoneTableEntity,
      ZoneTableDomain,
    );

    const zoneTableValueEntity = await this.zoneTableValueRepository.find({
      where: { zoneTableId: zoneTableEntity.id },
    });

    responseDomain.zoneTableValues = this.mapper.mapArray(
      zoneTableValueEntity,
      ZoneTableValueEntity,
      ZoneTableValueDomain,
    );

    return responseDomain;
  }

  async update(payload: ZoneTableDomain): Promise<ZoneTableDomain> {
    const requestEntity = this.mapper.map(
      payload,
      ZoneTableDomain,
      ZoneTableEntity,
    );
    const zoneTableEntity = await this.zoneTableRepository.save(requestEntity);

    if (!payload.isDeleted && payload.zoneTableValues?.length) {
      const existingIds = payload.zoneTableValues
        .filter((value) => value.id)
        .map((value) => value.id);

      const existingZoneTableValues =
        await this.zoneTableValueRepository.findBy({ id: In(existingIds) });

      const existingZoneTableValuesMap = new Map(
        existingZoneTableValues.map((value) => [value.id, value]),
      );

      const updatedValues: ZoneTableValueEntity[] = [];
      const newValues: ZoneTableValueEntity[] = [];

      for (const value of payload.zoneTableValues) {
        if (value.id && existingZoneTableValuesMap.has(value.id)) {
          const existingValue = existingZoneTableValuesMap.get(value.id);
          if (existingValue) {
            Object.assign(existingValue, value);
            updatedValues.push(existingValue);
          }
        } else {
          const newValue = this.zoneTableValueRepository.create({
            ...value,
            zoneTableId: zoneTableEntity.id,
          });
          newValues.push(newValue);
        }
      }

      if (updatedValues.length) {
        await this.zoneTableValueRepository.save(updatedValues);
      }

      if (newValues.length) {
        await this.zoneTableValueRepository.save(newValues);
      }
    }

    const responseDomain = this.mapper.map(
      zoneTableEntity,
      ZoneTableEntity,
      ZoneTableDomain,
    );
    return responseDomain;
  }

  async deleteByZoneId(zoneId: ZoneDomain['id']): Promise<void> {
    await this.zoneTableValueRepository.delete({
      originZoneId: zoneId,
    });

    await this.zoneTableValueRepository.delete({
      destinationZoneId: zoneId,
    });
  }
}
