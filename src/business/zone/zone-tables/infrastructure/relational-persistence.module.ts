import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ZoneTableEntity } from './entities/zone-table.entity';
import { ZoneTableRepository } from './repositories/zone-table.repository';
import { ZoneTableValueEntity } from './entities/zone-table-value.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ZoneTableEntity, ZoneTableValueEntity])],
  providers: [ZoneTableRepository],
  exports: [ZoneTableRepository],
})
export class RelationalZoneTablePersistenceModule {}
