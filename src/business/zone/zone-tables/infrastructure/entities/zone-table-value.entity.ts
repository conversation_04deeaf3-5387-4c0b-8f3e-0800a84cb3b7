import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { ZoneEntity } from '../../../zones/infrastructure/entities/zone.entity';
import { ZoneTableEntity } from './zone-table.entity';

@Entity('zone_table_value')
export class ZoneTableValueEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @ManyToOne(() => ZoneTableEntity)
  @JoinColumn({ name: 'zoneTableId' })
  @Index()
  zoneTable: ZoneTableEntity;

  @AutoMap()
  @Column('uuid')
  zoneTableId: string;

  @AutoMap()
  @ManyToOne(() => ZoneEntity)
  @JoinColumn({ name: 'originZoneId' })
  @Index()
  originZone: ZoneEntity;

  @AutoMap()
  @Column('uuid')
  originZoneId: string;

  @AutoMap()
  @ManyToOne(() => ZoneEntity)
  @JoinColumn({ name: 'destinationZoneId' })
  @Index()
  destinationZone: ZoneEntity;

  @AutoMap()
  @Column('uuid')
  destinationZoneId: string;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  value: number;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;
}
