import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { PageParamsRequest } from '@utils/page-params-request';
import { ZoneTableService } from './zone-table.service';
import { CreateZoneTableDto } from './dto/create-zone-table.dto';
import { ZoneTableDomain } from './domain/zone-table';
import {
  GetAllZoneTableDto,
  GetZoneTableWithoutValuesDto,
  GetZoneTableDto,
} from './dto/get-zone-table.dto';
import { UpdateZoneTableDto } from './dto/update-zone-table.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { ZoneTableOperationNotAllowedException } from '../../../utils/errors/exceptions/zone-exceptions';

@ApiTags('Business - Zone - Tables')
@Controller({
  path: 'zoneTable',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export default class ZoneTableController {
  constructor(
    private readonly zoneTableService: ZoneTableService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Zone Table' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createZoneTableDto: CreateZoneTableDto,
  ): Promise<ZoneTableDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new ZoneTableOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      const zoneTableDomain = this.mapper.map(
        createZoneTableDto,
        CreateZoneTableDto,
        ZoneTableDomain,
      );
      zoneTableDomain.tenantId = tenantId;
      const zoneTable = await this.zoneTableService.create(zoneTableDomain);
      return zoneTable;
    } catch (error) {
      throw error;
    }
  }

  @Post(':zoneTableId/duplicate')
  @ApiOperation({ summary: 'Duplicate Zone Table by Zone Table Id' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Duplicated' })
  async duplicateZoneTable(
    @Param('zoneTableId') zoneTableId: string,
  ): Promise<ZoneTableDomain> {
    try {
      const zoneTable =
        await this.zoneTableService.duplicateZoneTable(zoneTableId);
      return zoneTable;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all zone tables with pagination' })
  @ApiQuery({ name: 'searchTerm', required: false })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllZoneTableDto })
  async getZoneList(
    @Req() request: RequestWithUser,
    @Query() queryParams: PageParamsRequest,
    @Query('searchTerm') name: string,
  ): Promise<GetAllZoneTableDto> {
    try {
      const tenantId = request.tenantContext?.tenantId;

      if (!tenantId) {
        throw new ZoneTableOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getZoneTablesList',
          'Insufficient tenant access permissions',
        );
      }

      const responseDomains = await this.zoneTableService.getZoneTableList(
        queryParams,
        name,
        tenantId,
      );
      const mappedData = this.mapper.mapArray(
        responseDomains.data,
        ZoneTableDomain,
        GetZoneTableWithoutValuesDto,
      );
      const total = responseDomains.totalCount;
      const page = queryParams.pageNumber || 1;
      const limit = queryParams.pageSize || 10;
      const totalPages = Math.ceil(total / limit);

      const response: GetAllZoneTableDto = {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get(':zoneTableId')
  @ApiOperation({ summary: 'Find zone table by zone table Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetZoneTableDto })
  async getZoneDetails(
    @Param('zoneTableId') zoneTableId: string,
  ): Promise<GetZoneTableDto> {
    try {
      const responseDomain =
        await this.zoneTableService.getZoneTableDetails(zoneTableId);

      const response = this.mapper.map(
        responseDomain,
        ZoneTableDomain,
        GetZoneTableDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':zoneTableId')
  @ApiOperation({ summary: 'Update zone table by zone table Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateZoneTableDetails(
    @Param('zoneTableId') zoneTableId: string,
    @Body() updateZoneTableDto: UpdateZoneTableDto,
  ): Promise<void> {
    try {
      const zoneTable = this.mapper.map(
        updateZoneTableDto,
        UpdateZoneTableDto,
        ZoneTableDomain,
      );
      zoneTable.id = zoneTableId;
      await this.zoneTableService.updateZoneTableDetails(zoneTable);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':zoneTableId')
  @ApiOperation({ summary: 'Soft-delete zone table by zone table Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteZone(@Param('zoneTableId') zoneTableId: string): Promise<void> {
    try {
      await this.zoneTableService.deleteZoneTable(zoneTableId);
      return;
    } catch (error) {
      throw error;
    }
  }
}
