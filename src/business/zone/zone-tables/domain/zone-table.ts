import { AutoMap } from '@automapper/classes';

export class ZoneTableDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  name: string;

  @AutoMap()
  zoneTableValues: Array<ZoneTableValueDomain>;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}

export class ZoneTableValueDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  originZoneId: string;

  @AutoMap()
  destinationZoneId: string;

  @AutoMap()
  value: number;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}
