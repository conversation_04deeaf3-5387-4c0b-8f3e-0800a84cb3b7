import { Injectable } from '@nestjs/common';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { DriverZoneAssignmentDomain } from './domain/driver-zone-assignment';
import { DriverZoneAssignmentRepository } from './infrastructure/repositories/driver-zone-assignment.repository';
import {
  DriverZoneAssignmentNotFoundException,
  DriverNotFoundException,
  ZoneNotFoundException,
} from '@utils/errors/exceptions/driver-zone-assignment-exceptions';
import {
  ZoneAssignmentError,
  ZoneAssignmentErrorType,
} from './domain/driver-zone-assignment.types';
import { ZonesService } from '../zones/zones.service';
import { DriverService } from '../../user/drivers/driver.service';

@Injectable()
export class DriverZoneAssignmentService {
  constructor(
    private readonly driverZoneAssignmentRepository: DriverZoneAssignmentRepository,
    private readonly zonesService: ZonesService,
    private readonly driverService: DriverService,
  ) {}

  /**
   * Assign a zone to a driver
   * If the zone is already assigned to another driver, it will be reassigned
   */
  async assignZone(
    driverId: string,
    zoneId: string,
    tenantId: string,
    userId?: string,
  ): Promise<DriverZoneAssignmentDomain> {
    // Verify driver exists
    const driver = await this.driverService.getDriverDetails(driverId);
    if (!driver) {
      throw new DriverNotFoundException(driverId);
    }

    // Verify zone exists
    const zone = await this.zonesService.getZoneDetails(zoneId);
    if (!zone) {
      throw new ZoneNotFoundException(zoneId);
    }

    // Check if zone is already assigned to a driver
    const existingAssignment =
      await this.driverZoneAssignmentRepository.findByZoneId(zoneId);
    if (existingAssignment) {
      // Delete the existing assignment, which soft-deletes it
      await this.driverZoneAssignmentRepository.deleteByZoneId(zoneId);
    }

    // Create a new assignment
    const assignment = new DriverZoneAssignmentDomain();
    assignment.driverId = driverId;
    assignment.zoneId = zoneId;
    assignment.tenantId = tenantId;
    assignment.createdBy = userId ?? '';
    assignment.updatedBy = userId ?? '';

    return this.driverZoneAssignmentRepository.create(assignment);
  }

  /**
   * Get a paginated list of driver zone assignments
   */
  async getDriverZoneAssignmentList(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<DriverZoneAssignmentDomain>> {
    return this.driverZoneAssignmentRepository.find(filter, tenantId);
  }

  /**
   * Get all zones assigned to a driver
   */
  async getZonesAssignedToDriver(
    driverId: string,
  ): Promise<DriverZoneAssignmentDomain[]> {
    // Verify driver exists
    const driver = await this.driverService.getDriverDetails(driverId);
    if (!driver) {
      throw new DriverNotFoundException(driverId);
    }

    return this.driverZoneAssignmentRepository.findByDriverId(driverId);
  }

  /**
   * Get the driver assigned to a zone
   */
  async getDriverAssignedToZone(
    zoneId: string,
  ): Promise<DriverZoneAssignmentDomain | null> {
    // Verify zone exists
    const zone = await this.zonesService.getZoneDetails(zoneId);
    if (!zone) {
      throw new ZoneNotFoundException(zoneId);
    }

    return this.driverZoneAssignmentRepository.findByZoneId(zoneId);
  }

  /**
   * Find drivers available for a route from pickup zone to delivery zone
   * Returns an error object if no drivers are available
   */
  async findAvailableDrivers(
    pickupZoneId: string,
    deliveryZoneId: string,
  ): Promise<{ availableDrivers: any[]; error?: ZoneAssignmentError }> {
    // Verify both zones exist
    try {
      await this.zonesService.getZoneDetails(pickupZoneId);
    } catch {
      return {
        availableDrivers: [],
        error: {
          code: ZoneAssignmentErrorType.PICKUP_DRIVER_NOT_FOUND,
          message: 'No driver found for pickup zone',
        },
      };
    }

    try {
      await this.zonesService.getZoneDetails(deliveryZoneId);
    } catch {
      return {
        availableDrivers: [],
        error: {
          code: ZoneAssignmentErrorType.DELIVERY_DRIVER_NOT_FOUND,
          message: 'No driver found for delivery zone',
        },
      };
    }

    // Find drivers assigned to both zones
    const pickupZoneAssignment =
      await this.driverZoneAssignmentRepository.findByZoneId(pickupZoneId);
    const deliveryZoneAssignment =
      await this.driverZoneAssignmentRepository.findByZoneId(deliveryZoneId);

    if (!pickupZoneAssignment) {
      return {
        availableDrivers: [],
        error: {
          code: ZoneAssignmentErrorType.PICKUP_DRIVER_NOT_FOUND,
          message: 'No driver found for pickup zone',
        },
      };
    }

    if (!deliveryZoneAssignment) {
      return {
        availableDrivers: [],
        error: {
          code: ZoneAssignmentErrorType.DELIVERY_DRIVER_NOT_FOUND,
          message: 'No driver found for delivery zone',
        },
      };
    }

    // Check if same driver is assigned to both zones
    if (pickupZoneAssignment.driverId === deliveryZoneAssignment.driverId) {
      // Get driver details
      const driver = await this.driverService.getDriverDetails(
        pickupZoneAssignment.driverId,
      );
      if (!driver) {
        return {
          availableDrivers: [],
          error: {
            code: ZoneAssignmentErrorType.DRIVER_NOT_FOUND,
            message:
              'Driver found in system but details could not be retrieved',
          },
        };
      }

      // Get all zones assigned to this driver
      const driverAssignments =
        await this.driverZoneAssignmentRepository.findByDriverId(driver.id);
      const zoneIds = driverAssignments.map((assignment) => assignment.zoneId);

      return {
        availableDrivers: [
          {
            id: driver.id,
            name: driver.name,
            email: driver.email,
            assignedZoneIds: zoneIds,
          },
        ],
      };
    }

    return {
      availableDrivers: [],
      error: {
        code: ZoneAssignmentErrorType.NO_AVAILABLE_DRIVERS,
        message:
          'No single driver available for both pickup and delivery zones',
      },
    };
  }

  /**
   * Delete an assignment by its ID
   */
  async deleteAssignment(assignmentId: string): Promise<void> {
    const assignment = await this.driverZoneAssignmentRepository.findOne({
      id: assignmentId,
    });

    if (!assignment) {
      throw new DriverZoneAssignmentNotFoundException(assignmentId);
    }

    // Soft delete the assignment
    assignment.isDeleted = true;
    assignment.deletedAt = new Date();
    await this.driverZoneAssignmentRepository.update(assignment);
  }

  /**
   * Delete all assignments for a driver
   */
  async deleteAssignmentsByDriverId(driverId: string): Promise<void> {
    await this.driverZoneAssignmentRepository.deleteByDriverId(driverId);
  }

  /**
   * Delete an assignment for a zone
   */
  async deleteAssignmentByZoneId(zoneId: string): Promise<void> {
    await this.driverZoneAssignmentRepository.deleteByZoneId(zoneId);
  }
}
