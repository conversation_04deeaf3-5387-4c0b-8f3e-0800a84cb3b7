import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class AvailableDriverDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: 'John Doe' })
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  assignedZoneIds: string[];
}

export class FindAvailableDriversDto {
  @AutoMap()
  @ApiProperty({
    description: 'Pickup Zone ID',
    example: '66fedab8-7820-4cca-b8ca-cf59ade1aada',
  })
  @IsNotEmpty()
  @IsUUID('4')
  pickupZoneId: string;

  @AutoMap()
  @ApiProperty({
    description: 'Delivery Zone ID',
    example: '66fedab8-7820-4cca-b8ca-cf59ade1aada',
  })
  @IsNotEmpty()
  @IsUUID('4')
  deliveryZoneId: string;
}

export class DriverAvailabilityErrorDto {
  @ApiProperty({
    example: 'PICKUP_DRIVER_NOT_FOUND',
    enum: [
      'PICKUP_DRIVER_NOT_FOUND',
      'DELIVERY_DRIVER_NOT_FOUND',
      'DRIVER_NOT_FOUND',
      'NO_AVAILABLE_DRIVERS',
    ],
  })
  code: string;

  @ApiProperty({
    example: 'No driver found for pickup zone',
  })
  message: string;
}

export class AvailableDriversResponseDto {
  @ApiProperty({
    description: 'Available drivers for the route',
    type: [AvailableDriverDto],
  })
  availableDrivers: AvailableDriverDto[];

  @ApiProperty({
    description: 'Error information if drivers are not available',
    type: DriverAvailabilityErrorDto,
    required: false,
  })
  error?: DriverAvailabilityErrorDto;
}
