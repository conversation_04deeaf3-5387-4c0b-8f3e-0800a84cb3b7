import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class AssignZoneDto {
  @AutoMap()
  @ApiProperty({
    description: 'Driver ID',
    example: '66fedab8-7820-4cca-b8ca-cf59ade1aada',
  })
  @IsNotEmpty()
  @IsUUID('4')
  driverId: string;

  @AutoMap()
  @ApiProperty({
    description: 'Zone ID',
    example: '66fedab8-7820-4cca-b8ca-cf59ade1aada',
  })
  @IsNotEmpty()
  @IsUUID('4')
  zoneId: string;
}
