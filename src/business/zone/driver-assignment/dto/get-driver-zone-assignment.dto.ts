import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';

export class GetDriverZoneAssignmentDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  driverId: string;

  @AutoMap()
  @ApiProperty({ example: 'John Doe' })
  driverName: string;

  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  zoneId: string;

  @AutoMap()
  @ApiProperty({ example: 'Downtown' })
  zoneName: string;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  createdBy: string;

  @AutoMap()
  @ApiProperty({ example: 'User2' })
  updatedBy: string;
}

export class GetAllDriverZoneAssignmentsDto extends PageResponse {
  @ApiProperty({ type: [GetDriverZoneAssignmentDto] })
  data: GetDriverZoneAssignmentDto[];
}
