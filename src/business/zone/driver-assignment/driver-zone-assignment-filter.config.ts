import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const DriverZoneAssignmentFilterConfig = () => {
  const fields = [
    {
      fieldName: 'driverId',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid driver ID format',
      },
    },
    {
      fieldName: 'zoneId',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid zone ID format',
      },
    },
  ];

  const config = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    config.addField(fieldName, options);
  });

  return config
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields(['driverId', 'zoneId'])
    .setSortableFields(['driverId', 'zoneId', 'createdAt'])
    .build();
};
