import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { RelationalDriverZoneAssignmentPersistenceModule } from './infrastructure/relational-persistence.module';
import { DriverZoneAssignmentController } from './driver-zone-assignment.controller';
import { DriverZoneAssignmentService } from './driver-zone-assignment.service';
import { DriverZoneAssignmentProfile } from './infrastructure/mappers/driver-zone-assignment.profile';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { DriverZoneAssignmentFilterConfig } from './driver-zone-assignment-filter.config';
import { ZonesModule } from '../zones/zones.module';
import { DriverModule } from '../../user/drivers/driver.module';
import { TenantsModule } from '../../user/tenants/tenants.module';

@Module({
  imports: [
    RelationalDriverZoneAssignmentPersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    ZonesModule,
    DriverModule,
    TenantsModule,
  ],
  controllers: [DriverZoneAssignmentController],
  providers: [
    DriverZoneAssignmentService,
    DriverZoneAssignmentProfile,
    {
      provide: SecureFilterService,
      useFactory: () =>
        new SecureFilterService(DriverZoneAssignmentFilterConfig()),
    },
  ],
  exports: [DriverZoneAssignmentService],
})
export class DriverZoneAssignmentModule {}
