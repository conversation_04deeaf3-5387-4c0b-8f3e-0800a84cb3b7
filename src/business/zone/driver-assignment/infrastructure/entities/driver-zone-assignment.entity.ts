import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';

@Entity('driver_zone_assignments')
@Unique(['tenantId', 'zoneId', 'isDeleted']) // Ensures a zone is assigned to only one driver
export class DriverZoneAssignmentEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  driverId: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  zoneId: string;

  @AutoMap()
  @Column({ default: false })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt: Date;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;
}
