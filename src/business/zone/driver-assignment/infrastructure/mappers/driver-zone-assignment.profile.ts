import { createMap, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { DriverZoneAssignmentDomain } from '../../domain/driver-zone-assignment';
import { DriverZoneAssignmentEntity } from '../entities/driver-zone-assignment.entity';
import { GetDriverZoneAssignmentDto } from '../../dto/get-driver-zone-assignment.dto';
import { AssignZoneDto } from '../../dto/assign-zone.dto';

@Injectable()
export class DriverZoneAssignmentProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, AssignZoneDto, DriverZoneAssignmentDomain);

      createMap(mapper, DriverZoneAssignmentDomain, DriverZoneAssignmentEntity);

      createMap(mapper, DriverZoneAssignmentEntity, DriverZoneAssignmentDomain);

      createMap(mapper, DriverZoneAssignmentDomain, GetDriverZoneAssignmentDto);
    };
  }
}
