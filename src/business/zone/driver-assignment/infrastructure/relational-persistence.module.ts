import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DriverZoneAssignmentEntity } from './entities/driver-zone-assignment.entity';
import { DriverZoneAssignmentRepository } from './repositories/driver-zone-assignment.repository';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { DriverZoneAssignmentFilterConfig } from '../driver-zone-assignment-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([DriverZoneAssignmentEntity])],
  providers: [
    DriverZoneAssignmentRepository,
    {
      provide: SecureFilterService,
      useFactory: () =>
        new SecureFilterService(DriverZoneAssignmentFilterConfig()),
    },
  ],
  exports: [DriverZoneAssignmentRepository],
})
export class RelationalDriverZoneAssignmentPersistenceModule {}
