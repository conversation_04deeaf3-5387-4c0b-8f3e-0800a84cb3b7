import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, In, Repository } from 'typeorm';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { DriverZoneAssignmentEntity } from '../entities/driver-zone-assignment.entity';
import { DriverZoneAssignmentDomain } from '../../domain/driver-zone-assignment';
import { DriverZoneAssignmentFilterConfig } from '../../driver-zone-assignment-filter.config';

@Injectable()
export class DriverZoneAssignmentRepository {
  constructor(
    @InjectRepository(DriverZoneAssignmentEntity)
    private readonly driverZoneAssignmentRepository: Repository<DriverZoneAssignmentEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(
      DriverZoneAssignmentFilterConfig(),
    );
  }

  async create(
    data: DriverZoneAssignmentDomain,
  ): Promise<DriverZoneAssignmentDomain> {
    const requestEntity = this.mapper.map(
      data,
      DriverZoneAssignmentDomain,
      DriverZoneAssignmentEntity,
    );

    const entity = await this.driverZoneAssignmentRepository.save(
      this.driverZoneAssignmentRepository.create(requestEntity),
    );

    const responseDomain = this.mapper.map(
      entity,
      DriverZoneAssignmentEntity,
      DriverZoneAssignmentDomain,
    );

    return responseDomain;
  }

  async find(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<DriverZoneAssignmentDomain>> {
    const queryBuilder = this.driverZoneAssignmentRepository
      .createQueryBuilder('driverZoneAssignment')
      .where('driverZoneAssignment.isDeleted = false')
      .andWhere('driverZoneAssignment.tenantId = :tenantId', {
        tenantId,
      });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      DriverZoneAssignmentEntity,
      DriverZoneAssignmentDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findOne(
    fields: EntityCondition<DriverZoneAssignmentDomain>,
  ): Promise<NullableType<DriverZoneAssignmentDomain>> {
    const requestEntity: Partial<DriverZoneAssignmentEntity> = this.mapper.map(
      fields,
      DriverZoneAssignmentDomain,
      DriverZoneAssignmentEntity,
    );

    const entity = await this.driverZoneAssignmentRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<DriverZoneAssignmentEntity>),
        isDeleted: false,
      },
    });

    if (entity) {
      const responseDomain = this.mapper.map(
        entity,
        DriverZoneAssignmentEntity,
        DriverZoneAssignmentDomain,
      );
      return responseDomain;
    }

    return null;
  }

  async findByDriverId(
    driverId: string,
  ): Promise<DriverZoneAssignmentDomain[]> {
    const entities = await this.driverZoneAssignmentRepository.find({
      where: {
        driverId,
        isDeleted: false,
      },
    });

    return this.mapper.mapArray(
      entities,
      DriverZoneAssignmentEntity,
      DriverZoneAssignmentDomain,
    );
  }

  async findByZoneId(
    zoneId: string,
  ): Promise<DriverZoneAssignmentDomain | null> {
    const entity = await this.driverZoneAssignmentRepository.findOne({
      where: {
        zoneId,
        isDeleted: false,
      },
    });

    if (!entity) {
      return null;
    }

    return this.mapper.map(
      entity,
      DriverZoneAssignmentEntity,
      DriverZoneAssignmentDomain,
    );
  }

  async findByZoneIds(
    zoneIds: string[],
  ): Promise<DriverZoneAssignmentDomain[]> {
    if (zoneIds.length === 0) return [];

    const entities = await this.driverZoneAssignmentRepository.find({
      where: {
        zoneId: In(zoneIds),
        isDeleted: false,
      },
    });

    return this.mapper.mapArray(
      entities,
      DriverZoneAssignmentEntity,
      DriverZoneAssignmentDomain,
    );
  }

  async update(
    payload: DriverZoneAssignmentDomain,
  ): Promise<DriverZoneAssignmentDomain> {
    const requestEntity = this.mapper.map(
      payload,
      DriverZoneAssignmentDomain,
      DriverZoneAssignmentEntity,
    );

    const entity =
      await this.driverZoneAssignmentRepository.save(requestEntity);

    const responseDomain = this.mapper.map(
      entity,
      DriverZoneAssignmentEntity,
      DriverZoneAssignmentDomain,
    );

    return responseDomain;
  }

  async deleteByZoneId(zoneId: string): Promise<void> {
    const assignment = await this.driverZoneAssignmentRepository.findOne({
      where: {
        zoneId,
        isDeleted: false,
      },
    });

    if (assignment) {
      assignment.isDeleted = true;
      assignment.deletedAt = new Date();
      await this.driverZoneAssignmentRepository.save(assignment);
    }
  }

  async deleteByDriverId(driverId: string): Promise<void> {
    const assignments = await this.driverZoneAssignmentRepository.find({
      where: {
        driverId,
        isDeleted: false,
      },
    });

    if (assignments.length > 0) {
      for (const assignment of assignments) {
        assignment.isDeleted = true;
        assignment.deletedAt = new Date();
      }

      await this.driverZoneAssignmentRepository.save(assignments);
    }
  }
}
