import { AutoMap } from '@automapper/classes';

export class DriverZoneAssignmentDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  driverId: string;

  @AutoMap()
  zoneId: string;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}
