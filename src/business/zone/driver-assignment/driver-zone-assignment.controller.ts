import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { Request } from 'express';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { TenantValidationService } from '../../../business/user/tenants/tenant-validation.service';
import { DriverZoneAssignmentService } from './driver-zone-assignment.service';
import { DriverZoneAssignmentDomain } from './domain/driver-zone-assignment';
import { AssignZoneDto } from './dto/assign-zone.dto';
import {
  GetDriverZoneAssignmentDto,
  GetAllDriverZoneAssignmentsDto,
} from './dto/get-driver-zone-assignment.dto';
import {
  FindAvailableDriversDto,
  AvailableDriversResponseDto,
} from './dto/find-available-drivers.dto';
import { DriverZoneAssignmentOperationNotAllowedException } from '../../../utils/errors/exceptions/driver-zone-assignment-exceptions';

@ApiTags('Business - Zone - Driver Assignments')
@Controller({
  path: 'driver-zone-assignments',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class DriverZoneAssignmentController {
  constructor(
    private readonly driverZoneAssignmentService: DriverZoneAssignmentService,
    private readonly secureFilterService: SecureFilterService,
    private readonly tenantValidationService: TenantValidationService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Assign zone to driver' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({
    description: 'Created',
    type: GetDriverZoneAssignmentDto,
  })
  async assignZone(
    @Req() request: RequestWithUser,
    @Body() assignZoneDto: AssignZoneDto,
  ): Promise<GetDriverZoneAssignmentDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);

      const responseDomain = await this.driverZoneAssignmentService.assignZone(
        assignZoneDto.driverId,
        assignZoneDto.zoneId,
        tenantId,
        request.user?.id,
      );

      const response = this.mapper.map(
        responseDomain,
        DriverZoneAssignmentDomain,
        GetDriverZoneAssignmentDto,
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all driver zone assignments with pagination and filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllDriverZoneAssignmentsDto })
  async getDriverZoneAssignmentList(
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllDriverZoneAssignmentsDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);

      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );

      const result =
        await this.driverZoneAssignmentService.getDriverZoneAssignmentList(
          combinedFilter,
          tenantId,
        );

      const mappedData = this.mapper.mapArray(
        result.data,
        DriverZoneAssignmentDomain,
        GetDriverZoneAssignmentDto,
      );

      const response: GetAllDriverZoneAssignmentsDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };

      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('driver/:driverId')
  @ApiOperation({ summary: 'Get all zones assigned to a driver' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: [GetDriverZoneAssignmentDto] })
  async getZonesAssignedToDriver(
    @Param('driverId') driverId: string,
  ): Promise<GetDriverZoneAssignmentDto[]> {
    try {
      const responseDomains =
        await this.driverZoneAssignmentService.getZonesAssignedToDriver(
          driverId,
        );

      const responses = this.mapper.mapArray(
        responseDomains,
        DriverZoneAssignmentDomain,
        GetDriverZoneAssignmentDto,
      );

      return responses;
    } catch (error) {
      throw error;
    }
  }

  @Get('zone/:zoneId')
  @ApiOperation({ summary: 'Get driver assigned to a zone' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetDriverZoneAssignmentDto })
  async getDriverAssignedToZone(
    @Param('zoneId') zoneId: string,
  ): Promise<GetDriverZoneAssignmentDto | null> {
    try {
      const responseDomain =
        await this.driverZoneAssignmentService.getDriverAssignedToZone(zoneId);

      if (!responseDomain) {
        return null;
      }

      const response = this.mapper.map(
        responseDomain,
        DriverZoneAssignmentDomain,
        GetDriverZoneAssignmentDto,
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post('available-drivers')
  @ApiOperation({ summary: 'Find available drivers for a route' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: AvailableDriversResponseDto })
  async findAvailableDrivers(
    @Req() request: RequestWithUser,
    @Body() findAvailableDriversDto: FindAvailableDriversDto,
  ): Promise<AvailableDriversResponseDto> {
    try {
      const tenantId = request.tenantContext?.tenantId;
      if (!tenantId) {
        throw new DriverZoneAssignmentOperationNotAllowedException(
          request.user?.id || 'unknown',
          'findAvailableDrivers',
          'Insufficient tenant access permissions',
        );
      }

      const { pickupZoneId, deliveryZoneId } = findAvailableDriversDto;
      const result =
        await this.driverZoneAssignmentService.findAvailableDrivers(
          pickupZoneId,
          deliveryZoneId,
        );

      return result;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':assignmentId')
  @ApiOperation({ summary: 'Delete driver zone assignment by ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteAssignment(
    @Param('assignmentId') assignmentId: string,
  ): Promise<void> {
    try {
      await this.driverZoneAssignmentService.deleteAssignment(assignmentId);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete('driver/:driverId')
  @ApiOperation({ summary: 'Delete all zone assignments for a driver' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteDriverAssignments(
    @Param('driverId') driverId: string,
  ): Promise<void> {
    try {
      await this.driverZoneAssignmentService.deleteAssignmentsByDriverId(
        driverId,
      );
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete('zone/:zoneId')
  @ApiOperation({ summary: 'Delete zone assignment for a zone' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteZoneAssignment(@Param('zoneId') zoneId: string): Promise<void> {
    try {
      await this.driverZoneAssignmentService.deleteAssignmentByZoneId(zoneId);
      return;
    } catch (error) {
      throw error;
    }
  }
}
