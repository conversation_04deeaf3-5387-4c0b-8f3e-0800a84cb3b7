import { Module } from '@nestjs/common';
import { MobileAuthModule } from '../auth/auth.module';
import { MobileVehiclesController } from './vehicles.controller';
import { VehiclesMobileService } from './vehicle.service';
import { RelationalVehiclePersistenceModule } from '../../vehicle/vehicles/infrastructure/relational-persistence.module';
import { RelationalVehicleTypePersistenceModule } from '../../vehicle/vehicle-types/infrastructure/relational-persistence.module';
import { RelationalUserPersistenceModule } from '../../user/users/infrastructure/relational-persistence.module';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { VehicleProfile } from './mappers/vehicle.profile';

@Module({
  imports: [
    MobileAuthModule,
    RelationalVehiclePersistenceModule,
    RelationalVehicleTypePersistenceModule,
    RelationalUserPersistenceModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [MobileVehiclesController],
  providers: [VehiclesMobileService, VehicleProfile],
  exports: [],
})
export class MobileVehicleModule {}
