import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { VehicleEntity } from '../../../vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { GetAllVehiclesForMobileDto } from '../dto/get-vehicle.dto';

@Injectable()
export class VehicleProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        VehicleEntity,
        GetAllVehiclesForMobileDto,
        forMember(
          (dest) => dest.vehicleType,
          mapFrom((src) => src.vehicleType?.name),
        ),
      );
    };
  }
}
