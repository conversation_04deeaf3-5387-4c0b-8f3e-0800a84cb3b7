import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';

import { GetAllVehiclesForMobileDto } from './dto/get-vehicle.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UsersRepository } from '../../user/users/infrastructure/repositories/user.repository';
import { VehicleOperationNotAllowedException } from '../../../utils/errors/exceptions/vehicle-exceptions';
import { VehiclesMobileService } from './vehicle.service';

@ApiTags('Mobile - Vehicles')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'mobile/vehicles',
  version: '1',
})
export class MobileVehiclesController {
  constructor(
    private readonly vehiclesMobileService: VehiclesMobileService,
    private readonly userSRepository: UsersRepository,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all Vehicles ( no pagination)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllVehiclesForMobileDto })
  async getAllVehiclesMinimal(@Request() request: any) {
    try {
      const driverId = request.user.sub;
      const driver = await this.userSRepository.findById(driverId);

      if (!driver?.tenantId) {
        throw new VehicleOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getAllVehiclesMinimal(Mobile)',
          'Insufficient tenant access permissions',
        );
      }

      const vehicles = await this.vehiclesMobileService.getAllVehiclesMinimal(
        driver.tenantId,
      );

      return { data: vehicles };
    } catch (error) {
      throw error;
    }
  }
}
