import { Module } from '@nestjs/common';
import { MobileAuthModule } from './auth/auth.module';
import { MobileOrdersModule } from './orders/orders.module';
import { MobileTimeClockModule } from './timeclock/timeclock.module';
import { MobileVehicleModule } from './vehicle/vehicle.module';

@Module({
  imports: [
    MobileAuthModule,
    MobileOrdersModule,
    MobileTimeClockModule,
    MobileVehicleModule,
  ],
  exports: [
    MobileAuthModule,
    MobileOrdersModule,
    MobileTimeClockModule,
    MobileVehicleModule,
  ],
})
export class MobileModule {}
