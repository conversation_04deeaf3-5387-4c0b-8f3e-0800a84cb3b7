import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { MobileAuthController } from './auth.controller';
import { MobileAuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { RelationalDriverPersistenceModule } from '../../user/drivers/infrastructure/relational-persistence.module';
import { RelationalTenantPersistenceModule } from '../../user/tenants/infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [
    PassportModule,
    RelationalDriverPersistenceModule,
    RelationalTenantPersistenceModule,
    JwtModule.register({}),
  ],
  controllers: [MobileAuthController],
  providers: [MobileAuthService, JwtStrategy],
  exports: [MobileAuthService],
})
export class MobileAuthModule {}
