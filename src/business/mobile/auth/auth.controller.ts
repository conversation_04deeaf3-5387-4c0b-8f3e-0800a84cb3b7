import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Get,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiUnauthorizedResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { MobileAuthService } from './auth.service';
import { DriverLoginDto } from './dto/login.dto';
import {
  AuthTokenDto,
  DriverAuthResponseDto,
  DriverProfileResponseDto,
} from './dto/auth-response.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { RefreshTokenDto } from './dto/refresh-token.dto';

@ApiTags('Mobile - Auth')
@Controller({
  path: 'mobile/auth',
  version: '1',
})
export class MobileAuthController {
  constructor(private readonly authService: MobileAuthService) {}

  @Post('login')
  @ApiOperation({ summary: 'Driver login' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Login successful',
    type: DriverAuthResponseDto,
  })
  @ApiUnauthorizedResponse({ description: 'Invalid credentials' })
  async login(
    @Body() loginDto: DriverLoginDto,
  ): Promise<DriverAuthResponseDto> {
    const response = this.authService.login(loginDto);
    return response;
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get driver profile' })
  @ApiOkResponse({
    description: 'Driver profile retrieved successfully',
    type: DriverProfileResponseDto,
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  async getProfile(
    @Request() req: RequestWithUser,
  ): Promise<DriverProfileResponseDto> {
    const response = await this.authService.findDriverById(req.user.sub);
    return response;
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh JWT token' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: AuthTokenDto })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<AuthTokenDto> {
    const response = await this.authService.refreshAccessToken(
      refreshTokenDto.refreshToken,
    );
    return response;
  }
}
