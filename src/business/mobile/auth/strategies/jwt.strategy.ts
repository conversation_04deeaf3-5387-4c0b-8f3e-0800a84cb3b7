import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DriverRepository } from '../../../user/drivers/infrastructure/repository/driver.repository';
import { DriverNotFoundException } from '@utils/errors/exceptions/driver.exceptions';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'mobile-jwt') {
  constructor(
    private readonly configService: ConfigService,
    private readonly driverRepository: DriverRepository,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>(
        'auth.mobileJwtSecret',
        'mobile-driver-jwt-secret',
        { infer: true },
      ),
    });
  }

  async validate(payload: any) {
    const driver = await this.driverRepository.findOne(payload.sub);
    if (!driver) {
      throw new DriverNotFoundException(payload.sub);
    }
    return {
      sub: payload.sub,
      email: payload.email,
      name: payload.name,
      role: payload.role,
    };
  }
}
