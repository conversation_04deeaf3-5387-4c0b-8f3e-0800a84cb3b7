import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class DriverLoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @Transform(trimTransformer)
  email: string;

  @ApiProperty({ example: 'password123' })
  @IsString()
  @IsNotEmpty()
  password: string;
}
