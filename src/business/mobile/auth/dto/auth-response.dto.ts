import { ApiProperty } from '@nestjs/swagger';
import { UserStatus } from '../../../user/users/domain/user.types';
import { DriverStatus } from '../domain/driver';

export class AuthTokenDto {
  @ApiProperty({ example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  accessToken: string;

  @ApiProperty({ example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  refreshToken: string;

  @ApiProperty({ example: '3600' })
  expiresIn: number;
}

export class DriverProfileResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ example: 'John Smith' })
  fullName: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiProperty({ example: 'Blobstation' })
  companyName: string;

  @ApiProperty({ example: '+19898989898' })
  phoneNumber: string;

  @ApiProperty({ enum: UserStatus })
  status: UserStatus;

  @ApiProperty({ enum: UserStatus })
  driverStatus: DriverStatus;
}

export class DriverAuthResponseDto extends DriverProfileResponseDto {
  @ApiProperty({ type: AuthTokenDto })
  token: AuthTokenDto;
}
