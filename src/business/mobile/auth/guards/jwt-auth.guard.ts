import { ExecutionContext, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  TokenExpiredException,
  TokenInvalidException,
  TokenMissingException,
} from '@utils/errors/exceptions/auth.exceptions';

@Injectable()
export class JwtAuthGuard extends AuthGuard('mobile-jwt') {
  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();

    if (!request.headers.authorization) {
      throw new TokenMissingException('access');
    }

    return super.canActivate(context);
  }

  handleRequest(err, user, info) {
    // If there's an error or no user, throw appropriate exception
    if (err || !user) {
      if (info && info.message === 'jwt expired') {
        throw new TokenExpiredException('access');
      } else if (info && info.message === 'invalid token') {
        throw new TokenInvalidException('access');
      }
      throw err || new TokenInvalidException('access');
    }
    return user;
  }
}
