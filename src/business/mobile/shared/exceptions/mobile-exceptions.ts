import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

// Order Exceptions
export class OrderNotFoundException extends AppException {
  constructor(orderId: string) {
    super(
      `Order with ID ${orderId} not found`,
      ErrorCode.ORDER_NOT_FOUND,
      HttpStatus.NOT_FOUND,
    );
  }
}

export class OrderNotAssignedToDriverException extends AppException {
  constructor(orderId: string, driverId: string) {
    super(
      `Order with ID ${orderId} is not assigned to driver ${driverId}`,
      ErrorCode.ORDER_NOT_ASSIGNED_TO_DRIVER,
      HttpStatus.FORBIDDEN,
    );
  }
}

export class InvalidOrderStatusTransitionException extends AppException {
  constructor(currentStatus: string, newStatus: string) {
    super(
      `Invalid status transition from ${currentStatus} to ${newStatus}`,
      ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class OrderUpdateFailedException extends AppException {
  constructor(orderId: string, reason?: string) {
    super(
      `Failed to update order ${orderId}${reason ? `: ${reason}` : ''}`,
      ErrorCode.ORDER_UPDATE_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

export class OrderAlreadyAssignedException extends AppException {
  constructor(orderId: string, assignedDriverId: string) {
    super(
      `Order with ID ${orderId} is already assigned to driver ${assignedDriverId}`,
      ErrorCode.ORDER_ALREADY_ASSIGNED,
      HttpStatus.BAD_REQUEST,
    );
  }
}

// TimeClock Exceptions
export class TimeClockNotClockedInException extends AppException {
  constructor() {
    super(
      'Driver is not clocked in',
      ErrorCode.TIMECLOCK_NOT_CLOCKED_IN,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class TimeClockAlreadyClockedInException extends AppException {
  constructor() {
    super(
      'Driver is already clocked in',
      ErrorCode.TIMECLOCK_ALREADY_CLOCKED_IN,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class TimeClockNotOnBreakException extends AppException {
  constructor() {
    super(
      'Driver is not on break',
      ErrorCode.TIMECLOCK_NOT_ON_BREAK,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class TimeClockAlreadyOnBreakException extends AppException {
  constructor() {
    super(
      'Driver is already on break',
      ErrorCode.TIMECLOCK_ALREADY_ON_BREAK,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class TimeClockEntryNotFoundException extends AppException {
  constructor(entryId: string) {
    super(
      `Time clock entry with ID ${entryId} not found`,
      ErrorCode.TIMECLOCK_ENTRY_NOT_FOUND,
      HttpStatus.NOT_FOUND,
    );
  }
}

export class TimeClockVehicleRequiredException extends AppException {
  constructor() {
    super(
      'Vehicle ID is required for clock-in',
      ErrorCode.TIMECLOCK_VEHICLE_REQUIRED,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class TimeClockInvalidOdometerException extends AppException {
  constructor(currentOdometer: number, providedOdometer: number) {
    super(
      `Invalid odometer reading: ${providedOdometer}. Current odometer reading is ${currentOdometer}`,
      ErrorCode.TIMECLOCK_INVALID_ODOMETER,
      HttpStatus.BAD_REQUEST,
    );
  }
}
