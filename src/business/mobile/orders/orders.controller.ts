import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { MobileOrdersService } from './services/mobile-orders.service';
import { OrderStatus as BackendOrderStatus } from '@app/business/order/orders/domain/order.types';
import {
  OrderStatus,
  mapBackendToMobileStatus,
  getDetailedInProgressStatus,
} from './domain/order';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { OrderDetailsResponseDto } from './dto/order-details-response.dto';
import { MobileOrderListItemDto } from '@app/business/mobile/orders/dto/order-list-response.dto';

@ApiTags('Mobile - Orders')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'mobile/orders',
  version: '1',
})
export class MobileOrdersController {
  constructor(private readonly ordersService: MobileOrdersService) {}

  @Get()
  @ApiOperation({ summary: 'Get orders for the authenticated driver' })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: OrderStatus,
    isArray: true,
    description: 'Filter orders by status',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of orders retrieved successfully',
    type: [MobileOrderListItemDto],
  })
  async getDriverOrders(
    @Request() req: any,
    @Query('status') status?: OrderStatus | OrderStatus[],
  ) {
    const driverId = req.user.sub;
    const orders = await this.ordersService.getDriverOrders(driverId, status);
    console.log({ orders });

    // Transform orders into the format shown in the screenshot with pickup and delivery pairs
    const formattedOrders: MobileOrderListItemDto[] = [];

    orders.forEach((order) => {
      // Get service level from metadata or use basePriceType from the database
      const serviceLevel =
        (order as any).metadata?.serviceLevel || order.basePriceType;

      // Determine delivery type - handle null scheduled times
      const deliveryType =
        serviceLevel ||
        this.determineDeliveryType(
          order.scheduledCollectionTime,
          order.scheduledDeliveryTime,
          serviceLevel,
        );

      // Determine status display text based on order status
      let statusText = 'Submitted';
      // Convert backend status to mobile status
      let mobileStatus = mapBackendToMobileStatus(order.status);

      // For InProgress status, get the detailed status from metadata
      if (mobileStatus === OrderStatus.InTransit) {
        mobileStatus = getDetailedInProgressStatus(order);
      }
      // Map status to display text
      if (mobileStatus === OrderStatus.GoingForPickup) {
        statusText = 'Going for Pickup';
      } else if (mobileStatus === OrderStatus.PickedUp) {
        statusText = 'Picked Up';
      } else if (mobileStatus === OrderStatus.InTransit) {
        statusText = 'In Transit';
      } else if (mobileStatus === OrderStatus.Assigned) {
        statusText = 'Assigned';
      } else if (mobileStatus === OrderStatus.Delivered) {
        statusText = 'Delivered';
      } else if (mobileStatus === OrderStatus.Completed) {
        statusText = 'Completed';
      }

      // Create format like in screenshot - showing only the order number without prefix
      // Remove TRK- prefix if present, otherwise just use the tracking number
      const orderNumberDisplay =
        order.trackingNumber?.replace(/^(ORD-|TRK-)/, '') || '';

      // Format date for display: H:MM AM/PM (just time)
      const formatTime = (date: Date | string | undefined): string => {
        // If date is undefined, return empty string
        if (!date) return '';
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        // Format like in screenshot: 10:45 AM (just time)
        return `${dateObj.getHours() % 12 || 12}:${String(dateObj.getMinutes()).padStart(2, '0')} ${dateObj.getHours() >= 12 ? 'PM' : 'AM'}`;
      };

      // Create display time string for delivery (or use current time if null)
      const deliveryTimeDisplay = formatTime(order.scheduledDeliveryTime);

      // Get delivery address from the order
      // The order object should have collectionAddressSummary and deliveryAddressSummary
      // populated by the service layer
      const deliveryAddressText = order.deliveryAddressSummary || '';

      // Get customer name
      const customerName = order.customerName || 'Unknown Customer';

      // Format based on the screenshot
      formattedOrders.push(<MobileOrderListItemDto>{
        id: order.id,
        orderNumber: orderNumberDisplay,
        customerName: customerName,
        type: 'Deliver', // Always use Deliver as shown in screenshot
        location: deliveryAddressText,
        serviceType: deliveryType,
        scheduledTime: deliveryTimeDisplay,
        status: statusText,
        // These fields aren't in the DTO but can be used in the UI
        shipperName: customerName,
        recipientName: order.deliveryContactName,
      });
    });

    return formattedOrders;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get order details by ID' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order details retrieved successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  async getOrderDetails(@Request() req: any, @Param('id') id: string) {
    const driverId = req.user.sub;
    const order = await this.ordersService.getDriverOrderById(driverId, id);

    // Format time similar to list view
    const formatTime = (date: Date | string | undefined): string => {
      if (!date) return '';
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} ${dateObj.getHours() % 12 || 12}:${String(dateObj.getMinutes()).padStart(2, '0')} ${dateObj.getHours() >= 12 ? 'PM' : 'AM'}`;
    };

    // Map status from enum to user-friendly text
    const getStatusText = (backendStatus: BackendOrderStatus): string => {
      // Convert backend status to mobile status
      let mobileStatus = mapBackendToMobileStatus(backendStatus);
      // For InProgress status, get the detailed status from metadata
      if (mobileStatus === OrderStatus.InTransit) {
        mobileStatus = getDetailedInProgressStatus(order);
      }
      const statusMap = {
        [OrderStatus.Pending]: 'Pending',
        [OrderStatus.Assigned]: 'Assigned',
        [OrderStatus.GoingForPickup]: 'Going for Pickup',
        [OrderStatus.PickedUp]: 'Picked Up',
        [OrderStatus.InTransit]: 'In Transit',
        [OrderStatus.OnHold]: 'On Hold',
        [OrderStatus.Delivered]: 'Delivered',
        [OrderStatus.Failed]: 'Failed',
        [OrderStatus.Cancelled]: 'Cancelled',
        [OrderStatus.Returned]: 'Returned',
        [OrderStatus.Completed]: 'Completed',
      };
      return statusMap[mobileStatus] || mobileStatus.toString();
    };

    // Get collection and delivery addresses from the order
    const collectionAddressParts = (order.collectionAddressSummary || '').split(
      ',',
    );
    const deliveryAddressParts = (order.deliveryAddressSummary || '').split(
      ',',
    );

    // Format order details according to the UI
    const response: OrderDetailsResponseDto = {
      orderNumber: order.trackingNumber?.replace('ORD-', '') || '',
      status: getStatusText(order.status),
      shipper: order.customerName || 'Unknown Customer',
      requestedBy: order.requestedByName || order.customerName || 'Unknown',
      collectionTime: formatTime(order.scheduledCollectionTime),
      deliveryTime: formatTime(order.scheduledDeliveryTime),

      collectionLocation: {
        businessName: order.collectionContactName || '',
        addressLine1: collectionAddressParts[0] || '',
        cityAndPostalCode: collectionAddressParts.slice(1).join(',') || '',
      },

      deliveryLocation: {
        businessName: order.deliveryContactName || '',
        addressLine1: deliveryAddressParts[0] || '',
        cityAndPostalCode: deliveryAddressParts.slice(1).join(',') || '',
      },

      serviceLevel: this.determineDeliveryType(
        order.scheduledCollectionTime,
        order.scheduledDeliveryTime,
        (order as any).metadata?.serviceLevel,
      ),

      serviceOptions: this.extractServiceOptions(order),

      description: this.formatOrderDescription(order),

      totalPrice: order.totalPrice,
      quantity:
        order.items?.reduce((total, item) => total + item.quantity, 0) || 0,
      weight:
        order.items?.reduce(
          (total, item) => total + (item.weight || 0) * item.quantity,
          0,
        ) || 0,
      dimensions: this.calculateDimensions(order.items),
    };

    return response;
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update order status' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({ type: UpdateOrderStatusDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order status updated successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Invalid status transition' })
  async updateOrderStatus(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdateOrderStatusDto,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.updateOrderStatus(driverId, id, updateDto);
    return this.getOrderDetails(req, id); // Return the full order details
  }

  @Post(':id/accept')
  @ApiOperation({ summary: 'Accept an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order accepted successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiBadRequestResponse({ description: 'Order cannot be accepted' })
  async acceptOrder(@Request() req: any, @Param('id') id: string) {
    const driverId = req.user.sub;
    await this.ordersService.acceptOrder(driverId, id);
    return this.getOrderDetails(req, id); // Return the full order details
  }

  @Post(':id/reject')
  @ApiOperation({ summary: 'Reject an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reason: { type: 'string', example: 'Vehicle issue' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order rejected successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Order cannot be rejected' })
  async rejectOrder(
    @Request() req: any,
    @Param('id') id: string,
    @Body('reason') reason: string,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.rejectOrder(driverId, id, reason);
    return this.getOrderDetails(req, id); // Return the full order details
  }

  @Post(':id/transfer')
  @ApiOperation({ summary: 'Transfer an order to another driver' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        newDriverId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440002',
        },
        reason: { type: 'string', example: 'Driver unavailable' },
      },
      required: ['newDriverId'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order transferred successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Order cannot be transferred' })
  async transferOrder(
    @Request() req: any,
    @Param('id') id: string,
    @Body('newDriverId') newDriverId: string,
    @Body('reason') reason: string,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.transferOrder(
      driverId,
      id,
      newDriverId,
      reason || 'Driver requested transfer',
    );
    return this.getOrderDetails(req, id); // Return the full order details
  }

  /**
   * Extract service options from order data
   * This replaces hardcoded service options with dynamic ones based on the order
   */
  private extractServiceOptions(order: any): { description: string }[] {
    const serviceOptions: { description: string }[] = [];
    // Get price modifiers from order metadata if available
    const metadata = (order as any).metadata;
    if (metadata && metadata.priceModifiers) {
      return order.metadata.priceModifiers.map((modifier: any) => ({
        description: modifier.name || modifier.description || 'Price modifier',
      }));
    }

    // If no price modifiers in metadata, extract from other order info
    // Check if there's a base price calculation
    if (order.basePrice) {
      // Convert to number if it's a string
      const basePrice =
        typeof order.basePrice === 'string'
          ? parseFloat(order.basePrice)
          : order.basePrice;

      serviceOptions.push({
        description: `Base price: ${Number(basePrice).toFixed(2)}`,
      });
    }

    // Check for misc adjustments
    if (order.miscAdjustment && order.miscAdjustment !== 0) {
      // Convert to number if it's a string
      const miscAdjustment =
        typeof order.miscAdjustment === 'string'
          ? parseFloat(order.miscAdjustment)
          : order.miscAdjustment;

      serviceOptions.push({
        description: `Adjustment: ${miscAdjustment > 0 ? '+' : ''}${Number(miscAdjustment).toFixed(2)}`,
      });
    }

    // If we still don't have any service options, provide minimal info
    if (serviceOptions.length === 0) {
      serviceOptions.push({
        description: 'Standard delivery service',
      });
    }

    return serviceOptions;
  }

  /**
   * Format the order description based on order data
   */
  private formatOrderDescription(order: any): string {
    // If the order already has a description, use it
    if (order.description) {
      return order.description;
    }

    // If there are items, describe them
    if (order.items && order.items.length > 0) {
      const totalItems = order.items.reduce(
        (total: number, item: any) => total + item.quantity,
        0,
      );
      const itemDescriptions = order.items
        .map((item: any) => `${item.quantity} × ${item.description || 'Item'}`)
        .join('\n');
      return `${totalItems} item${totalItems !== 1 ? 's' : ''} total:\n${itemDescriptions}`;
    }

    // Default case
    return 'No detailed description available';
  }

  /**
   * Calculate dimensions based on order items
   */
  private calculateDimensions(items: any[] | undefined): string {
    if (!items || items.length === 0) {
      return 'N/A';
    }

    // Try to extract dimensions from items if they exist
    const itemsWithDimensions = items.filter(
      (item) => item.length && item.width && item.height,
    );

    if (itemsWithDimensions.length > 0) {
      // Use the largest item's dimensions as representative
      const largestItem = itemsWithDimensions.reduce((largest, item) => {
        const currentVolume = item.length * item.width * item.height;
        const largestVolume = largest.length * largest.width * largest.height;
        return currentVolume > largestVolume ? item : largest;
      }, itemsWithDimensions[0]);

      return `${largestItem.length} × ${largestItem.width} × ${largestItem.height} ${largestItem.dimensionUnit || ''}`;
    }

    // If no specific dimensions, return N/A
    return 'N/A';
  }

  /**
   * Determine the delivery type based on scheduled collection and delivery times
   * This replaces hardcoded delivery type logic with a consistent service-level function
   */
  private determineDeliveryType(
    scheduledCollectionTime: Date | string | undefined,
    scheduledDeliveryTime: Date | string | undefined,
    metadataServiceLevel?: string,
  ): string {
    // First check if there's an explicit service level in metadata or from the database
    if (metadataServiceLevel) {
      return metadataServiceLevel;
    }

    // If both times are provided, determine based on date comparison
    if (scheduledCollectionTime && scheduledDeliveryTime) {
      const pickupDate = new Date(scheduledCollectionTime);
      const deliveryDate = new Date(scheduledDeliveryTime);

      const isSameDay =
        pickupDate.getDate() === deliveryDate.getDate() &&
        pickupDate.getMonth() === deliveryDate.getMonth() &&
        pickupDate.getFullYear() === deliveryDate.getFullYear();

      // Calculate hours difference to determine service level
      const hoursDiff =
        (deliveryDate.getTime() - pickupDate.getTime()) / (1000 * 60 * 60);

      // Determine service level based on time difference
      if (isSameDay) {
        if (hoursDiff <= 2) return 'Express';
        if (hoursDiff <= 4) return 'Priority';
        return 'Sameday';
      } else {
        if (hoursDiff <= 24) return 'Overnight';
        if (hoursDiff <= 48) return 'Nextday';
        return 'Standard';
      }
    }

    // Default to Standard if we can't determine
    return 'Standard';
  }
}
