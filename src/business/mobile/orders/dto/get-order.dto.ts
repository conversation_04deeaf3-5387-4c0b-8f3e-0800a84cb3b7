import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  OrderStatus,
  OrderPriority,
  Location,
  OrderItemDomain,
} from '../domain/order';

export class LocationDto implements Location {
  @ApiProperty({ example: '123 Main St' })
  addressLine1: string;

  @ApiPropertyOptional({ example: 'Suite 100' })
  addressLine2?: string;

  @ApiProperty({ example: 'Montreal' })
  city: string;

  @ApiProperty({ example: 'Quebec' })
  province: string;

  @ApiProperty({ example: 'H3Z 2Y7' })
  postalCode: string;

  @ApiProperty({ example: 'Canada' })
  country: string;

  @ApiPropertyOptional({ example: 45.5017 })
  latitude?: number;

  @ApiPropertyOptional({ example: -73.5673 })
  longitude?: number;

  @ApiPropertyOptional({ example: 'Doorbell is broken, please knock.' })
  notes?: string;
}

export class OrderItemDto implements OrderItemDomain {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655441111' })
  orderId: string;

  @ApiProperty({ example: 'Large box of electronics' })
  description: string;

  @ApiProperty({ example: 2 })
  quantity: number;

  @ApiPropertyOptional({ example: 5.5 })
  weight?: number;

  @ApiPropertyOptional({
    example: {
      length: 30,
      width: 20,
      height: 15,
    },
  })
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };

  @ApiProperty({ example: 'Box' })
  packageType: string;

  @ApiPropertyOptional({ example: 'Fragile - handle with care' })
  specialInstructions?: string;
}

export class GetOrderDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ example: 'ORD-12345' })
  orderNumber: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655442222' })
  customerId: string;

  @ApiProperty({ example: 'Acme Corp' })
  customerName: string;

  @ApiPropertyOptional({ example: '550e8400-e29b-41d4-a716-446655443333' })
  driverId?: string;

  @ApiPropertyOptional({ example: '550e8400-e29b-41d4-a716-446655444444' })
  vehicleId?: string;

  @ApiProperty({ enum: OrderStatus })
  status: OrderStatus;

  @ApiProperty({ enum: OrderPriority })
  priority: OrderPriority;

  @ApiProperty({ type: LocationDto })
  pickupLocation: LocationDto;

  @ApiProperty({ type: LocationDto })
  deliveryLocation: LocationDto;

  @ApiPropertyOptional({ format: 'date-time' })
  scheduledPickupTime?: Date;

  @ApiPropertyOptional({ format: 'date-time' })
  scheduledDeliveryTime?: Date;

  @ApiPropertyOptional({ format: 'date-time' })
  actualPickupTime?: Date;

  @ApiPropertyOptional({ format: 'date-time' })
  actualDeliveryTime?: Date;

  @ApiProperty({ type: [OrderItemDto] })
  items: OrderItemDto[];

  @ApiProperty({ example: 149.99 })
  totalPrice: number;

  @ApiProperty({ example: 'Paid' })
  paymentStatus: string;

  @ApiPropertyOptional({ example: 'Call customer before delivery' })
  specialInstructions?: string;

  @ApiProperty({ format: 'date-time' })
  createdAt: Date;

  @ApiProperty({ format: 'date-time' })
  updatedAt: Date;
}
