import { ApiProperty } from '@nestjs/swagger';

export class LocationDetailsDto {
  @ApiProperty({ example: 'MPM WAREHOUSING' })
  businessName: string;

  @ApiProperty({ example: '5505 Boulevard des Grandes-Prairies' })
  addressLine1: string;

  @ApiProperty({ example: 'Montréal, H1R 1B4' })
  cityAndPostalCode: string;
}

export class ServiceOptionDto {
  @ApiProperty({ example: 'Fuel Surcharge 15%' })
  description: string;
}

export class OrderDetailsResponseDto {
  @ApiProperty({ example: '795599' })
  orderNumber: string;

  @ApiProperty({ example: 'In Transit' })
  status: string;

  @ApiProperty({ example: 'Hubscher Ribbons' })
  shipper: string;

  @ApiProperty({ example: 'Hubscher Ribbons' })
  requestedBy: string;

  @ApiProperty({ example: '2024-05-28 11:43 AM' })
  collectionTime: string;

  @ApiProperty({ example: '2024-05-28 5:00 PM' })
  deliveryTime: string;

  @ApiProperty()
  collectionLocation: LocationDetailsDto;

  @ApiProperty()
  deliveryLocation: LocationDetailsDto;

  @ApiProperty({ example: 'Sameday' })
  serviceLevel: string;

  @ApiProperty({ type: [ServiceOptionDto] })
  serviceOptions: ServiceOptionDto[];

  @ApiProperty({
    example: '12 Skid = TOTAL : 12\nNote : please deliver tomorrow at 10 A.M.',
  })
  description: string;

  @ApiProperty({ example: 591.93 })
  totalPrice: number;

  @ApiProperty({ example: 0 })
  quantity: number;

  @ApiProperty({ example: 10000 })
  weight: number;

  @ApiProperty({ example: '0 x 0 x 0 (L x W x H)' })
  dimensions: string;
}
