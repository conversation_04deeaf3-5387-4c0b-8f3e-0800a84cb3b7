import { Is<PERSON>num, IsOptional, IsString, ValidateIf } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus } from '@app/business/mobile/orders/domain/order';

export class UpdateOrderStatusDto {
  @ApiProperty({ enum: OrderStatus })
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @ApiPropertyOptional({ example: 'Customer not available' })
  @IsString()
  @IsOptional()
  statusNotes?: string;

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  @IsOptional()
  proofOfDeliveryImage?: any;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @ValidateIf((o) => o.status === OrderStatus.Delivered)
  customerSignature?: string;

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  @IsOptional()
  @ValidateIf((o) => o.status === OrderStatus.Delivered)
  signatureImage?: any;
}
