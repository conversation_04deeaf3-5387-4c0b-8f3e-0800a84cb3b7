import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderPriority, OrderStatus } from '../domain/order';

export class OrderListItemDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ example: 'ORD-12345' })
  orderNumber: string;

  @ApiProperty({ example: 'Acme Corp' })
  customerName: string;

  @ApiProperty({ enum: OrderStatus })
  status: OrderStatus;

  @ApiProperty({ enum: OrderPriority })
  priority: OrderPriority;

  @ApiProperty({ example: '123 Main St, Montreal, QC' })
  pickupAddress: string;

  @ApiProperty({ example: '456 Oak St, Toronto, ON' })
  deliveryAddress: string;

  @ApiPropertyOptional({ format: 'date-time' })
  scheduledPickupTime?: Date;

  @ApiPropertyOptional({ format: 'date-time' })
  scheduledDeliveryTime?: Date;

  @ApiProperty({ example: 149.99 })
  totalPrice: number;
}

export class MobileOrderListItemDto {
  @ApiProperty({
    description: 'Order ID',
    example: '550e8400-e29b-41d4-a716-446655450001',
  })
  id: string;

  @ApiProperty({
    description: 'Order number without prefix',
    example: '795599',
  })
  orderNumber: string;

  @ApiProperty({ description: 'Customer name', example: 'Smart Nation' })
  customerName: string;

  @ApiProperty({
    description: 'Order action type',
    example: 'Collect',
    enum: ['Collect', 'Deliver'],
  })
  type: 'Collect' | 'Deliver';

  @ApiProperty({
    description: 'Location address',
    example: '2369 guenette st., Montreal',
  })
  location: string;

  @ApiProperty({
    description: 'Delivery type',
    example: 'Sameday',
    enum: ['Sameday', 'Nextday'],
  })
  serviceType: 'Sameday' | 'Nextday';

  @ApiProperty({ description: 'Scheduled time', example: '2024-08-22 5:00 PM' })
  scheduledTime: string;

  @ApiProperty({
    description: 'Order status',
    example: 'In Transit',
    enum: ['In Transit', 'Submitted', 'Assigned', 'Completed'],
  })
  status: string;
}
