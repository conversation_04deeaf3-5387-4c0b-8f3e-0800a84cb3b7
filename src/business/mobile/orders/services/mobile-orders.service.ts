import { Injectable } from '@nestjs/common';
import {
  OrderStatus as MobileOrderStatus,
  mapMobileToBackendStatus,
  mapBackendToMobileStatus,
} from '../domain/order';
import { UpdateOrderStatusDto } from '../dto/update-order-status.dto';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { HttpStatus } from '@nestjs/common';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';
import { OrdersService } from '@app/business/order/orders/orders.service';
import { OrderAssignmentService } from '@app/business/order/orders/services/order-assignment.service';
import { OrderStatusService } from '@app/business/order/orders/services/order-status.service';
import { UsersService } from '@app/business/user/users/users.service';
import { VehiclesService } from '@app/business/vehicle/vehicles/vehicles.service';
import { FileStorageService } from '@app/core/file-storage/file-storage.service';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import { OrderStatus as BackendOrderStatus } from '@app/business/order/orders/domain/order.types';
import { DataSource } from 'typeorm';

@Injectable()
export class MobileOrdersService {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly orderStatusService: OrderStatusService,
    private readonly usersService: UsersService,
    private readonly vehiclesService: VehiclesService,
    private readonly fileStorageService: FileStorageService,
    private readonly priceSetsService: PriceSetsService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Enhance order with service level information
   * This adds the service level to the metadata if it's not already present
   */
  private enhanceOrderWithServiceLevel(order: any): void {
    try {
      // If metadata doesn't exist, create it
      if (!order.metadata) {
        order.metadata = {};
      }

      // If service level is not in metadata, try to determine it
      if (!order.metadata.serviceLevel) {
        // First try to use the basePriceType as the service level if available
        if (order.basePriceType) {
          order.metadata.serviceLevel = order.basePriceType;
        }
        // Otherwise determine based on scheduled times
        else if (order.basePrice) {
          // Store the service level in metadata for future use
          order.metadata.serviceLevel = this.determineServiceLevel(order);
        }
      }
    } catch (error) {
      console.error('Error enhancing order with service level:', error);
    }
  }

  /**
   * Format an address entity into a readable summary string
   */
  private formatAddressSummary(address: any): string {
    if (!address) return '';

    const parts: string[] = [];

    if (address.addressLine1) {
      parts.push(address.addressLine1);
    }

    if (address.city || address.province || address.postalCode) {
      const cityParts: string[] = [];
      if (address.city) cityParts.push(address.city);
      if (address.province) cityParts.push(address.province);
      if (address.postalCode) cityParts.push(address.postalCode);
      parts.push(cityParts.join(', '));
    }

    return parts.join(', ');
  }

  /**
   * Determine the service level based on order information
   */
  private determineServiceLevel(order: any): string {
    // Default service levels based on delivery time
    if (order.scheduledCollectionTime && order.scheduledDeliveryTime) {
      const pickupDate = new Date(order.scheduledCollectionTime);
      const deliveryDate = new Date(order.scheduledDeliveryTime);

      const isSameDay =
        pickupDate.getDate() === deliveryDate.getDate() &&
        pickupDate.getMonth() === deliveryDate.getMonth() &&
        pickupDate.getFullYear() === deliveryDate.getFullYear();

      // Calculate hours difference
      const hoursDiff =
        (deliveryDate.getTime() - pickupDate.getTime()) / (1000 * 60 * 60);

      if (isSameDay) {
        if (hoursDiff <= 2) return 'Express';
        if (hoursDiff <= 4) return 'Same Day';
        return 'Standard';
      } else {
        return 'Next Day';
      }
    }

    return 'Standard';
  }

  async getDriverOrders(
    driverId: string,
    status?: MobileOrderStatus | MobileOrderStatus[],
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    console.log(
      `Getting orders for driver ${driverId} with tenant ${driver.tenantId}`,
    );

    // Use a query builder to get all orders assigned to this driver
    // Get all statuses that we want to exclude
    const excludedStatuses = [
      BackendOrderStatus.Draft,
      BackendOrderStatus.Completed,
    ];

    // Create a query builder that joins with address tables to get address details
    const orders = await this.dataSource
      .createQueryBuilder()
      .select('orders')
      .from('orders', 'orders')
      .leftJoinAndSelect('orders.collectionAddress', 'collectionAddress')
      .leftJoinAndSelect('orders.deliveryAddress', 'deliveryAddress')
      .leftJoinAndSelect('orders.customer', 'customer')
      .where('orders.tenant_id = :tenantId', { tenantId: driver.tenantId })
      .andWhere('orders.assigned_driver_id = :driverId', { driverId })
      .andWhere('orders.is_deleted = :isDeleted', { isDeleted: false })
      .andWhere('orders.status NOT IN (:...excludedStatuses)', {
        excludedStatuses,
      })
      .getMany();

    console.log(`Found ${orders.length} orders assigned to driver ${driverId}`);

    // Apply status filtering in memory if needed
    let filteredOrders = orders;
    if (status) {
      // Convert mobile status to backend status
      const backendStatuses = Array.isArray(status)
        ? status.map((s) => mapMobileToBackendStatus(s))
        : [mapMobileToBackendStatus(status)];

      // Filter by status
      filteredOrders = filteredOrders.filter((order) =>
        backendStatuses.includes(order.status),
      );

      console.log(
        `After status filtering, found ${filteredOrders.length} orders`,
      );
    }

    // Enhance each order with service level information and address summaries
    filteredOrders.forEach((order) => {
      this.enhanceOrderWithServiceLevel(order);

      // Add address summaries for easier access in the controller
      if (order.collectionAddress) {
        order.collectionAddressSummary = this.formatAddressSummary(
          order.collectionAddress,
        );
      }

      if (order.deliveryAddress) {
        order.deliveryAddressSummary = this.formatAddressSummary(
          order.deliveryAddress,
        );
      }

      // Add customer name for easier access
      if (order.customer) {
        order.customerName = order.customer.fullName || order.customer.email;
      }
    });

    return filteredOrders;
  }

  async getOrderById(id: string, driverId: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      return await this.ordersService.findOne(driver.tenantId, id);
    } catch {
      throw new AppException(
        `Order with ID ${id} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async getDriverOrderById(driverId: string, orderId: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      // Get order with service level information and related entities
      const order = await this.dataSource
        .createQueryBuilder()
        .select('orders')
        .from('orders', 'orders')
        .leftJoinAndSelect('orders.collectionAddress', 'collectionAddress')
        .leftJoinAndSelect('orders.deliveryAddress', 'deliveryAddress')
        .leftJoinAndSelect('orders.customer', 'customer')
        .where('orders.id = :orderId', { orderId })
        .andWhere('orders.tenant_id = :tenantId', { tenantId: driver.tenantId })
        .getOne();

      if (!order) {
        throw new AppException(
          `Order with ID ${orderId} not found`,
          ErrorCode.ORDER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      // Enhance the order with service level information if needed
      this.enhanceOrderWithServiceLevel(order);

      // Add address summaries for easier access in the controller
      if (order.collectionAddress) {
        order.collectionAddressSummary = this.formatAddressSummary(
          order.collectionAddress,
        );
      }
      
      if (order.deliveryAddress) {
        order.deliveryAddressSummary = this.formatAddressSummary(
          order.deliveryAddress,
        );
      }
      
      // Add customer name for easier access
      if (order.customer) {
        order.customerName = order.customer.fullName || order.customer.email;
      }

      if (order.assignedDriverId !== driverId) {
        throw new AppException(
          `Order with ID ${orderId} is not assigned to driver ${driverId}`,
          ErrorCode.ORDER_NOT_ASSIGNED_TO_DRIVER,
          HttpStatus.FORBIDDEN,
        );
      }

      return order;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Order with ID ${orderId} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async updateOrderStatus(
    driverId: string,
    orderId: string,
    updateDto: UpdateOrderStatusDto,
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Verify order exists and is assigned to driver
    const order = await this.getDriverOrderById(driverId, orderId);

    // Check if the status transition is valid
    const backendStatus = mapMobileToBackendStatus(updateDto.status);
    const currentBackendStatus = order.status;

    if (
      !this.orderStatusService.isValidTransition(
        currentBackendStatus,
        backendStatus,
      )
    ) {
      throw new AppException(
        `Invalid status transition from ${currentBackendStatus} to ${backendStatus}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Update the order status via the order status service
    await this.ordersService.changeStatus(
      driver.tenantId,
      orderId,
      driverId,
      backendStatus,
      updateDto.statusNotes || '',
      '',
    );

    // Process the uploaded files and metadata
    // Get existing metadata to preserve any existing values
    const existingMetadata = (order as any).metadata || {};
    const metadata: Record<string, any> = { ...existingMetadata };
    let hasMetadataUpdates = false;

    // Store the detailed driver status in metadata
    switch (updateDto.status) {
      case MobileOrderStatus.GoingForPickup:
        metadata.driverStatus = 'going_for_pickup';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
      case MobileOrderStatus.PickedUp:
        metadata.driverStatus = 'picked_up';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
      case MobileOrderStatus.InTransit:
        metadata.driverStatus = 'in_transit';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
      case MobileOrderStatus.Delivered:
        metadata.driverStatus = 'delivered';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
    }

    // Handle proof of delivery image if provided
    if (updateDto.proofOfDeliveryImage) {
      const fileName = `pod-${orderId}-${Date.now()}.jpg`;
      const fileUrl = await this.fileStorageService.uploadBase64Image(
        updateDto.proofOfDeliveryImage,
        fileName,
        'proof-of-delivery',
      );

      metadata.proofOfDeliveryImages = metadata.proofOfDeliveryImages || [];
      metadata.proofOfDeliveryImages.push(fileUrl);
      hasMetadataUpdates = true;
    }

    // Handle customer signature if provided
    if (updateDto.customerSignature) {
      metadata.customerSignature = updateDto.customerSignature;
      hasMetadataUpdates = true;
    }

    // Handle signature image if provided
    if (updateDto.signatureImage) {
      const fileName = `signature-${orderId}-${Date.now()}.jpg`;
      const fileUrl = await this.fileStorageService.uploadBase64Image(
        updateDto.signatureImage,
        fileName,
        'signatures',
      );

      metadata.signatureImage = fileUrl;
      hasMetadataUpdates = true;
    }

    // Update order metadata if there are any updates
    if (hasMetadataUpdates) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        metadata: metadata,
      });
    }

    // If the order is picked up, record the actual collection time if not already set
    if (
      updateDto.status === MobileOrderStatus.PickedUp &&
      !order.actualCollectionTime
    ) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        actualCollectionTime: new Date(),
      });
    }

    // If the order is delivered or completed, record the actual delivery time
    if (
      backendStatus === BackendOrderStatus.Completed ||
      updateDto.status === MobileOrderStatus.Delivered
    ) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        actualDeliveryTime: new Date(),
      });
    }

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }

  async acceptOrder(driverId: string, orderId: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      const order = await this.ordersService.findOne(driver.tenantId, orderId);

      // Convert backend status to mobile status for comparison
      const mobileStatus = mapBackendToMobileStatus(order.status);
      if (mobileStatus !== MobileOrderStatus.Pending) {
        throw new AppException(
          `Order with ID ${orderId} is not in Pending status`,
          ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
          HttpStatus.BAD_REQUEST,
        );
      }

      // Find the driver's active vehicle
      const filter = new BaseFilterDto();
      filter.pageNumber = 1;
      filter.pageSize = 10;
      filter.where = {
        driverId: { [FilterOperator.EQ]: driverId },
        isActive: { [FilterOperator.EQ]: true },
      };
      const vehiclesResult = await this.vehiclesService.getVehicleList(
        filter,
        driver.tenantId,
      );
      const vehicleId =
        vehiclesResult.data.length > 0 ? vehiclesResult.data[0].id : undefined;

      // Assign the order to the driver and update the status
      await this.orderAssignmentService.assignOrder(
        orderId,
        driverId,
        driverId, // Driver is assigning themselves
        vehicleId,
        'Driver accepted order via mobile app',
      );

      // Update the order status to Assigned
      await this.ordersService.changeStatus(
        driver.tenantId,
        orderId,
        driverId,
        BackendOrderStatus.Assigned,
        'Driver accepted order via mobile app',
        '',
      );

      // Return the updated order
      return this.ordersService.findOne(driver.tenantId, orderId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Order with ID ${orderId} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async rejectOrder(driverId: string, orderId: string, reason: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    const order = await this.getDriverOrderById(driverId, orderId);

    // Convert backend status to mobile status for comparison
    const mobileStatus = mapBackendToMobileStatus(order.status);
    if (
      ![MobileOrderStatus.Pending, MobileOrderStatus.Assigned].includes(
        mobileStatus,
      )
    ) {
      throw new AppException(
        `Cannot reject an order that is already ${order.status}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Unassign the order
    await this.orderAssignmentService.unassignOrder(
      orderId,
      driverId, // Driver is unassigning themselves
      reason,
    );

    // Update the order status to Pending if it was Assigned
    if (order.status === BackendOrderStatus.Assigned) {
      await this.ordersService.changeStatus(
        driver.tenantId,
        orderId,
        driverId,
        BackendOrderStatus.Pending,
        reason || 'Driver rejected order',
        '',
      );
    }

    // Add rejection reason to order notes
    await this.ordersService.update(driver.tenantId, orderId, driverId, {
      internalNotes: `Order rejected by driver: ${reason}`,
    });

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }

  async transferOrder(
    driverId: string,
    orderId: string,
    newDriverId: string,
    reason: string,
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    const order = await this.getDriverOrderById(driverId, orderId);

    // Convert backend status to mobile status for comparison
    const mobileStatus = mapBackendToMobileStatus(order.status);
    if (
      ![MobileOrderStatus.Assigned, MobileOrderStatus.Pending].includes(
        mobileStatus,
      )
    ) {
      throw new AppException(
        `Cannot transfer an order that is already ${order.status}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Verify the new driver exists
    const newDriver = await this.usersService.findById(newDriverId);
    if (!newDriver) {
      throw new AppException(
        `New driver with ID ${newDriverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Verify both drivers belong to the same tenant
    if (driver.tenantId !== newDriver.tenantId) {
      throw new AppException(
        `Cannot transfer order to driver from different tenant`,
        ErrorCode.USER_INVALID_STATUS,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if the new driver is available
    // In a real implementation, you might check driver status, current load, etc.

    // Find the new driver's active vehicle
    const filter = new BaseFilterDto();
    filter.pageNumber = 1;
    filter.pageSize = 10;
    filter.where = {
      driverId: { [FilterOperator.EQ]: newDriverId },
      isActive: { [FilterOperator.EQ]: true },
    };
    const vehiclesResult = await this.vehiclesService.getVehicleList(
      filter,
      driver.tenantId,
    );
    const newVehicleId =
      vehiclesResult.data.length > 0 ? vehiclesResult.data[0].id : undefined;

    // If no active vehicle found and order seems to need a vehicle
    // Since we can't directly check vehicleTypeId in the DTO,
    // we'll make a determination based on other factors
    if (
      !newVehicleId &&
      (order.assignedVehicleId ||
        (order.totalWeight && order.totalWeight > 100))
    ) {
      throw new AppException(
        `New driver has no active vehicle, but this order likely requires one`,
        ErrorCode.VEHICLE_NOT_FOUND,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Reassign the order to the new driver
    await this.orderAssignmentService.reassignOrder(
      orderId,
      driverId, // Current driver is reassigning
      newDriverId,
      newVehicleId,
      reason || 'Driver requested transfer',
    );

    // Add transfer information to order notes
    await this.ordersService.update(driver.tenantId, orderId, driverId, {
      internalNotes: `Order transferred from driver ${driverId} to driver ${newDriverId}. Reason: ${reason}`,
    });

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }
}
