import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Request,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  Put,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { MobileTimeClockService } from './timeclock.service';
import { ClockInDto } from './dto/clock-in.dto';
import {
  TimeClockHistoryResponseDto,
  TimeClockStatusDto,
} from '@app/business/mobile/timeclock/dto/time-clock-response.dto';
import { ClockOutDto } from '@app/business/mobile/timeclock/dto/clock-out.dto';
import { BreakDto } from '@app/business/mobile/timeclock/dto/break.dto';
import { TimeClockSessionService } from '../../vehicle/time-clock-session/time-clock-session.service';

@ApiTags('Mobile - Time Clock')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'mobile/timeclock',
  version: '1',
})
export class MobileTimeClockController {
  constructor(
    private readonly timeClockService: MobileTimeClockService,
    private readonly timeClockSessionService: TimeClockSessionService,
  ) {}

  @Get('status')
  @ApiOperation({ summary: 'Get driver time clock status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Time clock status retrieved successfully',
    type: TimeClockStatusDto,
  })
  async getStatus(@Request() req) {
    const driverId = req.user.sub;

    const data =
      await this.timeClockSessionService.getDriverDutyStatus(driverId);
    return data;
  }

  @Get('history')
  @ApiOperation({ summary: 'Get driver time clock history' })
  @ApiQuery({
    name: 'date',
    required: false,
    type: String,
    description:
      'Date in YYYY-MM-DD format. If not provided, returns entries for the current day.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Time clock history retrieved successfully',
    type: TimeClockHistoryResponseDto,
  })
  async getHistory(@Request() req, @Query('date') dateString?: string) {
    const driverId = req.user.sub;

    let date: Date;
    if (dateString) {
      date = new Date(dateString);
      if (isNaN(date.getTime())) {
        date = new Date(); // Use today if invalid date
      }
    } else {
      date = new Date(); // Use today if no date provided
    }

    const entries = this.timeClockService.getDriverEntries(driverId, date);
    const { totalHours, totalBreakHours } =
      this.timeClockService.calculateDailyHours(driverId, date);

    return {
      entries,
      date: date.toISOString().split('T')[0],
      totalHours,
      totalBreakHours,
    };
  }

  @Post('clock-in')
  @ApiOperation({ summary: 'Clock in' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Clocked in successfully',
  })
  @ApiBadRequestResponse({ description: 'Driver is already clocked in' })
  async clockIn(@Request() req, @Body() clockInDto: ClockInDto) {
    const driverId = req.user.sub;
    try {
      return await this.timeClockSessionService.clockIn(driverId, clockInDto);
    } catch (error) {
      throw error;
    }
  }

  @Put('clock-out')
  @ApiOperation({ summary: 'Clock out' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Clocked out successfully',
  })
  @ApiBadRequestResponse({
    description: 'Driver is not clocked in or is on break',
  })
  @HttpCode(HttpStatus.OK)
  async clockOut(@Request() req, @Body() clockOutDto: ClockOutDto) {
    const driverId = req.user.sub;
    try {
      return await this.timeClockSessionService.clockOut(driverId, clockOutDto);
    } catch (error) {
      throw error;
    }
  }

  @Post('break/start')
  @ApiOperation({ summary: 'Start break' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Break started successfully',
  })
  @ApiBadRequestResponse({
    description: 'Driver is not clocked in or is already on break',
  })
  @HttpCode(HttpStatus.OK)
  async startBreak(@Request() req, @Body() breakDto: BreakDto) {
    const driverId = req.user.sub;
    return this.timeClockService.startBreak(driverId, breakDto);
  }

  @Post('break/end')
  @ApiOperation({ summary: 'End break' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Break ended successfully',
  })
  @ApiBadRequestResponse({ description: 'Driver is not on break' })
  @HttpCode(HttpStatus.OK)
  async endBreak(@Request() req, @Body() breakDto: BreakDto) {
    const driverId = req.user.sub;
    return this.timeClockService.endBreak(driverId, breakDto);
  }

  @Get('entry/:id')
  @ApiOperation({ summary: 'Get time clock entry by ID' })
  @ApiParam({
    name: 'id',
    description: 'Time clock entry ID',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Time clock entry retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Time clock entry not found' })
  async getEntry(@Request() req, @Param('id') id: string) {
    return this.timeClockService.getEntryById(id);
  }
}
