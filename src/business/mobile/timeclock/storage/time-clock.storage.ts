import { Injectable } from '@nestjs/common';
import {
  TimeClockEntryDomain,
  TimeClockEntryType,
  TimeClockSessionDomain,
} from '../domain/time-clock-entry';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class TimeClockStorage {
  private entries: TimeClockEntryDomain[] = [];

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData() {
    const now = new Date();
    const today = new Date(now);

    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    const twoDaysAgo = new Date(now);
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

    const threeDaysAgo = new Date(now);
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    const fourDaysAgo = new Date(now);
    fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);

    const fiveDaysAgo = new Date(now);
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

    const sixDaysAgo = new Date(now);
    sixDaysAgo.setDate(sixDaysAgo.getDate() - 6);

    const sevenDaysAgo = new Date(now);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    this.entries = [
      // Original entries
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(yesterday.setHours(8, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12350,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(8, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(8, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(yesterday.setHours(12, 0, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(12, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(12, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(yesterday.setHours(13, 0, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(13, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(13, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(yesterday.setHours(17, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12450,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(17, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(17, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(today.setHours(8, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12450,
        isDeleted: false,
        createdAt: new Date(today.setHours(8, 0, 0, 0)),
        updatedAt: new Date(today.setHours(8, 0, 0, 0)),
      },

      // Add more entries for driver 1 (John Smith)
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(twoDaysAgo.setHours(7, 45, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12250,
        isDeleted: false,
        createdAt: new Date(twoDaysAgo.setHours(7, 45, 0, 0)),
        updatedAt: new Date(twoDaysAgo.setHours(7, 45, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(twoDaysAgo.setHours(11, 30, 0, 0)),
        location: {
          latitude: 45.4961,
          longitude: -73.7662,
          address: 'West Island, Montreal, QC',
        },
        notes: 'Quick lunch',
        isDeleted: false,
        createdAt: new Date(twoDaysAgo.setHours(11, 30, 0, 0)),
        updatedAt: new Date(twoDaysAgo.setHours(11, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(twoDaysAgo.setHours(12, 15, 0, 0)),
        location: {
          latitude: 45.4961,
          longitude: -73.7662,
          address: 'West Island, Montreal, QC',
        },
        isDeleted: false,
        createdAt: new Date(twoDaysAgo.setHours(12, 15, 0, 0)),
        updatedAt: new Date(twoDaysAgo.setHours(12, 15, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(twoDaysAgo.setHours(16, 30, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12350,
        isDeleted: false,
        createdAt: new Date(twoDaysAgo.setHours(16, 30, 0, 0)),
        updatedAt: new Date(twoDaysAgo.setHours(16, 30, 0, 0)),
      },

      // Entries for driver 2 (Jane Doe) - Regular schedule with overtime
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(yesterday.setHours(8, 0, 0, 0)),
        location: {
          latitude: 43.6532,
          longitude: -79.3832,
          address: 'Dispatch Center, Toronto, ON',
        },
        odometer: 5800,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(8, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(8, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(yesterday.setHours(12, 30, 0, 0)),
        location: {
          latitude: 43.7764,
          longitude: -79.2318,
          address: 'North York, Toronto, ON',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(12, 30, 0, 0)),
        updatedAt: new Date(yesterday.setHours(12, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(yesterday.setHours(13, 15, 0, 0)),
        location: {
          latitude: 43.7764,
          longitude: -79.2318,
          address: 'North York, Toronto, ON',
        },
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(13, 15, 0, 0)),
        updatedAt: new Date(yesterday.setHours(13, 15, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(yesterday.setHours(18, 45, 0, 0)),
        location: {
          latitude: 43.6532,
          longitude: -79.3832,
          address: 'Dispatch Center, Toronto, ON',
        },
        odometer: 5950,
        notes: 'Overtime due to high volume deliveries',
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(18, 45, 0, 0)),
        updatedAt: new Date(yesterday.setHours(18, 45, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(today.setHours(8, 15, 0, 0)),
        location: {
          latitude: 43.6532,
          longitude: -79.3832,
          address: 'Dispatch Center, Toronto, ON',
        },
        odometer: 5950,
        isDeleted: false,
        createdAt: new Date(today.setHours(8, 15, 0, 0)),
        updatedAt: new Date(today.setHours(8, 15, 0, 0)),
      },

      // Entries for driver 4 (Sarah Wilson) - Split shift
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(yesterday.setHours(6, 0, 0, 0)),
        location: {
          latitude: 49.2827,
          longitude: -123.1207,
          address: 'Distribution Hub, Vancouver, BC',
        },
        odometer: 8500,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(6, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(6, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(yesterday.setHours(10, 0, 0, 0)),
        location: {
          latitude: 49.2827,
          longitude: -123.1207,
          address: 'Distribution Hub, Vancouver, BC',
        },
        odometer: 8580,
        notes: 'Morning deliveries completed',
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(10, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(10, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(yesterday.setHours(15, 0, 0, 0)),
        location: {
          latitude: 49.2827,
          longitude: -123.1207,
          address: 'Distribution Hub, Vancouver, BC',
        },
        odometer: 8580,
        notes: 'Starting afternoon shift',
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(15, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(15, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(yesterday.setHours(19, 0, 0, 0)),
        location: {
          latitude: 49.2827,
          longitude: -123.1207,
          address: 'Distribution Hub, Vancouver, BC',
        },
        odometer: 8650,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(19, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(19, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(today.setHours(6, 0, 0, 0)),
        location: {
          latitude: 49.2827,
          longitude: -123.1207,
          address: 'Distribution Hub, Vancouver, BC',
        },
        odometer: 8650,
        isDeleted: false,
        createdAt: new Date(today.setHours(6, 0, 0, 0)),
        updatedAt: new Date(today.setHours(6, 0, 0, 0)),
      },

      // Entries for driver 6 (Emma Rodriguez) - Currently on break
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(today.setHours(7, 30, 0, 0)),
        location: {
          latitude: 45.4215,
          longitude: -75.6972,
          address: 'Logistics Center, Ottawa, ON',
        },
        odometer: 3200,
        isDeleted: false,
        createdAt: new Date(today.setHours(7, 30, 0, 0)),
        updatedAt: new Date(today.setHours(7, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(today.setHours(12, 0, 0, 0)),
        location: {
          latitude: 45.3876,
          longitude: -75.6355,
          address: 'South Keys, Ottawa, ON',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(today.setHours(12, 0, 0, 0)),
        updatedAt: new Date(today.setHours(12, 0, 0, 0)),
      },

      // Previous work week for driver 1 (John Smith) - full week
      // Monday (7 days ago)
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(sevenDaysAgo.setHours(8, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 11950,
        isDeleted: false,
        createdAt: new Date(sevenDaysAgo.setHours(8, 0, 0, 0)),
        updatedAt: new Date(sevenDaysAgo.setHours(8, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(sevenDaysAgo.setHours(12, 0, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(sevenDaysAgo.setHours(12, 0, 0, 0)),
        updatedAt: new Date(sevenDaysAgo.setHours(12, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(sevenDaysAgo.setHours(13, 0, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        isDeleted: false,
        createdAt: new Date(sevenDaysAgo.setHours(13, 0, 0, 0)),
        updatedAt: new Date(sevenDaysAgo.setHours(13, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(sevenDaysAgo.setHours(17, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12050,
        isDeleted: false,
        createdAt: new Date(sevenDaysAgo.setHours(17, 0, 0, 0)),
        updatedAt: new Date(sevenDaysAgo.setHours(17, 0, 0, 0)),
      },

      // Tuesday (6 days ago)
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(sixDaysAgo.setHours(8, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12050,
        isDeleted: false,
        createdAt: new Date(sixDaysAgo.setHours(8, 0, 0, 0)),
        updatedAt: new Date(sixDaysAgo.setHours(8, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(sixDaysAgo.setHours(12, 15, 0, 0)),
        location: {
          latitude: 45.4945,
          longitude: -73.7449,
          address: 'West Island, Montreal, QC',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(sixDaysAgo.setHours(12, 15, 0, 0)),
        updatedAt: new Date(sixDaysAgo.setHours(12, 15, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(sixDaysAgo.setHours(12, 45, 0, 0)),
        location: {
          latitude: 45.4945,
          longitude: -73.7449,
          address: 'West Island, Montreal, QC',
        },
        isDeleted: false,
        createdAt: new Date(sixDaysAgo.setHours(12, 45, 0, 0)),
        updatedAt: new Date(sixDaysAgo.setHours(12, 45, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(sixDaysAgo.setHours(16, 30, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12100,
        isDeleted: false,
        createdAt: new Date(sixDaysAgo.setHours(16, 30, 0, 0)),
        updatedAt: new Date(sixDaysAgo.setHours(16, 30, 0, 0)),
      },

      // Wednesday (5 days ago)
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(fiveDaysAgo.setHours(7, 45, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12100,
        isDeleted: false,
        createdAt: new Date(fiveDaysAgo.setHours(7, 45, 0, 0)),
        updatedAt: new Date(fiveDaysAgo.setHours(7, 45, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(fiveDaysAgo.setHours(11, 30, 0, 0)),
        location: {
          latitude: 45.5016,
          longitude: -73.5557,
          address: 'Old Port, Montreal, QC',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(fiveDaysAgo.setHours(11, 30, 0, 0)),
        updatedAt: new Date(fiveDaysAgo.setHours(11, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(fiveDaysAgo.setHours(12, 15, 0, 0)),
        location: {
          latitude: 45.5016,
          longitude: -73.5557,
          address: 'Old Port, Montreal, QC',
        },
        isDeleted: false,
        createdAt: new Date(fiveDaysAgo.setHours(12, 15, 0, 0)),
        updatedAt: new Date(fiveDaysAgo.setHours(12, 15, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(fiveDaysAgo.setHours(17, 15, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12150,
        notes: 'Completing paperwork',
        isDeleted: false,
        createdAt: new Date(fiveDaysAgo.setHours(17, 15, 0, 0)),
        updatedAt: new Date(fiveDaysAgo.setHours(17, 15, 0, 0)),
      },

      // Thursday (4 days ago)
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(fourDaysAgo.setHours(8, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12150,
        isDeleted: false,
        createdAt: new Date(fourDaysAgo.setHours(8, 0, 0, 0)),
        updatedAt: new Date(fourDaysAgo.setHours(8, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(fourDaysAgo.setHours(12, 0, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(fourDaysAgo.setHours(12, 0, 0, 0)),
        updatedAt: new Date(fourDaysAgo.setHours(12, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(fourDaysAgo.setHours(13, 0, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        isDeleted: false,
        createdAt: new Date(fourDaysAgo.setHours(13, 0, 0, 0)),
        updatedAt: new Date(fourDaysAgo.setHours(13, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(fourDaysAgo.setHours(18, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12200,
        notes: 'Overtime due to late delivery',
        isDeleted: false,
        createdAt: new Date(fourDaysAgo.setHours(18, 0, 0, 0)),
        updatedAt: new Date(fourDaysAgo.setHours(18, 0, 0, 0)),
      },

      // Friday (3 days ago)
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(threeDaysAgo.setHours(8, 30, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12200,
        notes: 'Late start due to vehicle inspection',
        isDeleted: false,
        createdAt: new Date(threeDaysAgo.setHours(8, 30, 0, 0)),
        updatedAt: new Date(threeDaysAgo.setHours(8, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(threeDaysAgo.setHours(12, 30, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(threeDaysAgo.setHours(12, 30, 0, 0)),
        updatedAt: new Date(threeDaysAgo.setHours(12, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(threeDaysAgo.setHours(13, 30, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'Downtown, Montreal, QC',
        },
        isDeleted: false,
        createdAt: new Date(threeDaysAgo.setHours(13, 30, 0, 0)),
        updatedAt: new Date(threeDaysAgo.setHours(13, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(threeDaysAgo.setHours(16, 0, 0, 0)),
        location: {
          latitude: 45.5017,
          longitude: -73.5673,
          address: 'Warehouse, Montreal, QC',
        },
        odometer: 12250,
        notes: 'Early finish for weekend',
        isDeleted: false,
        createdAt: new Date(threeDaysAgo.setHours(16, 0, 0, 0)),
        updatedAt: new Date(threeDaysAgo.setHours(16, 0, 0, 0)),
      },

      // Additional entries for other drivers
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(yesterday.setHours(7, 0, 0, 0)),
        location: {
          latitude: 51.0447,
          longitude: -114.0719,
          address: 'Hub, Calgary, AB',
        },
        odometer: 7500,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(7, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(7, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(yesterday.setHours(15, 0, 0, 0)),
        location: {
          latitude: 51.0447,
          longitude: -114.0719,
          address: 'Hub, Calgary, AB',
        },
        odometer: 7650,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(15, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(15, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(yesterday.setHours(9, 0, 0, 0)),
        location: {
          latitude: 43.7764,
          longitude: -79.2318,
          address: 'Depot, Toronto, ON',
        },
        odometer: 4200,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(9, 0, 0, 0)),
        updatedAt: new Date(yesterday.setHours(9, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakStart,
        timestamp: new Date(yesterday.setHours(12, 30, 0, 0)),
        location: {
          latitude: 43.6548,
          longitude: -79.3883,
          address: 'Downtown, Toronto, ON',
        },
        notes: 'Lunch break',
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(12, 30, 0, 0)),
        updatedAt: new Date(yesterday.setHours(12, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.BreakEnd,
        timestamp: new Date(yesterday.setHours(13, 15, 0, 0)),
        location: {
          latitude: 43.6548,
          longitude: -79.3883,
          address: 'Downtown, Toronto, ON',
        },
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(13, 15, 0, 0)),
        updatedAt: new Date(yesterday.setHours(13, 15, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockOut,
        timestamp: new Date(yesterday.setHours(17, 30, 0, 0)),
        location: {
          latitude: 43.7764,
          longitude: -79.2318,
          address: 'Depot, Toronto, ON',
        },
        odometer: 4280,
        isDeleted: false,
        createdAt: new Date(yesterday.setHours(17, 30, 0, 0)),
        updatedAt: new Date(yesterday.setHours(17, 30, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(today.setHours(9, 0, 0, 0)),
        location: {
          latitude: 43.7764,
          longitude: -79.2318,
          address: 'Depot, Toronto, ON',
        },
        odometer: 4280,
        isDeleted: false,
        createdAt: new Date(today.setHours(9, 0, 0, 0)),
        updatedAt: new Date(today.setHours(9, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(today.setHours(8, 0, 0, 0)),
        location: {
          latitude: 43.6579,
          longitude: -79.3901,
          address: 'Toronto Center Depot, ON',
        },
        odometer: 9500,
        isDeleted: false,
        createdAt: new Date(today.setHours(8, 0, 0, 0)),
        updatedAt: new Date(today.setHours(8, 0, 0, 0)),
      },
      {
        id: uuidv4(),
        driverId: '550e8400-e29b-41d4-a716-************',
        vehicleId: '550e8400-e29b-41d4-a716-************',
        type: TimeClockEntryType.ClockIn,
        timestamp: new Date(today.setHours(7, 0, 0, 0)),
        location: {
          latitude: 45.5088,
          longitude: -73.5878,
          address: 'East End Depot, Montreal, QC',
        },
        odometer: 2100,
        isDeleted: false,
        createdAt: new Date(today.setHours(7, 0, 0, 0)),
        updatedAt: new Date(today.setHours(7, 0, 0, 0)),
      },
    ];
  }

  // Keep all the original methods...
  findByDriver(driverId: string): TimeClockEntryDomain[] {
    return this.entries
      .filter((entry) => entry.driverId === driverId && !entry.isDeleted)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  findByDriverAndDate(driverId: string, date: Date): TimeClockEntryDomain[] {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    return this.entries
      .filter(
        (entry) =>
          entry.driverId === driverId &&
          !entry.isDeleted &&
          entry.timestamp >= startOfDay &&
          entry.timestamp <= endOfDay,
      )
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  getDriverStatus(driverId: string): TimeClockSessionDomain {
    const now = new Date();
    const startOfDay = new Date(now);
    startOfDay.setHours(0, 0, 0, 0);
    const todayEntries = this.findByDriverAndDate(driverId, now);
    const session: TimeClockSessionDomain = {
      driverId,
      isOnDuty: false,
      isOnBreak: false,
      lastActivity:
        todayEntries.length > 0
          ? todayEntries[todayEntries.length - 1].timestamp
          : now,
      totalHoursToday: 0,
      totalBreakHoursToday: 0,
      entries: todayEntries,
    };
    let lastClockIn: Date | null = null;
    let lastBreakStart: Date | null = null;
    let totalWorkMs = 0;
    let totalBreakMs = 0;
    for (let i = 0; i < todayEntries.length; i++) {
      const entry = todayEntries[i];
      switch (entry.type) {
        case TimeClockEntryType.ClockIn:
          lastClockIn = entry.timestamp;
          session.currentShiftStart = entry.timestamp;
          session.vehicleId = entry.vehicleId;
          session.isOnDuty = true;
          break;
        case TimeClockEntryType.ClockOut:
          if (lastClockIn) {
            totalWorkMs += entry.timestamp.getTime() - lastClockIn.getTime();
            lastClockIn = null;
            session.currentShiftStart = undefined;
            session.isOnDuty = false;
          }
          break;
        case TimeClockEntryType.BreakStart:
          lastBreakStart = entry.timestamp;
          session.currentBreakStart = entry.timestamp;
          session.isOnBreak = true;
          break;
        case TimeClockEntryType.BreakEnd:
          if (lastBreakStart) {
            totalBreakMs +=
              entry.timestamp.getTime() - lastBreakStart.getTime();
            lastBreakStart = null;
            session.currentBreakStart = undefined;
            session.isOnBreak = false;
          }
          break;
      }
    }
    if (lastClockIn && !session.isOnBreak) {
      totalWorkMs += now.getTime() - lastClockIn.getTime();
    }
    if (lastBreakStart) {
      totalBreakMs += now.getTime() - lastBreakStart.getTime();
    }
    session.totalHoursToday = totalWorkMs / (1000 * 60 * 60);
    session.totalBreakHoursToday = totalBreakMs / (1000 * 60 * 60);
    return session;
  }

  createEntry(entryData: Partial<TimeClockEntryDomain>): TimeClockEntryDomain {
    if (!entryData.driverId) {
      throw new Error('Driver ID is required for time clock entry');
    }
    if (!entryData.type) {
      throw new Error('Entry type is required for time clock entry');
    }
    const newEntry: TimeClockEntryDomain = {
      id: uuidv4(),
      driverId: entryData.driverId,
      vehicleId: entryData.vehicleId,
      type: entryData.type,
      timestamp: entryData.timestamp || new Date(),
      location: entryData.location,
      notes: entryData.notes,
      photoUrl: entryData.photoUrl,
      odometer: entryData.odometer,
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.entries.push(newEntry);
    return newEntry;
  }

  // Add some new utility methods

  // Find entries by date range
  findByDriverAndDateRange(
    driverId: string,
    startDate: Date,
    endDate: Date,
  ): TimeClockEntryDomain[] {
    return this.entries
      .filter(
        (entry) =>
          entry.driverId === driverId &&
          !entry.isDeleted &&
          entry.timestamp >= startDate &&
          entry.timestamp <= endDate,
      )
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  // Get weekly summary for a driver
  getWeeklySummary(
    driverId: string,
    weekStartDate: Date,
  ): {
    totalHours: number;
    totalBreakHours: number;
    daysWorked: number;
    entries: TimeClockEntryDomain[];
  } {
    // Calculate week end date (7 days from start date)
    const weekEndDate = new Date(weekStartDate);
    weekEndDate.setDate(weekEndDate.getDate() + 6);
    weekEndDate.setHours(23, 59, 59, 999);

    // Get all entries for the week
    const weekEntries = this.findByDriverAndDateRange(
      driverId,
      weekStartDate,
      weekEndDate,
    );

    // Calculate work hours
    let totalWorkMs = 0;
    let totalBreakMs = 0;
    let lastClockIn: Date | null = null;
    let lastBreakStart: Date | null = null;
    const workedDates = new Set<string>();

    for (const entry of weekEntries) {
      // Track unique days worked
      const dateString = entry.timestamp.toISOString().split('T')[0];
      workedDates.add(dateString);

      switch (entry.type) {
        case TimeClockEntryType.ClockIn:
          lastClockIn = entry.timestamp;
          break;
        case TimeClockEntryType.ClockOut:
          if (lastClockIn) {
            totalWorkMs += entry.timestamp.getTime() - lastClockIn.getTime();
            lastClockIn = null;
          }
          break;
        case TimeClockEntryType.BreakStart:
          lastBreakStart = entry.timestamp;
          break;
        case TimeClockEntryType.BreakEnd:
          if (lastBreakStart) {
            totalBreakMs +=
              entry.timestamp.getTime() - lastBreakStart.getTime();
            lastBreakStart = null;
          }
          break;
      }
    }

    // Convert to hours
    const totalHours = totalWorkMs / (1000 * 60 * 60);
    const totalBreakHours = totalBreakMs / (1000 * 60 * 60);

    return {
      totalHours: Math.round(totalHours * 100) / 100,
      totalBreakHours: Math.round(totalBreakHours * 100) / 100,
      daysWorked: workedDates.size,
      entries: weekEntries,
    };
  }

  // Other methods remain the same
  deleteEntry(id: string): boolean {
    const index = this.entries.findIndex(
      (entry) => entry.id === id && !entry.isDeleted,
    );
    if (index === -1) return false;
    this.entries[index].isDeleted = true;
    this.entries[index].deletedAt = new Date();
    this.entries[index].updatedAt = new Date();
    return true;
  }

  getLatestEntryOfType(
    driverId: string,
    type: TimeClockEntryType,
  ): TimeClockEntryDomain | null {
    const driverEntries = this.findByDriver(driverId);
    const entriesOfType = driverEntries.filter((entry) => entry.type === type);
    if (entriesOfType.length === 0) return null;
    return entriesOfType.sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),
    )[0];
  }

  isDriverClockedIn(driverId: string): boolean {
    return this.getDriverStatus(driverId).isOnDuty;
  }

  isDriverOnBreak(driverId: string): boolean {
    return this.getDriverStatus(driverId).isOnBreak;
  }

  getEntryById(id: string): TimeClockEntryDomain | undefined {
    return this.entries.find((entry) => entry.id === id && !entry.isDeleted);
  }
}
