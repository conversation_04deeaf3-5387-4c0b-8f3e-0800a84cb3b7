import { Module } from '@nestjs/common';
import { MobileTimeClockController } from './timeclock.controller';
import { MobileTimeClockService } from './timeclock.service';
import { TimeClockStorage } from './storage/time-clock.storage';
import { MobileAuthModule } from '../auth/auth.module';
import { RelationalTimeClockSessionPersistenceModule } from '../../vehicle/time-clock-session/infrastructure/relational-persistence.module';
import { TimeClockSessionModule } from '../../vehicle/time-clock-session/time-clock-session.module';

@Module({
  imports: [
    MobileAuthModule,
    RelationalTimeClockSessionPersistenceModule,
    TimeClockSessionModule,
  ],
  controllers: [MobileTimeClockController],
  providers: [MobileTimeClockService, TimeClockStorage],
  exports: [MobileTimeClockService],
})
export class MobileTimeClockModule {}
