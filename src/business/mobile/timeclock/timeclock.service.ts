import { Injectable } from '@nestjs/common';
import { TimeClockStorage } from './storage/time-clock.storage';
import {
  TimeClockEntryDomain,
  TimeClockEntryType,
  TimeClockSessionDomain,
} from './domain/time-clock-entry';
import { ClockInDto } from './dto/clock-in.dto';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { HttpStatus } from '@nestjs/common';
import { ClockOutDto } from '@app/business/mobile/timeclock/dto/clock-out.dto';
import { BreakDto } from '@app/business/mobile/timeclock/dto/break.dto';

@Injectable()
export class MobileTimeClockService {
  constructor(private readonly timeClockStorage: TimeClockStorage) {}

  getDriverStatus(driverId: string): TimeClockSessionDomain {
    return this.timeClockStorage.getDriverStatus(driverId);
  }

  getDriverEntries(driverId: string, date?: Date): TimeClockEntryDomain[] {
    if (date) {
      return this.timeClockStorage.findByDriverAndDate(driverId, date);
    }
    return this.timeClockStorage.findByDriver(driverId);
  }

  getDriverEntriesByDateRange(
    driverId: string,
    startDate: Date,
    endDate: Date,
  ): Record<string, TimeClockEntryDomain[]> {
    const result: Record<string, TimeClockEntryDomain[]> = {};

    // Create a copy of the start date
    const currentDate = new Date(startDate);

    // Iterate through each day in the range
    while (currentDate <= endDate) {
      const dateString = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD
      result[dateString] = this.timeClockStorage.findByDriverAndDate(
        driverId,
        currentDate,
      );

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return result;
  }

  clockIn(driverId: string, clockInDto: ClockInDto): TimeClockEntryDomain {
    // Check if driver is already clocked in
    if (this.timeClockStorage.isDriverClockedIn(driverId)) {
      throw new AppException(
        'Driver is already clocked in',
        ErrorCode.TIMECLOCK_ALREADY_CLOCKED_IN,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Create clock-in entry
    const entry = this.timeClockStorage.createEntry({
      driverId,
      vehicleId: clockInDto.vehicleId,
      type: TimeClockEntryType.ClockIn,
      timestamp: new Date(),
      // location: clockInDto.location,
      // notes: clockInDto.notes,
      odometer: clockInDto.odometer,
      // photoUrl: clockInDto.photo ? `clock-in-${Date.now()}.jpg` : undefined,
    });

    return entry;
  }

  clockOut(driverId: string, clockOutDto: ClockOutDto): TimeClockEntryDomain {
    // Check if driver is clocked in
    if (!this.timeClockStorage.isDriverClockedIn(driverId)) {
      throw new AppException(
        'Driver is not clocked in',
        ErrorCode.TIMECLOCK_NOT_CLOCKED_IN,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if driver is on break
    if (this.timeClockStorage.isDriverOnBreak(driverId)) {
      throw new AppException(
        'Driver must end break before clocking out',
        ErrorCode.TIMECLOCK_NOT_CLOCKED_IN,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Get vehicle ID from latest clock-in entry
    const latestClockIn = this.timeClockStorage.getLatestEntryOfType(
      driverId,
      TimeClockEntryType.ClockIn,
    );

    // Create clock-out entry
    const entry = this.timeClockStorage.createEntry({
      driverId,
      vehicleId: latestClockIn?.vehicleId,
      type: TimeClockEntryType.ClockOut,
      timestamp: new Date(),
      // location: clockOutDto.location,
      // notes: clockOutDto.notes,
      odometer: clockOutDto.odometer,
      // photoUrl: clockOutDto.photo ? `clock-out-${Date.now()}.jpg` : undefined,
    });

    return entry;
  }

  startBreak(driverId: string, breakDto: BreakDto): TimeClockEntryDomain {
    // Check if driver is clocked in
    if (!this.timeClockStorage.isDriverClockedIn(driverId)) {
      throw new AppException(
        'Driver is not clocked in',
        ErrorCode.TIMECLOCK_NOT_CLOCKED_IN,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if driver is already on break
    if (this.timeClockStorage.isDriverOnBreak(driverId)) {
      throw new AppException(
        'Driver is already on break',
        ErrorCode.TIMECLOCK_ALREADY_ON_BREAK,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Get vehicle ID from latest clock-in entry
    const latestClockIn = this.timeClockStorage.getLatestEntryOfType(
      driverId,
      TimeClockEntryType.ClockIn,
    );

    // Create break-start entry
    const entry = this.timeClockStorage.createEntry({
      driverId,
      vehicleId: latestClockIn?.vehicleId,
      type: TimeClockEntryType.BreakStart,
      timestamp: new Date(),
      location: breakDto.location,
      notes: breakDto.notes,
    });

    return entry;
  }

  endBreak(driverId: string, breakDto: BreakDto): TimeClockEntryDomain {
    // Check if driver is on break
    if (!this.timeClockStorage.isDriverOnBreak(driverId)) {
      throw new AppException(
        'Driver is not on break',
        ErrorCode.TIMECLOCK_NOT_ON_BREAK,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Get vehicle ID from latest break-start entry
    const latestBreakStart = this.timeClockStorage.getLatestEntryOfType(
      driverId,
      TimeClockEntryType.BreakStart,
    );

    // Create break-end entry
    const entry = this.timeClockStorage.createEntry({
      driverId,
      vehicleId: latestBreakStart?.vehicleId,
      type: TimeClockEntryType.BreakEnd,
      timestamp: new Date(),
      location: breakDto.location,
      notes: breakDto.notes,
    });

    return entry;
  }

  getEntryById(id: string): TimeClockEntryDomain {
    const entry = this.timeClockStorage.getEntryById(id);
    if (!entry) {
      throw new AppException(
        `Time clock entry with ID ${id} not found`,
        ErrorCode.TIMECLOCK_ENTRY_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
    return entry;
  }

  calculateDailyHours(
    driverId: string,
    date: Date,
  ): { totalHours: number; totalBreakHours: number } {
    const entries = this.timeClockStorage.findByDriverAndDate(driverId, date);

    if (entries.length === 0) {
      return { totalHours: 0, totalBreakHours: 0 };
    }

    // Sort entries by timestamp
    entries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    let totalWorkMs = 0;
    let totalBreakMs = 0;
    let lastClockIn: Date | null = null;
    let lastBreakStart: Date | null = null;

    for (const entry of entries) {
      switch (entry.type) {
        case TimeClockEntryType.ClockIn:
          lastClockIn = entry.timestamp;
          break;

        case TimeClockEntryType.ClockOut:
          if (lastClockIn) {
            totalWorkMs += entry.timestamp.getTime() - lastClockIn.getTime();
            lastClockIn = null;
          }
          break;

        case TimeClockEntryType.BreakStart:
          lastBreakStart = entry.timestamp;
          break;

        case TimeClockEntryType.BreakEnd:
          if (lastBreakStart) {
            totalBreakMs +=
              entry.timestamp.getTime() - lastBreakStart.getTime();
            lastBreakStart = null;
          }
          break;
      }
    }

    // Handle unclosed periods if the date is today
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const targetDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
    );

    if (today.getTime() === targetDate.getTime()) {
      if (lastClockIn && !lastBreakStart) {
        totalWorkMs += now.getTime() - lastClockIn.getTime();
      }

      if (lastBreakStart) {
        totalBreakMs += now.getTime() - lastBreakStart.getTime();
      }
    }

    // Convert milliseconds to hours
    const totalHours = totalWorkMs / (1000 * 60 * 60);
    const totalBreakHours = totalBreakMs / (1000 * 60 * 60);

    return {
      totalHours: Math.round(totalHours * 100) / 100,
      totalBreakHours: Math.round(totalBreakHours * 100) / 100,
    };
  }
}
