import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class LocationDto {
  @ApiProperty({ example: 45.5017 })
  @IsNumber()
  latitude: number;

  @ApiProperty({ example: -73.5673 })
  @IsNumber()
  longitude: number;

  @ApiPropertyOptional({ example: '123 Main St, Montreal, QC' })
  @IsString()
  @IsOptional()
  address?: string;
}

export class ClockInDto {
  @ApiPropertyOptional({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  @IsOptional()
  vehicleId: string;

  @ApiPropertyOptional({ example: 12500 })
  @IsNumber()
  @IsOptional()
  odometer: number;
}
