import { TimeClockEntryType } from '../domain/time-clock-entry';
import { LocationDto } from '@app/business/mobile/timeclock/dto/clock-in.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class TimeClockEntryDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ enum: TimeClockEntryType })
  type: TimeClockEntryType;

  @ApiProperty({ example: '2023-01-01T08:00:00Z' })
  timestamp: Date;

  @ApiPropertyOptional({ example: '550e8400-e29b-41d4-a716-446655440000' })
  vehicleId?: string;

  @ApiPropertyOptional({ type: LocationDto })
  location?: LocationDto;

  @ApiPropertyOptional({ example: 12500 })
  odometer?: number;

  @ApiPropertyOptional({ example: 'Starting shift' })
  notes?: string;

  @ApiPropertyOptional({
    example: 'http://example.com/photos/clock-in-123.jpg',
  })
  photoUrl?: string;
}

export class TimeClockStatusDto {
  @ApiProperty({ example: true })
  isOnDuty: boolean;

  @ApiPropertyOptional({ example: '2023-01-01T08:00:00Z' })
  currentShiftStart?: Date;

  @ApiPropertyOptional({ example: null })
  currentBreakStart?: Date;

  @ApiProperty({ example: '2023-01-01T12:30:00Z' })
  lastActivity: Date;

  @ApiProperty({ example: 4.5 })
  totalHoursToday: number;

  @ApiProperty({ example: 0.5 })
  totalBreakHoursToday: number;
}

export class TimeClockHistoryResponseDto {
  @ApiProperty({ type: [TimeClockEntryDto] })
  entries: TimeClockEntryDto[];

  @ApiProperty({ example: '2023-01-01' })
  date: string;

  @ApiProperty({ example: 8.0 })
  totalHours: number;

  @ApiProperty({ example: 1.0 })
  totalBreakHours: number;
}
