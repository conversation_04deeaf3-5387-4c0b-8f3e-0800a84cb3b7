import { AutoMap } from '@automapper/classes';

export enum TimeClockEntryType {
  ClockIn = 'ClockIn',
  ClockOut = 'ClockOut',
  BreakStart = 'BreakStart',
  BreakEnd = 'BreakEnd',
}

export class TimeClockEntryDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  driverId: string;

  @AutoMap()
  vehicleId?: string;

  @AutoMap()
  type: TimeClockEntryType;

  @AutoMap()
  timestamp: Date;

  @AutoMap()
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };

  @AutoMap()
  notes?: string;

  @AutoMap()
  photoUrl?: string;

  @AutoMap()
  odometer?: number;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt?: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;
}

export class TimeClockSessionDomain {
  @AutoMap()
  driverId: string;

  @AutoMap()
  vehicleId?: string;

  @AutoMap()
  isOnDuty: boolean;

  @AutoMap()
  isOnBreak: boolean;

  @AutoMap()
  currentShiftStart?: Date;

  @AutoMap()
  currentBreakStart?: Date;

  @AutoMap()
  lastActivity: Date;

  @AutoMap()
  totalHoursToday: number;

  @AutoMap()
  totalBreakHoursToday: number;

  @AutoMap()
  entries: TimeClockEntryDomain[];
}
