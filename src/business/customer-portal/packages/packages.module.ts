import { Module } from '@nestjs/common';
import { CustomerPackagesController } from './packages.controller';
import { CustomerPackagesService } from './packages.service';
import { PackageTemplatesModule } from '../../order/package-templates/package-templates.module';
import { ContactsModule } from '../../user/contacts/contacts.module';

@Module({
  imports: [PackageTemplatesModule, ContactsModule],
  controllers: [CustomerPackagesController],
  providers: [CustomerPackagesService],
  exports: [CustomerPackagesService],
})
export class CustomerPackagesModule {}
