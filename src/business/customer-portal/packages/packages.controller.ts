import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiB<PERSON>erAuth,
  ApiCookieAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtContactAuthGuard } from '@core/auth/guards/jwt-contact-auth.guard';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { CustomerPackagesService } from './packages.service';
import { ListCustomerPackagesResponseDto } from './dto/list-customer-packages-response.dto';

@ApiTags('Customer Portal - Packages')
@Controller({
  path: 'customer-portal/packages',
  version: '1',
})
@UseGuards(JwtContactAuthGuard)
@ApiCookieAuth('contact_session_token')
@ApiBearerAuth()
export class CustomerPackagesController {
  constructor(
    private readonly customerPackagesService: CustomerPackagesService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all available package templates for the customer',
    description:
      'Returns all active package templates available for the customer',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of all available package templates',
    type: ListCustomerPackagesResponseDto,
  })
  @HttpCode(HttpStatus.OK)
  async getAllPackages(
    @CurrentUser() user: JwtPayload,
  ): Promise<ListCustomerPackagesResponseDto> {
    const customerId = user.sub;

    // Get all packages for this customer
    const packages =
    await this.customerPackagesService.getAllPackagesForCustomer(customerId);
    
    // Return the response
    return { packages };
  }
}
