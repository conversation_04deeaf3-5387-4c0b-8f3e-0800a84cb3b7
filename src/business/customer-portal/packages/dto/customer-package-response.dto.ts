import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PackageStatus } from '@app/business/order/package-templates/domain/package-template.types';

export class CustomerPackageResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier',
  })
  id: string;

  @ApiProperty({
    example: 'Standard Box',
    description: 'Name of the package template',
  })
  name: string;

  @ApiPropertyOptional({
    example: 'Standard box for most shipments',
    description: 'Description of the package template',
  })
  description?: string;

  @ApiProperty({
    enum: PackageStatus,
    example: PackageStatus.Active,
    description: 'Status of the package template',
  })
  status: PackageStatus;

  @ApiProperty({
    example: ['Box', 'Envelope', 'Pallet'],
    description: 'Array of supported package types',
    type: [String],
  })
  capabilities: string[];

  @ApiProperty({
    example: true,
    description: 'Whether dimensions are required',
  })
  dimensionsRequired: boolean;

  @ApiProperty({
    example: true,
    description: 'Whether weight is required',
  })
  weightRequired: boolean;

  @ApiPropertyOptional({
    example: 50.5,
    description: 'Maximum weight allowed',
  })
  maxWeight?: number;

  @ApiPropertyOptional({
    example: 100.5,
    description: 'Maximum volume allowed',
  })
  maxVolume?: number;

  @ApiPropertyOptional({
    example: 30,
    description: 'Width of the package',
  })
  width?: number;

  @ApiPropertyOptional({
    example: 20,
    description: 'Height of the package',
  })
  height?: number;

  @ApiPropertyOptional({
    example: 40,
    description: 'Length of the package',
  })
  length?: number;

  @ApiProperty({
    example: false,
    description: 'Whether signature is required',
  })
  requiresSignature: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether insurance is required',
  })
  requiresInsurance: boolean;

  @ApiPropertyOptional({
    example: 'Handle with care. Keep upright.',
    description: 'Special handling instructions',
  })
  specialHandlingInstructions?: string;

  @ApiPropertyOptional({
    example: { fragile: true, perishable: false },
    description: 'Additional metadata for the package',
  })
  metadata?: Record<string, any>;
}
