import { Module } from '@nestjs/common';
import { AddressModule } from './address/address.module';
import { CustomerPortalContactModule } from './contact/contact.module';
import { CustomerPortalCategoriesModule } from './categories/categories.module';
import { PricingModule } from './pricing/pricing.module';
import { CustomerPackagesModule } from './packages/packages.module';

@Module({
  imports: [
    AddressModule,
    CustomerPortalContactModule,
    CustomerPortalCategoriesModule,
    PricingModule,
    CustomerPackagesModule,
  ],
})
export class CustomerPortalModule {}
