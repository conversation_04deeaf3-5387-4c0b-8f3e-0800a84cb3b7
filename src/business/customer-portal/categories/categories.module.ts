import { Module } from '@nestjs/common';
import { CustomerPortalCategoriesController } from './categories.controller';
import { CustomerPortalCategoriesService } from './categories.service';
import { CustomerCategoriesModule } from '@app/business/user/customer-categories/customer-categories.module';
import { ContactsModule } from '@app/business/user/contacts/contacts.module';

@Module({
  imports: [CustomerCategoriesModule, ContactsModule],
  controllers: [CustomerPortalCategoriesController],
  providers: [CustomerPortalCategoriesService],
  exports: [CustomerPortalCategoriesService],
})
export class CustomerPortalCategoriesModule {}
