import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiCookieAuth,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtContactAuthGuard } from '@core/auth/guards/jwt-contact-auth.guard';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { CustomerPortalCategoriesService } from './categories.service';
import { ListCategoryResponseDto } from '@app/business/user/customer-categories/dto/category-response.dto';

@ApiTags('Customer Portal - Categories')
@Controller({
  path: 'customer-portal/categories',
  version: '1',
})
@UseGuards(JwtContactAuthGuard)
@ApiCookieAuth('contact_session_token')
@ApiBearerAuth()
export class CustomerPortalCategoriesController {
  constructor(
    private readonly categoriesService: CustomerPortalCategoriesService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all contact categories',
    description:
      'Returns all contact categories (departments) for the current tenant',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns all contact categories',
    type: [ListCategoryResponseDto],
  })
  @HttpCode(HttpStatus.OK)
  async findAll(
    @CurrentUser() user: JwtPayload,
  ): Promise<ListCategoryResponseDto[]> {
    return this.categoriesService.findContactCategories(user.sub);
  }
}
