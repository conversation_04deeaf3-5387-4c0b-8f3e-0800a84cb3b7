import { Injectable } from '@nestjs/common';
import { CustomerCategoriesService } from '@app/business/user/customer-categories/customer-categories.service';
import { ListCategoryResponseDto } from '@app/business/user/customer-categories/dto/category-response.dto';
import { CategoryType } from '@app/business/user/customer-categories/domain/customer-category';
import { ContactsService } from '@app/business/user/contacts/contacts.service';

@Injectable()
export class CustomerPortalCategoriesService {
  constructor(
    private readonly categoriesService: CustomerCategoriesService,
    private readonly contactsService: ContactsService,
  ) {}

  /**
   * Find all contact categories (departments) for the tenant of the authenticated contact
   * @param contactId The ID of the authenticated contact
   * @returns List of contact categories
   */
  async findContactCategories(
    contactId: string,
  ): Promise<ListCategoryResponseDto[]> {
    // Get the contact to find the tenant ID
    const contact = await this.contactsService.findById(contactId);

    if (!contact.tenantId) {
      return [];
    }

    // Get all categories of type CONTACT for the tenant
    return this.categoriesService.findAll(
      contact.tenantId,
      CategoryType.CONTACT,
    );
  }
}
