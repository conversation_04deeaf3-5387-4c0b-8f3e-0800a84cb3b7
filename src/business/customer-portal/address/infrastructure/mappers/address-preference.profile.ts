import { createMap, forMember, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { Mapper } from '@automapper/core';
import { AddressPreferenceDomain } from '../../domain/address-preference';
import { ContactAddressPreferenceEntity } from '../../../../address/addresses/infrastructure/entities/contact-address-preferences.entity';

@Injectable()
export class AddressMapperProfile extends AutomapperProfile {
    constructor(@InjectMapper() mapper: Mapper) {
        super(mapper);
    }

    override get profile() {
        return (mapper) => {
            createMap(
                mapper,
                AddressPreferenceDomain,
                ContactAddressPreferenceEntity,
            );
            createMap(
                mapper,
                ContactAddressPreferenceEntity,
                AddressPreferenceDomain,
            );
        };
    }
}
