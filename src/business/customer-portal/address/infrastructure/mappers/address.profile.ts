import { Injectable } from '@nestjs/common';
import { Mapper, createMap, forMember, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { AddressEntity } from '../../../../address/addresses/infrastructure/entities/address.entity';
import { AddressDomain } from '../../domain/address';
import {
  CreateAddressDto,
  UpdateAddressDto,
} from '../../dto/create-address.dto';
import { GetAddressDto } from '../../dto/get-address.dto';

@Injectable()
export class AddressProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        CreateAddressDto,
        AddressDomain,
      );

      createMap(mapper, AddressDomain, AddressEntity);
      createMap(mapper, UpdateAddressDto, AddressDomain);
      createMap(mapper, UpdateAddressDto, AddressDomain);

      createMap(
        mapper,
        AddressEntity,
        AddressDomain,
        forMember(
          (dest) => dest.customer,
          mapFrom((src) => src.customer),
        ),
      );

      createMap(
        mapper,
        AddressDomain,
        GetAddressDto,
        forMember(
          (dest) => dest.customer,
          mapFrom((src) => src.customer?.companyName || ''),
        ),
      );

      // Custom mapping for duplicate address
      createMap(
        mapper,
        AddressDomain,
        AddressDomain,
        forMember(
          (dest) => dest.id,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => `${src.name} - Copy`),
        ),
        forMember(
          (dest) => dest.companyName,
          mapFrom((src) => `${src.companyName} - Copy`),
        ),
        forMember(
          (dest) => dest.createdAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.createdBy,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedBy,
          mapFrom(() => undefined),
        ),
      );
    };
  }
}
