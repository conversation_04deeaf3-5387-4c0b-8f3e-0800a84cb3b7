import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AddressEntity } from '../../../address/addresses/infrastructure/entities/address.entity';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';
import { AddressRepository } from './repositories/address.repository';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { AddressFilterConfig } from '../address-filter.config';
import { ContactAddressPreferenceEntity } from '../../../address/addresses/infrastructure/entities/contact-address-preferences.entity';
import { ContactEntity } from '../../../user/contacts/infrastructure/persistence/relational/entities/contact.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AddressEntity, UserEntity , ContactAddressPreferenceEntity , ContactEntity])],
  providers: [
    AddressRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(AddressFilterConfig()),
    },
  ],
  exports: [AddressRepository],
})
export class RelationalAddressPersistenceModule {}
