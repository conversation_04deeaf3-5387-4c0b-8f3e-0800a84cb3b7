import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

export class AddressPreferenceDto {
  @AutoMap()
  @ApiProperty({
    example: true,
    description: 'Set this address as favorite for pickup',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isFavoriteForPickup?: boolean;

  @AutoMap()
  @ApiProperty({
    example: true,
    description: 'Set this address as favorite for delivery',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isFavoriteForDelivery?: boolean;

  @AutoMap()
  @ApiProperty({
    example: true,
    description:
      'Set this address as default for pickup. Only one address can be default for pickup at a time.',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefaultForPickup?: boolean;

  @AutoMap()
  @ApiProperty({
    example: false,
    description:
      'Set this address as default for delivery. Only one address can be default for delivery at a time.',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefaultForDelivery?: boolean;
}

export class AddressPreferenceResponseDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: true })
  isFavoriteForPickup: boolean;

  @AutoMap()
  @ApiProperty({ example: true })
  isFavoriteForDelivery: boolean;

  @AutoMap()
  @ApiProperty({ example: true })
  isDefaultForPickup: boolean;

  @AutoMap()
  @ApiProperty({ example: false })
  isDefaultForDelivery: boolean;
}
