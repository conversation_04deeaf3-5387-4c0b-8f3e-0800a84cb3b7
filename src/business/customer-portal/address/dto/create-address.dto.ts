import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsBoolean,
  IsN<PERSON>ber,
  Min,
  Max,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class UpdateAddressDto {
  @AutoMap()
  @ApiProperty({ example: 'Insight Technologies' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ example: 'Swift Logistics' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  companyName: string;

  @AutoMap()
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @Transform(trimTransformer)
  email: string;

  @AutoMap()
  @ApiProperty({ example: 'US' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  countryCode: string;

  @AutoMap()
  @ApiProperty({ example: '1234567890' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @AutoMap()
  @ApiProperty({ example: 101 })
  @IsString()
  @IsOptional()
  phoneExtension: number;

  @AutoMap()
  @ApiProperty({ example: '123 Main St' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  addressLine1: string;

  @AutoMap()
  @ApiProperty({ example: 'Apt 2' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  addressLine2: string;

  @AutoMap()
  @ApiProperty({ example: 'Montreal' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  city: string;

  @AutoMap()
  @ApiProperty({ example: 'Quebec' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  province: string;

  @AutoMap()
  @ApiProperty({ default: 'H45 2Y7' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  postalCode: string;

  @AutoMap()
  @ApiProperty({ example: 'Canada' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  country: string;

  @AutoMap()
  @ApiProperty({ example: 'Address notes' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  notes: string;

  @AutoMap()
  @ApiProperty({
    example: 45.508888,
    description: 'Latitude coordinate of the address location',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Min(-90)
  @Max(90)
  latitude: number;

  @AutoMap()
  @ApiProperty({
    example: -73.561668,
    description: 'Longitude coordinate of the address location',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Min(-180)
  @Max(180)
  longitude: number;

  @AutoMap()
  @ApiProperty({ example: true, default: false })
  @IsBoolean()
  @IsOptional()
  isFavoriteForPickup: boolean;

  @AutoMap()
  @ApiProperty({ example: true, default: false })
  @IsBoolean()
  @IsOptional()
  isFavoriteForDelivery: boolean;

  @AutoMap()
  @ApiProperty({ example: true, default: false })
  @IsBoolean()
  @IsOptional()
  isDefaultForPickup: boolean;

  @AutoMap()
  @ApiProperty({ example: true, default: false })
  @IsBoolean()
  @IsOptional()
  isDefaultForDelivery: boolean;
}

export class CreateAddressDto extends UpdateAddressDto {
  // Note: In customer portal, we don't need to pass customerId as it will be derived from the authenticated contact
}
