import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';
import { CreateAddressDto } from './create-address.dto';

export class GetAddressDto extends CreateAddressDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: 'Acme Inc.' })
  customer: string;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  createdBy: string;

  @AutoMap()
  @ApiProperty({ example: 'User2' })
  updatedBy: string;
}

export class GetAllAddressDto extends PageResponse {
  @ApiProperty({ type: [GetAddressDto] })
  data: GetAddressDto[];
}
