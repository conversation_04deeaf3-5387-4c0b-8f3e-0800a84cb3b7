import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { ContactsService } from '@app/business/user/contacts/contacts.service';

@Injectable()
export class PrimaryContactGuard implements CanActivate {
  constructor(private readonly contactsService: ContactsService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const contactId = request.user?.sub;
    if (!contactId) {
      throw new ForbiddenException('Authentication required');
    }

    // Get the contact from the database
    const contact = await this.contactsService.findById(contactId);
    // Check if the contact is primary
    if (!contact.isPrimary) {
      throw new ForbiddenException(
        'Only primary contacts can perform this operation',
      );
    }

    return true;
  }
}
