import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Query,
  HttpCode,
  HttpStatus,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiCookieAuth,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { CustomerPortalContactService } from './contact.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { GetContactDto, GetAllContactsDto } from './dto/get-contact.dto';
import { JwtContactAuthGuard } from '@core/auth/guards/jwt-contact-auth.guard';
import { PrimaryContactGuard } from './guards/primary-contact.guard';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { Request } from 'express';

@ApiTags('Customer Portal - Contacts')
@Controller({
  path: 'customer-portal/contacts',
  version: '1',
})
@UseGuards(JwtContactAuthGuard, PrimaryContactGuard)
@ApiCookieAuth('contact_session_token')
@ApiBearerAuth()
export class CustomerPortalContactController {
  constructor(private readonly contactService: CustomerPortalContactService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new contact' })
  @ApiBody({ type: CreateContactDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The contact has been successfully created.',
    type: GetContactDto,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createContactDto: CreateContactDto,
  ) {
    return this.contactService.create(user.sub, createContactDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all contacts for the customer' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns all contacts for the customer',
    type: GetAllContactsDto,
  })
  @HttpCode(HttpStatus.OK)
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ) {
    return this.contactService.findAll(user.sub, request.query, filter);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a contact by ID' })
  @ApiParam({ name: 'id', description: 'Contact ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns the contact',
    type: GetContactDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @HttpCode(HttpStatus.OK)
  async findOne(@CurrentUser() user: JwtPayload, @Param('id') id: string) {
    return this.contactService.findOne(user.sub, id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a contact' })
  @ApiParam({ name: 'id', description: 'Contact ID' })
  @ApiBody({ type: UpdateContactDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The contact has been successfully updated.',
    type: GetContactDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @HttpCode(HttpStatus.OK)
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateContactDto: UpdateContactDto,
  ) {
    return this.contactService.update(user.sub, id, updateContactDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a contact' })
  @ApiParam({ name: 'id', description: 'Contact ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The contact has been successfully deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string) {
    return this.contactService.remove(user.sub, id);
  }

  @Post(':id/reset-password')
  @ApiOperation({ summary: "Reset a contact's password" })
  @ApiParam({ name: 'id', description: 'Contact ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The password has been successfully reset.',
    schema: {
      properties: {
        newPassword: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @HttpCode(HttpStatus.OK)
  async resetPassword(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ) {
    return this.contactService.resetPassword(user.sub, id);
  }
}
