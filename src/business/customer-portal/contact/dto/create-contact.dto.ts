import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsArray,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';
import { CategorySelectionDto } from '@app/business/user/customer-categories/dto/category-selection.dto';
import {
  ContactPermissions,
  DEFAULT_CONTACT_PERMISSIONS,
} from '@app/business/user/contacts/domain/contact-permissions';

export class CreateContactDto {
  @ApiProperty({ example: '<PERSON>', description: 'Full name of the contact' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the contact',
  })
  @IsEmail()
  @IsNotEmpty()
  @Transform(trimTransformer)
  email: string;

  @ApiPropertyOptional({
    example: '+1',
    description: 'Phone country code',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  phoneCountryCode?: string;

  @ApiPropertyOptional({
    example: '1234567890',
    description: 'Phone number',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  phoneNumber?: string;

  @ApiPropertyOptional({
    example: '123',
    description: 'Phone extension',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  phoneExtension?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the contact is active',
    default: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean = true;

  @ApiProperty({
    type: [CategorySelectionDto],
    description: 'Categories to assign to the contact',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CategorySelectionDto)
  @IsOptional()
  categories?: CategorySelectionDto[];

  @ApiPropertyOptional({
    example: { key: 'value' },
    description: 'Additional metadata for the contact',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiProperty({
    type: ContactPermissions,
    description: 'Permissions to assign to the contact',
    default: DEFAULT_CONTACT_PERMISSIONS,
  })
  @IsObject()
  permissions: Record<string, any> = { ...DEFAULT_CONTACT_PERMISSIONS };
}
