import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsBoolean,
  IsArray,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';
import { CategorySelectionDto } from '@app/business/user/customer-categories/dto/category-selection.dto';

export class UpdateContactDto {
  @ApiPropertyOptional({
    example: '<PERSON>',
    description: 'Full name of the contact',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  name?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Email address of the contact',
  })
  @IsEmail()
  @IsOptional()
  @Transform(trimTransformer)
  email?: string;

  @ApiPropertyOptional({
    example: '+1',
    description: 'Phone country code',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  phoneCountryCode?: string;

  @ApiPropertyOptional({
    example: '1234567890',
    description: 'Phone number',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  phoneNumber?: string;

  @ApiPropertyOptional({
    example: '123',
    description: 'Phone extension',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  phoneExtension?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether the contact is active',
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    type: [CategorySelectionDto],
    description: 'Categories to assign to the contact',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CategorySelectionDto)
  @IsOptional()
  categories?: CategorySelectionDto[];

  @ApiPropertyOptional({
    example: { key: 'value' },
    description: 'Additional metadata for the contact',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Permissions to assign to the contact',
    example: { address: true, prices: false, invoices: false },
  })
  @IsObject()
  @IsOptional()
  permissions?: Record<string, any>;
}
