import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';

export class ContactCategoryDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ example: 'Category Name' })
  name: string;
}

export class GetContactDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ example: 'John Doe' })
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiPropertyOptional({ example: '+1' })
  phoneCountryCode?: string;

  @ApiPropertyOptional({ example: '1234567890' })
  phoneNumber?: string;

  @ApiPropertyOptional({ example: '123' })
  phoneExtension?: string;

  @ApiProperty({ example: true })
  isActive: boolean;

  @ApiPropertyOptional({ example: true })
  isPrimary?: boolean;

  @ApiPropertyOptional({ type: [ContactCategoryDto] })
  categories?: ContactCategoryDto[];

  @ApiPropertyOptional()
  metadata?: Record<string, any>;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class GetAllContactsDto extends PageResponse {
  @ApiProperty({ type: [GetContactDto] })
  data: GetContactDto[];
}
