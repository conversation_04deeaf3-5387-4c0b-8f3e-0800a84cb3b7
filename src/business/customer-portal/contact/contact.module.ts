import { Modu<PERSON> } from '@nestjs/common';
import { CustomerPortalContactController } from './contact.controller';
import { CustomerPortalContactService } from './contact.service';
import { ContactsModule } from '@app/business/user/contacts/contacts.module';
import { PrimaryContactGuard } from './guards/primary-contact.guard';

@Module({
  imports: [ContactsModule],
  controllers: [CustomerPortalContactController],
  providers: [CustomerPortalContactService, PrimaryContactGuard],
  exports: [CustomerPortalContactService],
})
export class CustomerPortalContactModule {}
