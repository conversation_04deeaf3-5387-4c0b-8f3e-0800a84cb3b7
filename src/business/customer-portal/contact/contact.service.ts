import { Injectable, ForbiddenException } from '@nestjs/common';
import { ContactsService } from '@app/business/user/contacts/contacts.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { Contact } from '@app/business/user/contacts/domain/contact';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { ParsedQs } from 'qs';
import { CreateContactDto as BusinessCreateContactDto } from '@app/business/user/contacts/dto/create-contact.dto';
import { UpdateContactDto as BusinessUpdateContactDto } from '@app/business/user/contacts/dto/update-contact.dto';
import { CategorySelectionDto } from '@app/business/user/customer-categories/dto/category-selection.dto';

@Injectable()
export class CustomerPortalContactService {
  constructor(private readonly contactsService: ContactsService) {}

  /**
   * Convert our portal DTO to the business DTO format
   */
  private convertToBusinessCreateDto(
    dto: CreateContactDto,
  ): BusinessCreateContactDto {
    // Convert string categories to CategorySelectionDto objects if needed
    const categories = dto.categories?.map((category) => {
      if (typeof category === 'string') {
        return { name: category } as CategorySelectionDto;
      }
      return category;
    });

    // Create a new business DTO with the converted data
    const businessDto = new BusinessCreateContactDto();
    businessDto.name = dto.name;
    businessDto.email = dto.email;
    businessDto.phoneCountryCode = dto.phoneCountryCode;
    businessDto.phoneNumber = dto.phoneNumber;
    businessDto.phoneExtension = dto.phoneExtension;
    businessDto.isActive = dto.isActive;
    businessDto.categories = categories;
    businessDto.permissions = dto.permissions;
    businessDto.metadata = dto.metadata;

    return businessDto;
  }

  /**
   * Convert our portal DTO to the business DTO format
   */
  private convertToBusinessUpdateDto(
    dto: UpdateContactDto,
  ): BusinessUpdateContactDto {
    // Convert string categories to CategorySelectionDto objects if needed
    const categories = dto.categories?.map((category) => {
      if (typeof category === 'string') {
        return { name: category } as CategorySelectionDto;
      }
      return category;
    });

    // Create a new business DTO with the converted data
    const businessDto = new BusinessUpdateContactDto();

    if (dto.name !== undefined) businessDto.name = dto.name;
    if (dto.email !== undefined) businessDto.email = dto.email;
    if (dto.phoneCountryCode !== undefined)
      businessDto.phoneCountryCode = dto.phoneCountryCode;
    if (dto.phoneNumber !== undefined)
      businessDto.phoneNumber = dto.phoneNumber;
    if (dto.phoneExtension !== undefined)
      businessDto.phoneExtension = dto.phoneExtension;
    if (dto.isActive !== undefined) businessDto.isActive = dto.isActive;
    if (categories !== undefined) businessDto.categories = categories;
    if (dto.permissions !== undefined)
      businessDto.permissions = dto.permissions;
    if (dto.metadata !== undefined) businessDto.metadata = dto.metadata;

    return businessDto;
  }

  /**
   * Get all contacts for a customer, excluding the currently logged-in contact
   * @param contactId The ID of the primary contact making the request
   * @param query Query parameters for filtering
   * @param filter Base filter parameters
   * @returns Paginated list of contacts
   */
  async findAll(
    contactId: string,
    query: ParsedQs,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<Contact>> {
    // Get the primary contact to find the customer ID
    const primaryContact = await this.contactsService.findById(contactId);

    if (!primaryContact.isPrimary) {
      throw new ForbiddenException(
        'Only primary contacts can view all contacts',
      );
    }

    // Get all contacts for the customer
    const result = await this.contactsService.filter(
      primaryContact.userId,
      query,
      filter,
    );

    // Filter out the currently logged-in contact
    result.data = result.data.filter((contact) => contact.id !== contactId);

    // Update the total count to reflect the filtered results
    result.total = result.total > 0 ? result.total - 1 : 0;

    // Recalculate total pages if needed
    if (result.limit > 0) {
      result.totalPages = Math.ceil(result.total / result.limit);
      result.hasNextPage = result.page < result.totalPages;
    }

    return result;
  }

  /**
   * Get a specific contact by ID
   * @param primaryContactId The ID of the primary contact making the request
   * @param contactId The ID of the contact to retrieve
   * @returns The contact details
   */
  async findOne(primaryContactId: string, contactId: string): Promise<Contact> {
    // Get the primary contact to find the customer ID
    const primaryContact =
      await this.contactsService.findById(primaryContactId);

    if (!primaryContact.isPrimary) {
      throw new ForbiddenException(
        'Only primary contacts can view contact details',
      );
    }

    // Get the requested contact
    const contact = await this.contactsService.findById(contactId);

    // Verify the contact belongs to the same customer
    if (contact.userId !== primaryContact.userId) {
      throw new ForbiddenException(
        'Contact does not belong to your customer account',
      );
    }

    return contact;
  }

  /**
   * Create a new contact
   * @param primaryContactId The ID of the primary contact making the request
   * @param createContactDto The contact data to create
   * @returns The created contact
   */
  async create(
    primaryContactId: string,
    createContactDto: CreateContactDto,
  ): Promise<Contact> {
    // Get the primary contact to find the customer ID and tenant ID
    const primaryContact =
      await this.contactsService.findById(primaryContactId);

    if (!primaryContact.isPrimary) {
      throw new ForbiddenException(
        'Only primary contacts can create new contacts',
      );
    }

    // Make sure we have a tenant ID
    if (!primaryContact.tenantId) {
      throw new Error('Primary contact does not have a tenant ID');
    }

    // Convert our DTO to the business format
    const businessDto = this.convertToBusinessCreateDto(createContactDto);

    // Create the new contact
    return this.contactsService.create(
      primaryContact.tenantId,
      primaryContact.userId,
      businessDto,
    );
  }

  /**
   * Update an existing contact
   * @param primaryContactId The ID of the primary contact making the request
   * @param contactId The ID of the contact to update
   * @param updateContactDto The contact data to update
   * @returns The updated contact
   */
  async update(
    primaryContactId: string,
    contactId: string,
    updateContactDto: UpdateContactDto,
  ): Promise<Contact> {
    // Get the primary contact to find the customer ID and tenant ID
    const primaryContact =
      await this.contactsService.findById(primaryContactId);

    if (!primaryContact.isPrimary) {
      throw new ForbiddenException('Only primary contacts can update contacts');
    }

    // Make sure we have a tenant ID
    if (!primaryContact.tenantId) {
      throw new Error('Primary contact does not have a tenant ID');
    }

    // Get the contact to update
    const contactToUpdate = await this.contactsService.findById(contactId);

    // Verify the contact belongs to the same customer
    if (contactToUpdate.userId !== primaryContact.userId) {
      throw new ForbiddenException(
        'Contact does not belong to your customer account',
      );
    }

    // Convert our DTO to the business format
    const businessDto = this.convertToBusinessUpdateDto(updateContactDto);

    // Update the contact
    return this.contactsService.update(
      contactId,
      primaryContact.userId,
      primaryContact.tenantId,
      businessDto,
    );
  }

  /**
   * Delete a contact
   * @param primaryContactId The ID of the primary contact making the request
   * @param contactId The ID of the contact to delete
   */
  async remove(primaryContactId: string, contactId: string): Promise<void> {
    // Get the primary contact to find the customer ID
    const primaryContact =
      await this.contactsService.findById(primaryContactId);

    if (!primaryContact.isPrimary) {
      throw new ForbiddenException('Only primary contacts can delete contacts');
    }

    // Get the contact to delete
    const contactToDelete = await this.contactsService.findById(contactId);

    // Verify the contact belongs to the same customer
    if (contactToDelete.userId !== primaryContact.userId) {
      throw new ForbiddenException(
        'Contact does not belong to your customer account',
      );
    }

    // Prevent deletion of the primary contact
    if (contactToDelete.isPrimary) {
      throw new ForbiddenException('Primary contacts cannot be deleted');
    }

    // Delete the contact
    await this.contactsService.delete(contactId, primaryContact.userId);
  }

  /**
   * Reset a contact's password
   * @param primaryContactId The ID of the primary contact making the request
   * @param contactId The ID of the contact to reset password
   * @returns Object containing the new password
   */
  async resetPassword(
    primaryContactId: string,
    contactId: string,
  ): Promise<{ newPassword: string }> {
    // Get the primary contact to find the customer ID
    const primaryContact =
      await this.contactsService.findById(primaryContactId);

    if (!primaryContact.isPrimary) {
      throw new ForbiddenException('Only primary contacts can reset passwords');
    }

    // Get the contact to reset password
    const contactToReset = await this.contactsService.findById(contactId);

    // Verify the contact belongs to the same customer
    if (contactToReset.userId !== primaryContact.userId) {
      throw new ForbiddenException(
        'Contact does not belong to your customer account',
      );
    }

    // Reset the password
    return this.contactsService.resetPassword(contactId, primaryContact.userId);
  }
}
