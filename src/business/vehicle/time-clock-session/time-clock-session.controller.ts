import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { TimeClockSessionService } from './time-clock-session.service';
import { TimeClockSessionDomain } from './domain/time-clock-session';
import { CreateTimeClockSessionDto } from './dto/create-time-clock-session.dto';
import {
  GetAllTimeClockSessionDto,
  GetTimeClockSessionDto,
} from './dto/get-time-clock-session.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { TimeClockSessionOperationNotAllowedException } from '../../../utils/errors/exceptions/vehicle-exceptions';
import { Request } from 'express';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';

@ApiTags('Business - Vehicle - Time Clock Session')
@Controller({
  path: 'timeClockSession',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class TimeClockSessionController {
  constructor(
    private readonly timeClockSessionService: TimeClockSessionService,
    private readonly secureFilterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Time Clock Session' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createTimeClockSessionDto: CreateTimeClockSessionDto,
  ): Promise<TimeClockSessionDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new TimeClockSessionOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      const timeClockSessionDomain = this.mapper.map(
        createTimeClockSessionDto,
        CreateTimeClockSessionDto,
        TimeClockSessionDomain,
      );
      timeClockSessionDomain.tenantId = tenantId;
      const timeClockSession = await this.timeClockSessionService.create(
        timeClockSessionDomain,
      );
      return timeClockSession;
    } catch (error) {
      throw error;
    }
  }

  @Get('/vehicle/:vehicleId')
  @ApiOperation({
    summary:
      'Find time clock session by vehicle id with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllTimeClockSessionDto })
  async getTimeClockSessionList(
    @Param('vehicleId') vehicleId: string,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllTimeClockSessionDto> {
    try {
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );

      const result = await this.timeClockSessionService.getTimeClockSessionList(
        vehicleId,
        combinedFilter,
      );

      const mappedData = this.mapper.mapArray(
        result.data,
        TimeClockSessionDomain,
        GetTimeClockSessionDto,
      );

      const response: GetAllTimeClockSessionDto = {
        totalDistance: result.totalDistance,
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Find time clock session by time clock session Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetTimeClockSessionDto })
  async getTimeClockSessionDetails(
    @Param('id') id: string,
  ): Promise<GetTimeClockSessionDto> {
    try {
      const responseDomain =
        await this.timeClockSessionService.getTimeClockSessionDetails(id);
      const response = this.mapper.map(
        responseDomain,
        TimeClockSessionDomain,
        GetTimeClockSessionDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update time clock session by time clock session Id',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateTimeClockSessionDetails(
    @Param('id') id: string,
    @Body() updateTimeClockSessionDto: CreateTimeClockSessionDto,
  ): Promise<void> {
    try {
      const timeClockSession = this.mapper.map(
        updateTimeClockSessionDto,
        CreateTimeClockSessionDto,
        TimeClockSessionDomain,
      );
      timeClockSession.id = id;
      await this.timeClockSessionService.updateTimeClockSessionDetails(
        timeClockSession,
      );
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete time clock session by time clock session Id',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteTimeClockSession(@Param('id') id: string): Promise<void> {
    try {
      await this.timeClockSessionService.deleteTimeClockSession(id);
      return;
    } catch (error) {
      throw error;
    }
  }
}
