import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsUUID,
  IsPositive,
  IsDateString,
  IsOptional,
  IsEnum,
  IsNumber,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';
import { TimeClockSessionSource } from '../domain/time-clock-session.types';

export class CreateTimeClockSessionDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsUUID(4)
  @IsNotEmpty()
  vehicleId: string;

  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsUUID(4)
  @IsNotEmpty()
  driverId: string;

  @AutoMap()
  @ApiProperty({ example: 24.23 })
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: 'Distance must be a valid number with up to 2 decimal places' },
  )
  @IsPositive()
  @IsNotEmpty()
  @Transform(({ value }) => (value && value !== '' ? Number(value) : undefined))
  distanceTraveled: number;

  @AutoMap()
  @ApiProperty()
  @Transform(trimTransformer)
  @IsNotEmpty()
  @IsDateString({ strict: true })
  startTime: Date;

  @AutoMap()
  @ApiProperty()
  @Transform(trimTransformer)
  @IsOptional()
  @IsDateString({ strict: true })
  endTime: Date;

  @AutoMap()
  @ApiProperty({ enum: TimeClockSessionSource })
  @IsOptional()
  @IsEnum(TimeClockSessionSource)
  source: string;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  @IsString()
  @Transform(trimTransformer)
  @IsOptional()
  addedBy: string;
}
