import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';
import { TimeClockSessionStatus } from '../domain/time-clock-session.types';
import { CreateTimeClockSessionDto } from './create-time-clock-session.dto';

export class GetTimeClockSessionDto extends CreateTimeClockSessionDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ enum: TimeClockSessionStatus })
  status: TimeClockSessionStatus;

  @AutoMap()
  @ApiProperty({ example: 1.6 })
  totalHours: number;

  @AutoMap()
  @ApiProperty({ example: 'John Doe' })
  driver: string;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  createdBy: string;

  @AutoMap()
  @ApiProperty({ example: 'User2' })
  updatedBy: string;
}

export class GetAllTimeClockSessionDto extends PageResponse {
  @ApiProperty({ example: 13 })
  totalDistance: number;

  @ApiProperty({ type: [GetTimeClockSessionDto] })
  data: GetTimeClockSessionDto[];
}
