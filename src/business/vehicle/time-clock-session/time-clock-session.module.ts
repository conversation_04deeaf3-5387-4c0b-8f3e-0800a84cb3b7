import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { TimeClockSessionProfile } from './infrastructure/mappers/time-clock-session.profile';
import { AutomapperModule } from '@automapper/nestjs';
import { TimeClockSessionController } from './time-clock-session.controller';
import { RelationalTimeClockSessionPersistenceModule } from './infrastructure/relational-persistence.module';
import { classes } from '@automapper/classes';
import { TimeClockSessionService } from './time-clock-session.service';
import { RelationalVehiclePersistenceModule } from '@app/business/vehicle/vehicles/infrastructure/relational-persistence.module';
import { RelationalUserPersistenceModule } from '@app/business/user/users/infrastructure/relational-persistence.module';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { TimeClockSessionFilterConfig } from './time-clock-session-filter.config';

@Module({
  imports: [
    RelationalTimeClockSessionPersistenceModule,
    RelationalVehiclePersistenceModule,
    RelationalUserPersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [TimeClockSessionController],
  providers: [
    TimeClockSessionService,
    TimeClockSessionProfile,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(TimeClockSessionFilterConfig()),
    },
  ],
  exports: [TimeClockSessionService],
})
export class TimeClockSessionModule {}
