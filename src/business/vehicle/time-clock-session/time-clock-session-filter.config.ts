import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const TimeClockSessionFilterConfig = () => {
  const fields = [
    {
      fieldName: 'startTime',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
          FilterOperator.BETWEEN,
        ],
        validationMessage: 'Invalid start time format',
      },
    },
    {
      fieldName: 'endTime',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
          FilterOperator.BETWEEN,
        ],
        validationMessage: 'Invalid end time format',
      },
    },
    {
      fieldName: 'distanceTraveled',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
          FilterOperator.BETWEEN,
        ],
        validationMessage: 'Invalid distance traveled format',
      },
    },
  ];

  const vehicleConfig = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    vehicleConfig.addField(fieldName, options);
  });

  return vehicleConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields([])
    .setSortableFields(['startTime', 'endTime', 'createdAt'])
    .build();
};
