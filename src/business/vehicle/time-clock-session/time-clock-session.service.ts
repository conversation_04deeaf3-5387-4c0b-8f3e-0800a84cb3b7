import { Injectable } from '@nestjs/common';
import { TimeClockSessionRepository } from './infrastructure/repositories/time-clock-session.repository';
import { TimeClockSessionDomain } from './domain/time-clock-session';
import { VehicleDomain } from '@app/business/vehicle/vehicles/domain/vehicle';
import { VehicleRepository } from '@app/business/vehicle/vehicles/infrastructure/repositories/vehicle.repository';
import {
  TimeClockSessionNotFoundException,
  TimeClockSessionOverlapException,
  TimeClockSessionDurationExceededException,
  TimeClockSessionOperationNotAllowedException,
  VehicleNotFoundException,
} from '@app/utils/errors/exceptions/vehicle-exceptions';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ClockInDto } from '../../mobile/timeclock/dto/clock-in.dto';
import { ClockOutDto } from '../../mobile/timeclock/dto/clock-out.dto';
import { UsersRepository } from '../../user/users/infrastructure/repositories/user.repository';
import { UserType } from '../../user/users/domain/user.types';

@Injectable()
export class TimeClockSessionService {
  private readonly MAX_SESSION_DURATION_HOURS = 24;

  constructor(
    private readonly timeClockSessionRepository: TimeClockSessionRepository,
    private readonly vehicleRepository: VehicleRepository,
    private readonly usersRepository: UsersRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async clockOut(
    driverId: string,
    clockOutDto: ClockOutDto,
  ): Promise<TimeClockSessionDomain> {
    const activeSession = await this.findCurrentSessionForDriver(driverId);

    if (!activeSession) {
      throw new TimeClockSessionOperationNotAllowedException(
        '',
        'clock-out',
        'There is no active session for this driver',
      );
    }

    const vehicle = await this.vehicleRepository.findOne({
      id: activeSession.vehicleId,
    });

    if (!vehicle) {
      throw new VehicleNotFoundException(activeSession.vehicleId);
    }

    if (
      clockOutDto.odometer < activeSession.startOdometer ||
      clockOutDto.odometer < vehicle.currentOdometer
    ) {
      throw new TimeClockSessionOperationNotAllowedException(
        driverId,
        'clock-out',
        `Odometer (${clockOutDto.odometer}) must be greater than or equal to start (${activeSession.startOdometer}) and current vehicle odometer (${vehicle.currentOdometer})`,
      );
    }
    activeSession.endTime = new Date();
    activeSession.endOdometer = clockOutDto.odometer;
    const totalKm = activeSession.endOdometer - activeSession.startOdometer;
    activeSession.distanceTraveled = totalKm;
    vehicle.isAvailable = true;

    await this.timeClockSessionRepository.update(activeSession);

    if (clockOutDto.odometer > vehicle.currentOdometer) {
      vehicle.currentOdometer = clockOutDto.odometer;
    }
    await this.vehicleRepository.update(vehicle);
    return activeSession;
  }

  async clockIn(
    driverId: string,
    clockInDto: ClockInDto,
  ): Promise<TimeClockSessionDomain> {
    const driver = await this.usersRepository.findOne({
      where: { id: driverId, userType: UserType.Driver },
    });

    if (!driver) {
      throw new TimeClockSessionOperationNotAllowedException(
        '',
        'clock-in',
        'Driver not found',
      );
    }

    const tenantId = driver.tenantId;

    if (!tenantId) {
      throw new TimeClockSessionOperationNotAllowedException(
        '',
        'clock-in',
        'Tenant not found',
      );
    }

    const activeSession = await this.findCurrentSessionForDriver(driverId);

    // checking if there is any active session for driver
    if (activeSession) {
      throw new TimeClockSessionOperationNotAllowedException(
        '',
        'clock-in',
        'There is already an active session for this driver',
      );
    }

    const vehicle = await this.vehicleRepository.findOne({
      id: clockInDto.vehicleId,
    });

    if (!vehicle) {
      throw new VehicleNotFoundException(clockInDto.vehicleId);
    }

    if (clockInDto.odometer < vehicle.currentOdometer) {
      throw new TimeClockSessionOperationNotAllowedException(
        driverId,
        'clock-in',
        `Odometer (${clockInDto.odometer}) cannot be less than current vehicle odometer (${vehicle.currentOdometer})`,
      );
    }

    const timeClockSessionDomain = this.mapper.map(
      clockInDto,
      ClockInDto,
      TimeClockSessionDomain,
    );

    timeClockSessionDomain.driverId = driverId;
    timeClockSessionDomain.tenantId = tenantId;
    timeClockSessionDomain.startOdometer = clockInDto.odometer;
    const session = await this.create(timeClockSessionDomain);
    vehicle.isAvailable = false;

    // ✅ Update vehicle odometer if higher
    if (clockInDto.odometer > vehicle.currentOdometer) {
      vehicle.currentOdometer = clockInDto.odometer;
    }
    await this.vehicleRepository.update(vehicle);
    return session;
    // return await this.create(timeClockSessionDomain);
  }

  async create(
    timeClockSessionDomain: TimeClockSessionDomain,
  ): Promise<TimeClockSessionDomain> {
    const vehicle = await this.vehicleRepository.findOne({
      id: timeClockSessionDomain.vehicleId,
    });
    if (!vehicle) {
      throw new VehicleNotFoundException(timeClockSessionDomain.vehicleId);
    }

    if (timeClockSessionDomain.endTime) {
      const duration =
        (new Date(timeClockSessionDomain.endTime).getTime() -
          new Date(timeClockSessionDomain.startTime).getTime()) /
        (1000 * 60 * 60);

      if (duration > this.MAX_SESSION_DURATION_HOURS) {
        throw new TimeClockSessionDurationExceededException(
          timeClockSessionDomain.id,
          duration,
          this.MAX_SESSION_DURATION_HOURS,
        );
      }

      if (duration <= 0) {
        throw new TimeClockSessionOperationNotAllowedException(
          '',
          'create',
          'End time must be after start time',
        );
      }
    }

    const overlappingSession =
      await this.timeClockSessionRepository.findOverlapping(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
      );

    if (overlappingSession) {
      throw new TimeClockSessionOverlapException(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
      );
    }

    const timeClockSession = await this.timeClockSessionRepository.create(
      timeClockSessionDomain,
    );
    return timeClockSession;
  }

  async getTimeClockSessionList(
    vehicleId: VehicleDomain['id'],
    filter: BaseFilterDto,
  ) {
    const vehicle = await this.vehicleRepository.findOne({ id: vehicleId });
    if (!vehicle) {
      throw new VehicleNotFoundException(vehicleId);
    }

    const timeClockSessionDomain = await this.timeClockSessionRepository.find(
      vehicleId,
      filter,
    );
    return timeClockSessionDomain;
  }

  async getTimeClockSessionDetails(
    id: TimeClockSessionDomain['id'],
  ): Promise<TimeClockSessionDomain> {
    const timeClockSessionDomain =
      await this.timeClockSessionRepository.findOne({ id });
    if (!timeClockSessionDomain) {
      throw new TimeClockSessionNotFoundException(id);
    }
    return timeClockSessionDomain;
  }

  async updateTimeClockSessionDetails(
    timeClockSessionDomain: TimeClockSessionDomain,
  ): Promise<void> {
    const timeClockSession = await this.timeClockSessionRepository.findOne({
      id: timeClockSessionDomain.id,
    });
    if (!timeClockSession) {
      throw new TimeClockSessionNotFoundException(timeClockSessionDomain.id);
    }

    const vehicle = await this.vehicleRepository.findOne({
      id: timeClockSessionDomain.vehicleId,
    });
    if (!vehicle) {
      throw new VehicleNotFoundException(timeClockSessionDomain.vehicleId);
    }

    if (timeClockSessionDomain.endTime) {
      const duration =
        (new Date(timeClockSessionDomain.endTime).getTime() -
          new Date(timeClockSessionDomain.startTime).getTime()) /
        (1000 * 60 * 60);

      if (duration > this.MAX_SESSION_DURATION_HOURS) {
        throw new TimeClockSessionDurationExceededException(
          timeClockSessionDomain.id,
          duration,
          this.MAX_SESSION_DURATION_HOURS,
        );
      }

      if (duration <= 0) {
        throw new TimeClockSessionOperationNotAllowedException(
          '',
          'update',
          'End time must be after start time',
        );
      }
    }

    const overlappingSession =
      await this.timeClockSessionRepository.findOverlapping(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
        timeClockSessionDomain.id,
      );

    if (overlappingSession) {
      throw new TimeClockSessionOverlapException(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
      );
    }

    await this.timeClockSessionRepository.update(timeClockSessionDomain);
    return;
  }

  async deleteTimeClockSession(
    id: TimeClockSessionDomain['id'],
  ): Promise<void> {
    const timeClockSession = await this.timeClockSessionRepository.findOne({
      id,
    });
    if (!timeClockSession) {
      throw new TimeClockSessionNotFoundException(id);
    }

    timeClockSession.isDeleted = true;
    timeClockSession.deletedAt = new Date();
    await this.timeClockSessionRepository.update(timeClockSession);
    return;
  }

  async findCurrentSessionForDriver(
    driverId: string,
  ): Promise<TimeClockSessionDomain | null> {
    const timeClockSession =
      await this.timeClockSessionRepository.findCurrentSessionForDriver(
        driverId,
      );
    return timeClockSession;
  }

  async getDriverDutyStatus(driverId: string): Promise<any> {
    const session =
      await this.timeClockSessionRepository.findCurrentSessionForDriver(
        driverId,
      );

    if (session) {
      const vehicle: any = session.vehicle;
      return {
        isOnDuty: true,
        currentShiftStart: session.startTime,
        selected_vehicle: {
          id: vehicle.id,
          model: vehicle.model,
          currentOdometer: vehicle.currentOdometer,
          vehicleType: vehicle.vehicleType.name,
        },
      };
    }

    const lastEndedSession =
      await this.timeClockSessionRepository.findLastEndedSessionForDriver(
        driverId,
      );

    return {
      isOnDuty: false,
      currentShiftStart: lastEndedSession?.startTime || null,
      selected_vehicle: null,
    };
  }
}
