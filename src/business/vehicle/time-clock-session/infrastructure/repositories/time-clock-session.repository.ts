import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, Repository } from 'typeorm';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { TimeClockSessionEntity } from '../entities/time-clock-session.entity';
import { TimeClockSessionDomain } from '../../domain/time-clock-session';
import { NullableType } from '@utils/types/nullable.type';
import { VehicleDomain } from '@app/business/vehicle/vehicles/domain/vehicle';
import { BaseFilterDto } from '../../../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '../../../../../core/infrastructure/filtering/services/secure-filter.service';
import { TimeClockSessionFilterConfig } from '../../time-clock-session-filter.config';

@Injectable()
export class TimeClockSessionRepository {
  constructor(
    @InjectRepository(TimeClockSessionEntity)
    private readonly timeClockSessionRepository: Repository<TimeClockSessionEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(
      TimeClockSessionFilterConfig(),
    );
  }

  async create(data: TimeClockSessionDomain): Promise<TimeClockSessionDomain> {
    const requestEntity = this.mapper.map(
      data,
      TimeClockSessionDomain,
      TimeClockSessionEntity,
    );
    const timeClockSessionEntity = await this.timeClockSessionRepository.save(
      this.timeClockSessionRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      timeClockSessionEntity,
      TimeClockSessionEntity,
      TimeClockSessionDomain,
    );
    return responseDomain;
  }

  async find(vehicleId: VehicleDomain['id'], filter: BaseFilterDto) {
    const queryBuilder = this.timeClockSessionRepository
      .createQueryBuilder('timeClockSession')
      .leftJoinAndSelect('timeClockSession.driver', 'driver')
      .where('timeClockSession.isDeleted = false')
      .andWhere('timeClockSession.vehicleId = :vehicleId', { vehicleId });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      TimeClockSessionEntity,
      TimeClockSessionDomain,
    );

    const totalDistance = await this.timeClockSessionRepository
      .createQueryBuilder('session')
      .select('SUM(session.distanceTraveled)', 'totalDistance')
      .where('session.vehicleId = :vehicleId', { vehicleId })
      .andWhere('session.isDeleted = false')
      .getRawOne();

    return {
      ...result,
      data: mappedData,
      totalDistance: totalDistance.totalDistance || 0,
    };
  }

  async findOne(
    fields: EntityCondition<TimeClockSessionDomain>,
  ): Promise<NullableType<TimeClockSessionDomain>> {
    const requestEntity: Partial<TimeClockSessionEntity> = this.mapper.map(
      fields,
      TimeClockSessionDomain,
      TimeClockSessionEntity,
    );
    const timeClockSessionEntity =
      await this.timeClockSessionRepository.findOne({
        where: {
          ...(requestEntity as FindOptionsWhere<TimeClockSessionEntity>),
          isDeleted: false,
        },
      });

    if (timeClockSessionEntity) {
      const responseDomain = this.mapper.map(
        timeClockSessionEntity,
        TimeClockSessionEntity,
        TimeClockSessionDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(
    payload: TimeClockSessionDomain,
  ): Promise<TimeClockSessionDomain> {
    const requestEntity = this.mapper.map(
      payload,
      TimeClockSessionDomain,
      TimeClockSessionEntity,
    );
    const timeClockSessionEntity =
      await this.timeClockSessionRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      timeClockSessionEntity,
      TimeClockSessionEntity,
      TimeClockSessionDomain,
    );
    return responseDomain;
  }

  async findOverlapping(
    vehicleId: string,
    startTime: Date,
    endTime: Date,
    excludeId?: string,
  ): Promise<TimeClockSessionDomain | null> {
    const query = this.timeClockSessionRepository
      .createQueryBuilder('session')
      .where('session.vehicleId = :vehicleId', { vehicleId })
      .andWhere('session.isDeleted = false')
      .andWhere(
        '(session.startTime, COALESCE(session.endTime, NOW())) OVERLAPS (:startTime, :endTime)',
        { startTime, endTime: endTime || new Date() },
      );

    if (excludeId) {
      query.andWhere('session.id != :excludeId', { excludeId });
    }

    const session = await query.getOne();
    return session
      ? this.mapper.map(session, TimeClockSessionEntity, TimeClockSessionDomain)
      : null;
  }

  async findCurrentSessionForDriver(
    driverId: string,
  ): Promise<TimeClockSessionDomain | null> {
    const query = this.timeClockSessionRepository
      .createQueryBuilder('session')
      .leftJoinAndSelect('session.vehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .where('session.driverId = :driverId', { driverId })
      .andWhere('session.isDeleted = false')
      .andWhere('session.endTime IS NULL');

    const session = await query.getOne();

    const mappedSession = this.mapper.map(session, TimeClockSessionEntity, TimeClockSessionDomain);

    return mappedSession
  }

  async findLastEndedSessionForDriver(
    driverId: string,
  ): Promise<TimeClockSessionDomain | null> {
    const query = this.timeClockSessionRepository
      .createQueryBuilder('session')
      .where('session.driverId = :driverId', { driverId })
      .andWhere('session.isDeleted = false')
      .andWhere('session.endTime IS NOT NULL')

    const session = await query.getOne();

    return session
      ? this.mapper.map(session, TimeClockSessionEntity, TimeClockSessionDomain)
      : null;
  }
}
