import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TimeClockSessionEntity } from './entities/time-clock-session.entity';
import { TimeClockSessionRepository } from './repositories/time-clock-session.repository';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { TimeClockSessionFilterConfig } from '../time-clock-session-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([TimeClockSessionEntity])],
  providers: [
    TimeClockSessionRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(TimeClockSessionFilterConfig()),
    },
  ],
  exports: [TimeClockSessionRepository],
})
export class RelationalTimeClockSessionPersistenceModule {}
