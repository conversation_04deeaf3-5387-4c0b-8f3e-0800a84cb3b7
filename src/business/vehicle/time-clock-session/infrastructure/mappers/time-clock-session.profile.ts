import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateTimeClockSessionDto } from '../../dto/create-time-clock-session.dto';
import { TimeClockSessionDomain } from '../../domain/time-clock-session';
import { TimeClockSessionEntity } from '../entities/time-clock-session.entity';
import { GetTimeClockSessionDto } from '../../dto/get-time-clock-session.dto';
import {
  IncompleteSessionThresholdHours,
  TimeClockSessionStatus,
  TimeClockSessionSource,
} from '../../domain/time-clock-session.types';
import { ClockInDto } from '../../../../mobile/timeclock/dto/clock-in.dto';

@Injectable()
export class TimeClockSessionProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, CreateTimeClockSessionDto, TimeClockSessionDomain);
      createMap(
        mapper,
        ClockInDto,
        TimeClockSessionDomain,
        forMember(
          (dest) => dest.startOdometer,
          mapFrom((src) => src.odometer),
        ),
        forMember(
          (dest) => dest.vehicleId,
          mapFrom((src) => src.vehicleId),
        ),
        forMember(
          (dest) => dest.startTime,
          mapFrom(() => new Date()),
        ),
        forMember(
          (dest) => dest.source,
          mapFrom(() => TimeClockSessionSource.Automatic),
        ),
      );
      createMap(mapper, TimeClockSessionDomain, TimeClockSessionEntity);
      createMap(mapper, TimeClockSessionEntity, TimeClockSessionDomain);

      createMap(
        mapper,
        TimeClockSessionDomain,
        GetTimeClockSessionDto,
        forMember(
          (dest) => dest.totalHours,
          mapFrom((src) => {
            if (src.startTime && src.endTime) {
              const start = new Date(src.startTime).getTime();
              const end = new Date(src.endTime).getTime();
              const differenceInMs = end - start;

              const totalHours = differenceInMs / (1000 * 60 * 60);

              return Math.round(totalHours * 100) / 100;
            }
            return null;
          }),
        ),
        forMember(
          (dest) => dest.status,
          mapFrom((src) => {
            if (src.startTime && !src.endTime) {
              const timeDifference =
                Date.now() - new Date(src.startTime).getTime();
              const threshold =
                IncompleteSessionThresholdHours * 60 * 60 * 1000;
              if (timeDifference <= threshold) {
                return TimeClockSessionStatus.Active;
              } else {
                return TimeClockSessionStatus.Incomplete;
              }
            } else {
              return null;
            }
          }),
        ),
        forMember(
          (dest) => dest.driver,
          mapFrom((src) => src.driver?.contactName),
        ),
      );
    };
  }
}
