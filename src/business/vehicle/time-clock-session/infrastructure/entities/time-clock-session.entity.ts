import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { AutoMap } from '@automapper/classes';
import { TimeClockSessionSource } from '../../domain/time-clock-session.types';
import { UserEntity } from '../../../../user/users/infrastructure/entities/user.entity';

@Entity('time_clock_session')
export class TimeClockSessionEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'vehicleId' })
  @Index()
  vehicle: VehicleEntity;

  @AutoMap()
  @Column('uuid')
  vehicleId: string;

  @AutoMap()
  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'driverId' })
  @Index()
  driver: UserEntity;

  @AutoMap()
  @Column('uuid')
  driverId: string;

  @AutoMap()
  @Column({ type: 'timestamptz' })
  startTime: Date;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true })
  endTime: Date;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  distanceTraveled: number;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: TimeClockSessionSource,
    default: TimeClockSessionSource.Manual,
  })
  source: TimeClockSessionSource;

  @AutoMap()
  @Column({ nullable: true })
  addedBy: string;

  @AutoMap()
  @Column({ nullable: true })
  startOdometer: number;

  @AutoMap()
  @Column({ nullable: true })
  endOdometer: number;

  @AutoMap()
  @Column('text', { nullable: true })
  notes: string;

  @AutoMap()
  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @AutoMap()
  @Column({ default: false })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt: Date;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  @AutoMap()
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;
}
