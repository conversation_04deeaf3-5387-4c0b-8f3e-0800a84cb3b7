import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const VehicleFilterConfig = () => {
  const fields = [
    // Fleet ID
    {
      fieldName: 'fleetId',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        type: 'string' as const,
        searchable: true,
        validationMessage: 'Invalid fleet ID format',
      },
    },

    // Vehicle Type (relation)
    {
      fieldName: 'vehicleType.name',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        relation: 'vehicleType',
        relationField: 'name',
        searchable: true,
        validationMessage: 'Invalid vehicle type format',
      },
    },

    // Make
    {
      fieldName: 'make',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        type: 'string' as const,
        searchable: true,
        validationMessage: 'Invalid make format',
      },
    },

    // Model
    {
      fieldName: 'model',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        type: 'string' as const,
        searchable: true,
        validationMessage: 'Invalid model format',
      },
    },

    // License Plate
    {
      fieldName: 'licensePlate',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        type: 'string' as const,
        searchable: true,
        validationMessage: 'Invalid license plate format',
      },
    },

    // Capacity (maxWeight)
    {
      fieldName: 'maxWeight',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
          FilterOperator.BETWEEN,
        ],
        type: 'number' as const,
        searchable: true,
        validationMessage: 'Invalid capacity format',
      },
    },

    // Odometer Reading
    {
      fieldName: 'currentOdometer',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
          FilterOperator.BETWEEN,
        ],
        type: 'number' as const,
        searchable: true,
        validationMessage: 'Invalid odometer reading format',
      },
    },
  ];

  const vehicleConfig = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    vehicleConfig.addField(fieldName, options);
  });

  return vehicleConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields([
      'fleetId',
      'make',
      'model',
      'licensePlate',
      'maxWeight',
      'currentOdometer',
      'vehicleType.name',
    ])
    .setSortableFields([
      'fleetId',
      'make',
      'model',
      'licensePlate',
      'maxWeight',
      'currentOdometer',
      'vehicleType.name',
      'createdAt',
    ])
    .setRelations({
      vehicleType: 'vehicle.vehicleType',
    })
    .build();
};
