import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { RelationalVehiclePersistenceModule } from './infrastructure/relational-persistence.module';
import { VehiclesController } from './vehicles.controller';
import { VehiclesService } from './vehicles.service';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { RelationalVehicleTypePersistenceModule } from '../vehicle-types/infrastructure/relational-persistence.module';
import { VehicleProfile } from './infrastructure/mappers/vehicle.profile';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { VehicleFilterConfig } from '@app/business/vehicle/vehicles/vehicle-filter.config';
import { RbacModule } from '../../../core/rbac/rbac.module';

@Module({
  imports: [
    RelationalVehiclePersistenceModule,
    RelationalVehicleTypePersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    RbacModule,
  ],
  controllers: [VehiclesController],
  providers: [
    VehiclesService,
    VehicleProfile,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(VehicleFilterConfig()),
    },
  ],
  exports: [VehiclesService],
})
export class VehiclesModule {}
