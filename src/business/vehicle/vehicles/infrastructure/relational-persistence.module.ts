import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleEntity } from './entities/vehicle.entity';
import { VehicleRepository } from './repositories/vehicle.repository';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { VehicleFilterConfig } from '../vehicle-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([VehicleEntity])],
  providers: [
    VehicleRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(VehicleFilterConfig()),
    },
  ],
  exports: [VehicleRepository],
})
export class RelationalVehiclePersistenceModule {}
