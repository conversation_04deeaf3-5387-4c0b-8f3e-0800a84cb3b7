import { AutoMap } from '@automapper/classes';
import { VehicleTypeDomain } from '../../vehicle-types/domain/vehicle-type';

export class VehicleDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  vehicleTypeId: string;

  @AutoMap()
  vehicleType: VehicleTypeDomain;

  @AutoMap()
  fleetId: string;

  @AutoMap()
  make: string;

  @AutoMap()
  model: string;

  @AutoMap()
  year: number;

  @AutoMap()
  licensePlate: string;

  @AutoMap()
  vin?: string;

  @AutoMap()
  status: string;

  @AutoMap()
  ownedBy?: string;

  @AutoMap()
  packageType: Array<string>;

  @AutoMap()
  maxWeight?: number;

  @AutoMap()
  maxVolume?: number;

  @AutoMap()
  currentOdometer: number;

  @AutoMap()
  lastMaintenanceDate?: Date;

  @AutoMap()
  nextMaintenanceDue?: Date;

  @AutoMap()
  maintenanceIntervalKm: number;

  @AutoMap()
  branch: string;

  @AutoMap()
  imageUrl?: string;

  @AutoMap()
  notes?: string;

  @AutoMap()
  isAvailable: boolean;

  @AutoMap()
  metaData?: Record<string, any>;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt?: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy?: string;

  @AutoMap()
  updatedBy?: string;
}
