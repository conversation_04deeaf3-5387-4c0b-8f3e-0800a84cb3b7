import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';
import { CreateVehicleDto } from './create-vehicle.dto';

export class GetVehicleDto extends CreateVehicleDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: 'Sedan' })
  vehicleType: string;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  createdBy: string;

  @AutoMap()
  @ApiProperty({ example: 'User2' })
  updatedBy: string;
}

export class GetAllVehicleDto extends PageResponse {
  @ApiProperty({ type: [GetVehicleDto] })
  data: GetVehicleDto[];
}
