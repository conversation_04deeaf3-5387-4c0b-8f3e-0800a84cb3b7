import { Injectable } from '@nestjs/common';
import { VehicleTypeRepository } from './infrastructure/repositories/vehicle-type.repository';
import { VehicleTypeDomain } from './domain/vehicle-type';
import { PageParamsRequest } from '@utils/page-params-request';
import {
  VehicleTypeNotFoundException,
  VehicleTypeAlreadyExistsException,
  VehicleTypeInUseException,
  VehicleTypeOperationNotAllowedException,
} from '@app/utils/errors/exceptions/vehicle-exceptions';
import { VehicleRepository } from '@app/business/vehicle/vehicles/infrastructure/repositories/vehicle.repository';

@Injectable()
export class VehicleTypesService {
  constructor(
    private readonly vehicleTypeRepository: VehicleTypeRepository,
    private readonly vehicleRepository: VehicleRepository,
  ) {}

  async create(
    vehicleTypeDomain: VehicleTypeDomain,
  ): Promise<VehicleTypeDomain> {
    const existingType = await this.vehicleTypeRepository.findOne({
      name: vehicleTypeDomain.name,
    });
    if (existingType) {
      throw new VehicleTypeAlreadyExistsException(vehicleTypeDomain.name);
    }

    const vehicleType =
      await this.vehicleTypeRepository.create(vehicleTypeDomain);
    return vehicleType;
  }

  async getVehicleTypeList(
    queryParams: PageParamsRequest,
  ): Promise<{ totalCount: number; data: VehicleTypeDomain[] }> {
    const vehicleTypeDomain =
      await this.vehicleTypeRepository.find(queryParams);
    return vehicleTypeDomain;
  }

  async getVehicleTypeDetails(
    vehicleTypeId: VehicleTypeDomain['id'],
  ): Promise<VehicleTypeDomain> {
    const vehicleTypeDomain = await this.vehicleTypeRepository.findOne({
      id: vehicleTypeId,
    });
    if (!vehicleTypeDomain) {
      throw new VehicleTypeNotFoundException(vehicleTypeId);
    }

    return vehicleTypeDomain;
  }

  async updateVehicleTypeDetails(
    vehicleTypeDomain: VehicleTypeDomain,
  ): Promise<void> {
    const vehicleType = await this.vehicleTypeRepository.findOne({
      id: vehicleTypeDomain.id,
    });
    if (!vehicleType) {
      throw new VehicleTypeNotFoundException(vehicleTypeDomain.id);
    }

    // If name is being changed, check for uniqueness
    if (vehicleType.name !== vehicleTypeDomain.name) {
      const existingType = await this.vehicleTypeRepository.findOne({
        name: vehicleTypeDomain.name,
      });
      if (existingType && existingType.id !== vehicleTypeDomain.id) {
        throw new VehicleTypeAlreadyExistsException(vehicleTypeDomain.name);
      }
    }

    // Check if there are vehicles using this type and if the changes are compatible
    const vehiclesUsingType = await this.vehicleRepository.countByType(
      vehicleTypeDomain.id,
    );
    if (vehiclesUsingType > 0) {
      if (
        vehicleTypeDomain.maxWeight < vehicleType.maxWeight ||
        vehicleTypeDomain.maxVolume < vehicleType.maxVolume
      ) {
        throw new VehicleTypeOperationNotAllowedException(
          '',
          'update',
          'Cannot reduce capacity while vehicles are using this type',
        );
      }
    }

    await this.vehicleTypeRepository.update(vehicleTypeDomain);
    return;
  }

  async deleteVehicleType(
    vehicleTypeId: VehicleTypeDomain['id'],
  ): Promise<void> {
    const vehicleType = await this.vehicleTypeRepository.findOne({
      id: vehicleTypeId,
    });
    if (!vehicleType) {
      throw new VehicleTypeNotFoundException(vehicleTypeId);
    }

    const vehiclesUsingType =
      await this.vehicleRepository.countByType(vehicleTypeId);
    if (vehiclesUsingType > 0) {
      throw new VehicleTypeInUseException(vehicleTypeId, vehiclesUsingType);
    }

    vehicleType.isDeleted = true;
    vehicleType.deletedAt = new Date();
    await this.vehicleTypeRepository.update(vehicleType);
    return;
  }
}
