import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { RelationalVehicleTypePersistenceModule } from './infrastructure/relational-persistence.module';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { VehicleTypesController } from './vehicle-types.controller';
import { VehicleTypesService } from './vehicle-types.service';
import { VehicleTypeProfile } from './infrastructure/mappers/vehicle-type.profile';
import { RelationalVehiclePersistenceModule } from '@app/business/vehicle/vehicles/infrastructure/relational-persistence.module';

@Module({
  imports: [
    RelationalVehicleTypePersistenceModule,
    MonitoringModule,
    RelationalVehiclePersistenceModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [VehicleTypesController],
  providers: [VehicleTypesService, VehicleTypeProfile],
  exports: [VehicleTypesService],
})
export class VehicleTypesModule {}
