import { AutoMap } from '@automapper/classes';

export class VehicleTypeDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  name: string;

  @AutoMap()
  description: string;

  @AutoMap()
  maxWeight: number;

  @AutoMap()
  maxVolume: number;

  @AutoMap()
  capabilities: Record<string, any>[];

  @AutoMap()
  metaData: Record<string, any>;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}
