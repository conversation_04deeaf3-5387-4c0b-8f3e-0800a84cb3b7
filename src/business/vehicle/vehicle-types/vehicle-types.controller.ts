import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { VehicleTypesService } from './vehicle-types.service';
import { VehicleTypeDomain } from './domain/vehicle-type';
import { CreateVehicleTypeDto } from './dto/create-vehicle-type.dto';
import {
  GetAllVehicleTypeDto,
  GetVehicleTypeDto,
} from './dto/get-vehicle-type.dto';
import { PageParamsRequest } from '@utils/page-params-request';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { VehicleTypeOperationNotAllowedException } from '../../../utils/errors/exceptions/vehicle-exceptions';

@ApiTags('Business - Vehicle - Types')
@Controller({
  path: 'vehicleTypes',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class VehicleTypesController {
  constructor(
    private readonly vehicleTypesService: VehicleTypesService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Vehicle Type' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createVehicleTypeDto: CreateVehicleTypeDto,
  ): Promise<VehicleTypeDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new VehicleTypeOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      const vehicleTypeDomain = this.mapper.map(
        createVehicleTypeDto,
        CreateVehicleTypeDto,
        VehicleTypeDomain,
      );
      vehicleTypeDomain.tenantId = tenantId;
      const vehicleType =
        await this.vehicleTypesService.create(vehicleTypeDomain);
      return vehicleType;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all vehicle types with pagination' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllVehicleTypeDto })
  async getVehicleTypeList(
    @Query() queryParams: PageParamsRequest,
  ): Promise<GetAllVehicleTypeDto> {
    try {
      const responseDomains =
        await this.vehicleTypesService.getVehicleTypeList(queryParams);
      const mappedData = this.mapper.mapArray(
        responseDomains.data,
        VehicleTypeDomain,
        GetVehicleTypeDto,
      );
      const total = responseDomains.totalCount;
      const page = queryParams.pageNumber || 1;
      const limit = queryParams.pageSize || 10;
      const totalPages = Math.ceil(total / limit);

      const response: GetAllVehicleTypeDto = {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get(':vehicleTypeId')
  @ApiOperation({ summary: 'Find vehicle type by vehicle type Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetVehicleTypeDto })
  async getVehicleTypeDetails(
    @Param('vehicleTypeId') vehicleTypeId: string,
  ): Promise<GetVehicleTypeDto> {
    try {
      const responseDomain =
        await this.vehicleTypesService.getVehicleTypeDetails(vehicleTypeId);
      const response = this.mapper.map(
        responseDomain,
        VehicleTypeDomain,
        GetVehicleTypeDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':vehicleTypeId')
  @ApiOperation({ summary: 'Update vehicle type by vehicle type Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateVehicleTypeDetails(
    @Param('vehicleTypeId') vehicleTypeId: string,
    @Body() updateVehicleTypeDto: CreateVehicleTypeDto,
  ): Promise<void> {
    try {
      const vehicleType = this.mapper.map(
        updateVehicleTypeDto,
        CreateVehicleTypeDto,
        VehicleTypeDomain,
      );
      vehicleType.id = vehicleTypeId;
      await this.vehicleTypesService.updateVehicleTypeDetails(vehicleType);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':vehicleTypeId')
  @ApiOperation({ summary: 'Soft-delete vehicle type by vehicle type Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteVehicleType(
    @Param('vehicleTypeId') vehicleTypeId: string,
  ): Promise<void> {
    try {
      await this.vehicleTypesService.deleteVehicleType(vehicleTypeId);
      return;
    } catch (error) {
      throw error;
    }
  }
}
