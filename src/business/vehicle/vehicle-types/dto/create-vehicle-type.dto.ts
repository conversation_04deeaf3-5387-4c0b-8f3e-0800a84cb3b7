import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class CreateVehicleTypeDto {
  @AutoMap()
  @ApiProperty({ example: 'SUVs' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ example: 'Nice elegant and comfortable SUVs' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  description: string;
}
