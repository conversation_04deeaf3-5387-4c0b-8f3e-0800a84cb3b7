import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, Repository } from 'typeorm';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { VehicleTypeEntity } from '../entities/vehicle-type.entity';
import { VehicleTypeDomain } from '../../domain/vehicle-type';
import { NullableType } from '@utils/types/nullable.type';
import { PageParamsRequest } from '@utils/page-params-request';

@Injectable()
export class VehicleTypeRepository {
  constructor(
    @InjectRepository(VehicleTypeEntity)
    private readonly vehicleTypeRepository: Repository<VehicleTypeEntity>,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(data: VehicleTypeDomain): Promise<VehicleTypeDomain> {
    const requestEntity = this.mapper.map(
      data,
      VehicleTypeDomain,
      VehicleTypeEntity,
    );
    const vehicleTypeEntity = await this.vehicleTypeRepository.save(
      this.vehicleTypeRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      vehicleTypeEntity,
      VehicleTypeEntity,
      VehicleTypeDomain,
    );
    return responseDomain;
  }

  async find(
    queryParams: PageParamsRequest,
  ): Promise<{ totalCount: number; data: VehicleTypeDomain[] }> {
    const [vehicleTypeEntities, totalCount] =
      await this.vehicleTypeRepository.findAndCount({
        where: { isDeleted: false },
        skip: (queryParams.pageNumber - 1) * queryParams.pageSize,
        take: queryParams.pageSize,
      });
    const data = this.mapper.mapArray(
      vehicleTypeEntities,
      VehicleTypeEntity,
      VehicleTypeDomain,
    );
    return { totalCount, data };
  }

  async findOne(
    fields: EntityCondition<VehicleTypeDomain>,
  ): Promise<NullableType<VehicleTypeDomain>> {
    const requestEntity: Partial<VehicleTypeEntity> = this.mapper.map(
      fields,
      VehicleTypeDomain,
      VehicleTypeEntity,
    );
    const vehicleTypeEntity = await this.vehicleTypeRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<VehicleTypeEntity>),
        isDeleted: false,
      },
    });
    if (vehicleTypeEntity) {
      const responseDomain = this.mapper.map(
        vehicleTypeEntity,
        VehicleTypeEntity,
        VehicleTypeDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: VehicleTypeDomain): Promise<VehicleTypeDomain> {
    const requestEntity = this.mapper.map(
      payload,
      VehicleTypeDomain,
      VehicleTypeEntity,
    );
    const vehicleTypeEntity =
      await this.vehicleTypeRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      vehicleTypeEntity,
      VehicleTypeEntity,
      VehicleTypeDomain,
    );
    return responseDomain;
  }
}
