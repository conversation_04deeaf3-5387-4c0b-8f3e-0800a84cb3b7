import { createMap, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateVehicleTypeDto } from '../../dto/create-vehicle-type.dto';
import { VehicleTypeDomain } from '../../domain/vehicle-type';
import { VehicleTypeEntity } from '../entities/vehicle-type.entity';
import { GetVehicleTypeDto } from '../../dto/get-vehicle-type.dto';

@Injectable()
export class VehicleTypeProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, CreateVehicleTypeDto, VehicleTypeDomain);
      createMap(mapper, VehicleTypeDomain, VehicleTypeEntity);
      createMap(mapper, VehicleTypeEntity, VehicleTypeDomain);
      createMap(mapper, VehicleTypeDomain, GetVehicleTypeDto);
    };
  }
}
