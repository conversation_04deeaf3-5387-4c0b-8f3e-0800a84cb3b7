import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleTypeRepository } from './repositories/vehicle-type.repository';
import { VehicleTypeEntity } from './entities/vehicle-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([VehicleTypeEntity])],
  providers: [VehicleTypeRepository],
  exports: [VehicleTypeRepository],
})
export class RelationalVehicleTypePersistenceModule {}
