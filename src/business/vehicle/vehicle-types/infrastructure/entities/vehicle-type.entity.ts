import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';

@Entity('vehicle_types')
export class VehicleTypeEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @Column({ length: 100 })
  name: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  description: string;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxWeight: boolean;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxVolume: string;

  @AutoMap()
  @Column({ type: 'jsonb', default: [] })
  capabilities: Record<string, any>[];

  @AutoMap()
  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @AutoMap()
  @Column({ default: false })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt: Date;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;
}
