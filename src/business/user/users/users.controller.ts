import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  Put,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiCreatedResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { User } from './domain/user';
import { FindAllUsersResponseDto } from './dto/find-all-users.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { AccountNumberService } from '@app/business/user/users/services/account-number.service';
import { CreateCustomerDto } from '@app/business/user/users/dto/create-customer.dto';
import { UpdateCustomerDto } from '@app/business/user/users/dto/update-customer.dto';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { TenantNotFoundException } from '@app/utils/errors/exceptions/tenant.exceptions';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { CustomerResponseDto } from '@app/business/user/users/dto/customer-response.dto';
import { CustomerDto } from '@app/business/user/users/dto/customer-dto.model';
import { AssignPriceSetsDto } from './dto/assign-price-set.dto';
import { GetCustomerPriceSetsResponseDto } from './dto/get-customer-price-sets.dto';
import { TenantValidationService } from '@app/business/user/tenants/tenant-validation.service';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { Request } from 'express';
import { GetAllCustomerMinimalDto } from './dto/get-customers-minimal.dto';

@ApiTags('Business - User - Customers')
@ApiBearerAuth()
@Controller({
  path: '/customers',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly accountNumberService: AccountNumberService,
    private readonly tenantValidationService: TenantValidationService,
    private readonly secureFilterService: SecureFilterService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all customers for the current Company Administrator',
  })
  @ApiOkResponse({
    description: 'List of customers with pagination info',
    type: FindAllUsersResponseDto,
    schema: {
      example: {
        data: [
          {
            id: '550e8400-e29b-41d4-a716-************',
            tenantId: '550e8400-e29b-41d4-a716-************',
            companyName: 'Acme Inc.',
            contactName: 'John Doe',
            email: '<EMAIL>',
            phoneNumber: '**********',
            status: 'Active',
            userType: 'Customer',
            createdAt: '2023-01-15T08:30:00.000Z',
          },
        ],
        total: 25,
        page: 1,
        limit: 10,
        totalPages: 3,
        hasNextPage: true,
        hasPreviousPage: false,
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  async getCustomers(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<PaginatedResult<CustomerDto>> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );

    const response = await this.usersService.findTenantCustomers(
      tenant.id,
      combinedFilter,
    );
    return response;
  }

  @Get('all/minimal')
  @ApiOperation({
    summary:
      'Get all customers (id, customer name and company name only, no pagination)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllCustomerMinimalDto })
  async getAllCustomersMinimal(@Req() request: Request) {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);
      const data = await this.usersService.getAllCustomers(tenantId);
      return { data };
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get customer details by ID' })
  @ApiOkResponse({
    description: 'Customer details retrieved successfully',
    type: User,
    schema: {
      example: {
        id: '550e8400-e29b-41d4-a716-************',
        tenantId: '550e8400-e29b-41d4-a716-************',
        companyName: 'Acme Inc.',
        contactName: 'John Doe',
        email: '<EMAIL>',
        phoneNumber: '**********',
        phoneExtension: '123',
        faxNumber: '**********',
        website: 'https://acme.com',
        addressLine1: '123 Main St',
        addressLine2: 'Suite 100',
        city: 'New York',
        province: 'NY',
        postalCode: '10001',
        country: 'USA',
        status: 'Active',
        userType: 'Customer',
        createdAt: '2023-01-15T08:30:00.000Z',
        updatedAt: '2023-05-20T14:45:00.000Z',
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Customer not found' })
  @ApiForbiddenResponse({
    description:
      'Insufficient permissions or customer belongs to different tenant',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-************',
  })
  async getCustomer(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<User> {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    if (!request.tenantContext.tenantId) {
      throw new TenantNotFoundException(
        'No tenant specified in request context',
      );
    }

    const { tenantId } = request.tenantContext;
    return this.usersService.getTenantCustomer(tenantId, id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new customer with categories' })
  @ApiCreatedResponse({
    description: 'Customer created successfully',
    type: User,
    schema: {
      example: {
        id: '44e216e2-0916-4eef-bb2d-0a6e7fafdba0',
        companyName: 'Tech Solutions LLC',
        contactName: 'Jane Smith',
        email: '<EMAIL>',
        accountNumber: 'Tech Solutions LLC',
        phoneCountryCode: '+1',
        phoneNumber: '**********',
        phoneExtension: '123',
        faxCountryCode: '+1',
        faxNumber: '**********',
        website: 'https://techsolutions.com',
        addressLine1: '456 Tech Blvd',
        addressLine2: 'Floor 10',
        city: 'San Francisco',
        province: 'CA',
        postalCode: '94107',
        country: 'USA',
        status: 'Active',
        createdAt: '2025-02-26T07:25:56.610Z',
        updatedAt: '2025-02-26T07:25:56.610Z',
        categories: [
          {
            id: 'a013b8d9-1868-45b0-8088-7ab84d148e53',
            name: 'Technology',
          },
          {
            id: '3245c1b4-b807-4275-a141-c4ed9670db1f',
            name: 'Premium',
          },
        ],
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  @ApiUnauthorizedResponse({ description: 'User not authenticated' })
  @ApiBody({
    type: CreateCustomerDto,
    description: 'Customer data with categories',
    examples: {
      'Basic Customer': {
        value: {
          companyName: 'Acme Inc.',
          contactName: 'John Doe',
          email: '<EMAIL>',
          accountNumber: 'ACC123456',
          phoneCountryCode: '+1',
          phoneNumber: '**********',
          addressLine1: '123 Main St',
          city: 'New York',
          province: 'NY',
          status: 'Active',
          postalCode: '10001',
          country: 'USA',
          categories: [],
        },
      },
      'Customer with Categories': {
        value: {
          companyName: 'Tech Solutions LLC',
          contactName: 'Jane Smith',
          email: '<EMAIL>',
          accountNumber: 'ACC123456',
          phoneCountryCode: '+1',
          phoneNumber: '**********',
          phoneExtension: '123',
          faxCountryCode: '+1',
          faxNumber: '**********',
          website: 'https://techsolutions.com',
          addressLine1: '456 Tech Blvd',
          addressLine2: 'Floor 10',
          city: 'San Francisco',
          province: 'CA',
          postalCode: '94107',
          status: 'Active',
          country: 'USA',
          categories: [
            {
              id: '550e8400-e29b-41d4-a716-************',
              name: 'Premium',
            },
            {
              name: 'Technology',
            },
          ],
        },
      },
    },
  })
  async createCustomer(
    @Req() request: RequestWithUser,
    @Body() createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    return this.usersService.createCustomerWithCategories(
      tenant.id,
      createCustomerDto,
    );
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a customer with categories' })
  @ApiOkResponse({
    description: 'Customer updated successfully',
    type: User,
    schema: {
      example: {
        id: '550e8400-e29b-41d4-a716-************',
        tenantId: '550e8400-e29b-41d4-a716-************',
        companyName: 'Acme Corporation',
        contactName: 'John Doe',
        email: '<EMAIL>',
        status: 'Active',
        userType: 'Customer',
        updatedAt: '2023-07-15T11:45:00.000Z',
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Customer not found' })
  @ApiForbiddenResponse({
    description:
      'Insufficient permissions or customer belongs to different tenant',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiBody({
    type: UpdateCustomerDto,
    description: 'Updated customer data with categories',
    examples: {
      'Basic Update': {
        value: {
          companyName: 'Acme Corporation',
          contactName: 'John Doe',
          email: '<EMAIL>',
          status: 'Active',
        },
      },
      'Full Update': {
        value: {
          companyName: 'Acme Global Corp',
          contactName: 'John A. Doe',
          email: '<EMAIL>',
          phoneCountryCode: '+1',
          phoneNumber: '5552223333',
          phoneExtension: '456',
          faxCountryCode: '+1',
          faxNumber: '5554445555',
          website: 'https://acmeglobal.com',
          addressLine1: '789 Corporate Ave',
          addressLine2: 'Suite 500',
          city: 'Chicago',
          province: 'IL',
          postalCode: '60601',
          country: 'USA',
          status: 'Active',
          categories: [
            {
              id: '550e8400-e29b-41d4-a716-************',
              name: 'Premium',
            },
            {
              id: '550e8400-e29b-41d4-a716-446655443333',
              name: 'Enterprise',
            },
          ],
        },
      },
    },
  })
  async updateCustomer(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
  ): Promise<User> {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    if (!request.tenantContext.tenantId) {
      throw new TenantNotFoundException(
        'No tenant specified in request context',
      );
    }

    const { tenantId } = request.tenantContext;
    return this.usersService.updateCustomerWithCategories(
      tenantId,
      id,
      updateCustomerDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete a customer' })
  @ApiNoContentResponse({
    description: 'Customer has been successfully soft-deleted',
  })
  @ApiNotFoundResponse({ description: 'Customer not found' })
  @ApiForbiddenResponse({
    description:
      'Insufficient permissions or customer belongs to different tenant',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async softDeleteCustomer(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    if (!request.tenantContext.tenantId) {
      throw new TenantNotFoundException(
        'No tenant specified in request context',
      );
    }

    const { tenantId } = request.tenantContext;
    await this.usersService.softDelete(tenantId, id);
  }

  @Post(':id/restore')
  @ApiOperation({ summary: 'Restore a soft-deleted customer' })
  @ApiNoContentResponse({
    description: 'Customer has been successfully restored',
  })
  @ApiNotFoundResponse({ description: 'Customer not found' })
  @ApiForbiddenResponse({
    description:
      'Insufficient permissions or customer belongs to different tenant',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async restoreCustomer(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    if (!request.tenantContext.tenantId) {
      throw new TenantNotFoundException(
        'No tenant specified in request context',
      );
    }

    const { tenantId } = request.tenantContext;
    await this.usersService.restore(tenantId, id);
  }

  @Delete(':id/permanent')
  @ApiOperation({
    summary: 'Permanently delete a customer',
    description:
      'WARNING: This operation cannot be undone. The customer will be permanently removed from the system.',
  })
  @ApiNoContentResponse({
    description: 'Customer has been permanently deleted',
  })
  @ApiNotFoundResponse({ description: 'Customer not found' })
  @ApiForbiddenResponse({
    description:
      'Insufficient permissions or customer belongs to different tenant',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async permanentlyDeleteCustomer(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    if (!request.tenantContext.tenantId) {
      throw new TenantNotFoundException(
        'No tenant specified in request context',
      );
    }

    const { tenantId } = request.tenantContext;
    await this.usersService.hardDelete(tenantId, id);
  }

  @Get('account-number/generate')
  @ApiOperation({
    summary: 'Generate a unique account number for a new customer',
  })
  @ApiOkResponse({
    description: 'Unique account number generated successfully',
    schema: {
      type: 'object',
      properties: {
        accountNumber: {
          type: 'string',
          example: 'ACC123456',
          description: 'Generated unique account number',
        },
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  async generateAccountNumber(
    @Req() request: RequestWithUser,
  ): Promise<{ accountNumber: string }> {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    const accountNumber =
      await this.accountNumberService.generateUniqueAccountNumber();
    return { accountNumber };
  }

  @Put(':customerId/priceSets')
  @ApiOperation({ summary: 'Assign Price Set to Customers' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async assignPriceSets(
    @Param('customerId') customerId: string,
    @Body() assignPriceSetsDto: AssignPriceSetsDto,
  ): Promise<void> {
    try {
      await this.usersService.assignPriceSets(
        customerId,
        assignPriceSetsDto.priceSetIds,
      );
      return;
    } catch (error) {
      throw error;
    }
  }

  @Get(':customerId/priceSets')
  @ApiOperation({ summary: 'Get all Price Sets linked to Customer' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetCustomerPriceSetsResponseDto })
  async getCustomerPriceSets(
    @Param('customerId') customerId: string,
  ): Promise<GetCustomerPriceSetsResponseDto> {
    try {
      const data = await this.usersService.getCustomerPriceSets(customerId);
      return { data };
    } catch (error) {
      throw error;
    }
  }
}
