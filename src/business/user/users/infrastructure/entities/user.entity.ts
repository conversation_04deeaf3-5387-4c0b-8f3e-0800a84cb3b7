import {
  Column,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import {
  UserOrigin,
  UserStatus,
  UserType,
} from '@app/business/user/users/domain/user.types';
import { CustomerCategoryEntity } from '@app/business/user/customer-categories/infrastructure/persistence/relational/entities/customer-category.entity';
import { AutoMap } from '@automapper/classes';
import { DriverStatus } from '../../../../mobile/auth/domain/driver';

@Entity('users')
export class UserEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  // Basic Information
  @AutoMap()
  @Column({ length: 100, name: 'company_name', nullable: true })
  companyName: string;

  @AutoMap()
  @Column({ length: 100, name: 'contact_name' })
  contactName: string;

  @AutoMap()
  @Column({ name: 'account_number', length: 50, nullable: true })
  accountNumber?: string;

  @AutoMap()
  @Column({ name: 'phone_country_code', nullable: true })
  phoneCountryCode?: string;

  @AutoMap()
  @Column({ name: 'phone_number', nullable: true })
  phoneNumber?: string;

  @AutoMap()
  @Column({ name: 'phone_extension', nullable: true })
  phoneExtension?: string;

  @AutoMap()
  @Column({ name: 'fax_country_code', nullable: true })
  faxCountryCode?: string;

  @AutoMap()
  @Column({ name: 'fax_number', length: 50, nullable: true })
  faxNumber?: string;

  @AutoMap()
  @Column({ nullable: true })
  website?: string;

  @AutoMap()
  @Column({ nullable: true })
  timeZone?: string;

  @AutoMap()
  @Column()
  email: string;

  // Authentication
  @AutoMap()
  @Column({ length: 255 })
  password: string;

  @AutoMap()
  @Column({ name: 'email_verified', default: false })
  emailVerified: boolean;

  @AutoMap()
  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.Active })
  status: UserStatus;

  @AutoMap()
  @Column({ type: 'enum', enum: DriverStatus, nullable: true })
  driverStatus: DriverStatus;

  @AutoMap()
  @Column({
    name: 'last_login_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  lastLoginAt?: Date;

  @AutoMap()
  @Column({ name: 'login_count', default: 0 })
  loginCount: number;

  @AutoMap()
  @Column({ name: 'failed_attempts', default: 0 })
  failedAttempts: number;

  @AutoMap()
  @Column({
    name: 'locked_until',
    type: 'timestamp with time zone',
    nullable: true,
  })
  lockedUntil?: Date;

  // Profile
  @AutoMap()
  @Column({ type: 'enum', enum: UserOrigin, default: UserOrigin.Local })
  origin: UserOrigin;

  @AutoMap()
  @Column({ name: 'user_type', type: 'enum', enum: UserType })
  userType: UserType;

  // Preferences & Metadata
  @AutoMap()
  @Column({ name: 'notification_settings', type: 'jsonb', default: {} })
  notificationSettings: {
    channels: {
      sms: boolean;
      email: boolean;
      push: boolean;
      in_app: boolean;
    };
    order: {
      [key: string]: {
        enabled: boolean;
        recipients?: string[];
      };
    };
    payment: {
      [key: string]: {
        enabled: boolean;
      };
    };
    security: {
      [key: string]: {
        enabled: boolean;
      };
    };
  };

  @ManyToMany(() => CustomerCategoryEntity, (category) => category.users)
  @JoinTable({
    name: 'user_categories',
    joinColumn: {
      name: 'user_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'category_id',
      referencedColumnName: 'id',
    },
  })
  categories: CustomerCategoryEntity[];

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true })
  preferences?: Record<string, any>;

  @AutoMap()
  @Column({ nullable: true })
  addressLine1: string;

  @AutoMap()
  @Column({ nullable: true })
  addressLine2: string;

  @AutoMap()
  @Column({ nullable: true })
  city: string;

  @AutoMap()
  @Column({ nullable: true })
  province: string;

  @AutoMap()
  @Column({ nullable: true })
  postalCode: string;

  @AutoMap()
  @Column({ nullable: true })
  country: string;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true })
  employment: Record<string, any>;

  // System Fields
  @AutoMap()
  @Column({ name: 'is_deleted', default: false })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({
    name: 'deleted_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  deletedAt?: Date;

  @AutoMap()
  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;
}
