import { Module } from '@nestjs/common';
import { UsersRepository } from './repositories/user.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from './entities/user.entity';
import { PriceSetEntity } from '../../../pricing/price-sets/infrastructure/entities/price-set.entity';
import { PriceSetCustomerEntity } from '../../../pricing/price-sets/infrastructure/entities/price-set-customer.entity';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { UserFilterConfig } from '../user-filter.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      PriceSetEntity,
      PriceSetCustomerEntity,
    ]),
  ],
  providers: [
    UsersRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(UserFilterConfig()),
    },
  ],
  exports: [UsersRepository],
})
export class RelationalUserPersistenceModule {}
