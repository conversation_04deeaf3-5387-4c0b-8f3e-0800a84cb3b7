import { UserStatus } from '../../domain/user.types';
import { CustomerCategoryEntity } from '../../../customer-categories/infrastructure/persistence/relational/entities/customer-category.entity';
import {
  CustomerCategory,
  CustomerDto,
} from '@app/business/user/users/dto/customer-dto.model';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';

export class CustomerMapper {
  /**
   * Maps a UserEntity directly to CustomerDto
   */
  static fromEntity(entity: UserEntity): CustomerDto {
    return {
      id: entity.id,
      companyName: entity.companyName,
      contactName: entity.contactName,
      accountNumber: entity.accountNumber,
      phoneNumberCountryCode: entity.phoneCountryCode,
      phoneNumber: entity.phoneNumber,
      phoneExtension: entity.phoneExtension,
      faxNumberCountryCode: entity.faxCountryCode,
      faxNumber: entity.faxNumber,
      website: entity.website,
      email: entity.email,
      // Map categories if they exist
      categories: entity.categories
        ? entity.categories.map(this.mapCategory)
        : [],
      // Convert status from enum to boolean
      status: entity.status === UserStatus.Active,
      addressLine1: entity.addressLine1,
      addressLine2: entity.addressLine2,
      city: entity.city,
      province: entity.province,
      postalCode: entity.postalCode,
      country: entity.country,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  /**
   * Maps a category entity to CustomerCategory
   */
  private static mapCategory(
    category: CustomerCategoryEntity,
  ): CustomerCategory {
    return {
      id: category.id,
      name: category.name,
    };
  }
}
