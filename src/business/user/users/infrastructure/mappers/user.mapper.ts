import { User } from '../../domain/user';
import { UserEntity } from '../entities/user.entity';

export class UserMapper {
  static toDomain(raw: UserEntity): User {
    const domainEntity = new User();
    domainEntity.id = raw.id;
    domainEntity.tenantId = raw.tenantId;
    domainEntity.companyName = raw.companyName;
    domainEntity.contactName = raw.contactName;
    domainEntity.accountNumber = raw.accountNumber;
    domainEntity.phoneNumberCountryCode = raw.phoneCountryCode;
    domainEntity.phoneNumber = raw.phoneNumber;
    domainEntity.phoneExtension = raw.phoneExtension;
    domainEntity.faxNumberCountryCode = raw.faxCountryCode;
    domainEntity.categories = raw.categories;
    domainEntity.faxNumber = raw.faxNumber;
    domainEntity.website = raw.website;
    domainEntity.email = raw.email;
    domainEntity.password = raw.password;
    domainEntity.emailVerified = raw.emailVerified;
    domainEntity.status = raw.status;
    domainEntity.lastLoginAt = raw.lastLoginAt;
    domainEntity.loginCount = raw.loginCount;
    domainEntity.failedAttempts = raw.failedAttempts;
    domainEntity.lockedUntil = raw.lockedUntil;
    domainEntity.origin = raw.origin;
    domainEntity.userType = raw.userType;
    domainEntity.notificationSettings = raw.notificationSettings;
    domainEntity.metadata = raw.metadata;
    domainEntity.preferences = raw.preferences;
    domainEntity.addressLine1 = raw.addressLine1;
    domainEntity.addressLine2 = raw.addressLine2;
    domainEntity.city = raw.city;
    domainEntity.province = raw.province;
    domainEntity.postalCode = raw.postalCode;
    domainEntity.country = raw.country;
    domainEntity.categories = raw.categories;
    domainEntity.isDeleted = raw.isDeleted;
    domainEntity.deletedAt = raw.deletedAt;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    return domainEntity;
  }

  static toPersistence(domainEntity: User): UserEntity {
    const persistenceEntity = new UserEntity();
    if (domainEntity.id) persistenceEntity.id = domainEntity.id;
    persistenceEntity.tenantId = domainEntity.tenantId;
    persistenceEntity.companyName = domainEntity.companyName;
    persistenceEntity.contactName = domainEntity.contactName;
    persistenceEntity.accountNumber = domainEntity.accountNumber;
    persistenceEntity.phoneNumber = domainEntity.phoneNumber;
    persistenceEntity.phoneExtension = domainEntity.phoneExtension;
    persistenceEntity.faxNumber = domainEntity.faxNumber;
    persistenceEntity.website = domainEntity.website;
    persistenceEntity.email = domainEntity.email;
    persistenceEntity.password = domainEntity.password;
    persistenceEntity.emailVerified = domainEntity.emailVerified;
    persistenceEntity.status = domainEntity.status;
    persistenceEntity.lastLoginAt = domainEntity.lastLoginAt;
    persistenceEntity.loginCount = domainEntity.loginCount;
    persistenceEntity.failedAttempts = domainEntity.failedAttempts;
    persistenceEntity.lockedUntil = domainEntity.lockedUntil;
    persistenceEntity.origin = domainEntity.origin;
    persistenceEntity.userType = domainEntity.userType;
    persistenceEntity.notificationSettings = domainEntity.notificationSettings;
    persistenceEntity.metadata = domainEntity.metadata;
    persistenceEntity.preferences = domainEntity.preferences;
    persistenceEntity.addressLine1 = domainEntity.addressLine1;
    if (domainEntity.addressLine2)
      persistenceEntity.addressLine2 = domainEntity.addressLine2;
    persistenceEntity.city = domainEntity.city;
    persistenceEntity.province = domainEntity.province;
    persistenceEntity.postalCode = domainEntity.postalCode;
    persistenceEntity.country = domainEntity.country;
    persistenceEntity.isDeleted = domainEntity.isDeleted;
    persistenceEntity.deletedAt = domainEntity.deletedAt;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    return persistenceEntity;
  }
}
