import { ApiProperty } from '@nestjs/swagger';

export class PriceSetDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: '5f7a7a7a-7a7a-7a7a-7a7a-7a7a7a7a7a7a' })
  priceSetId: string;

  @ApiProperty({ example: 'VNP 4 hours Fuel Charges' })
  name: string;

  @ApiProperty({ example: 'Internal VNP 4 hours Fuel Charges' })
  internalName: string;
}

export class GetCustomerPriceSetsResponseDto {
  @ApiProperty({ type: [PriceSetDto] })
  data: PriceSetDto[];
}
