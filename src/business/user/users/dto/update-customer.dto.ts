import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsArray,
  ValidateNested,
  IsOptional,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CategorySelectionDto } from '@app/business/user/customer-categories/dto/category-selection.dto';
import { UserStatus } from '@app/business/user/users/domain/user.types';

export class UpdateCustomerDto {
  @ApiPropertyOptional({
    example: 'Acme Corporation',
    description: 'Company name',
  })
  @IsString()
  @IsOptional()
  companyName?: string;

  @ApiPropertyOptional({
    example: '<PERSON>',
    description: 'Contact person name',
  })
  @IsString()
  @IsOptional()
  contactName?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Email address',
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    example: '+1',
    description: 'Phone country code',
  })
  @IsString()
  @IsOptional()
  phoneCountryCode?: string;

  @ApiPropertyOptional({
    example: '2345678901',
    description: 'Phone number (without country code)',
  })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    example: '123',
    description: 'Phone extension',
  })
  @IsString()
  @IsOptional()
  phoneExtension?: string;

  @ApiPropertyOptional({
    example: '+1',
    description: 'Fax country code',
  })
  @IsString()
  @IsOptional()
  faxCountryCode?: string;

  @ApiPropertyOptional({
    example: '2345678901',
    description: 'Fax number (without country code)',
  })
  @IsString()
  @IsOptional()
  faxNumber?: string;

  @ApiPropertyOptional({
    example: 'https://www.example.com',
    description: 'Website URL',
  })
  @IsString()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional({
    example: '123 Main St',
    description: 'Address line 1',
  })
  @IsString()
  @IsOptional()
  addressLine1?: string;

  @ApiPropertyOptional({
    example: 'Suite 100',
    description: 'Address line 2',
  })
  @IsString()
  @IsOptional()
  addressLine2?: string;

  @ApiPropertyOptional({
    example: 'Montreal',
    description: 'City',
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiPropertyOptional({
    example: 'Quebec',
    description: 'Province/State',
  })
  @IsString()
  @IsOptional()
  province?: string;

  @ApiPropertyOptional({
    example: 'H3Z 2Y7',
    description: 'Postal code',
  })
  @IsString()
  @IsOptional()
  postalCode?: string;

  @ApiPropertyOptional({
    example: 'Canada',
    description: 'Country',
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    example: 'Status',
    description: 'Active',
  })
  @IsString()
  @IsNotEmpty()
  status: UserStatus;

  @ApiPropertyOptional({
    type: [CategorySelectionDto],
    description: 'Categories to assign to the customer',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CategorySelectionDto)
  @IsOptional()
  categories?: CategorySelectionDto[];
}
