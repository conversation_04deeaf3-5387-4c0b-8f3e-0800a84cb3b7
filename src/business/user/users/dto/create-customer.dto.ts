import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CategorySelectionDto } from '@app/business/user/customer-categories/dto/category-selection.dto';
import { UserStatus } from '@app/business/user/users/domain/user.types';

export class CreateCustomerDto {
  @ApiProperty({
    example: 'Acme Corporation',
    description: 'Company name',
  })
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'Contact person name',
  })
  @IsString()
  @IsNotEmpty()
  contactName: string;

  @ApiProperty({
    example: 'ACC123456',
    description: 'Auto Generated Account Number',
  })
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  // Phone information with country code
  @ApiProperty({
    example: '+1',
    description: 'Phone country code',
  })
  @IsString()
  @IsNotEmpty()
  phoneCountryCode: string;

  @ApiProperty({
    example: '**********',
    description: 'Phone number (without country code)',
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiPropertyOptional({
    example: '123',
    description: 'Phone extension',
  })
  @IsString()
  @IsOptional()
  phoneExtension?: string;

  // Fax information
  @ApiPropertyOptional({
    example: '+1',
    description: 'Fax country code',
  })
  @IsString()
  @IsOptional()
  faxCountryCode?: string;

  @ApiPropertyOptional({
    example: '**********',
    description: 'Fax number (without country code)',
  })
  @IsString()
  @IsOptional()
  faxNumber?: string;

  @ApiPropertyOptional({
    example: 'https://www.example.com',
    description: 'Website URL',
  })
  @IsString()
  @IsOptional()
  website?: string;

  // Address information
  @ApiProperty({
    example: '123 Main St',
    description: 'Address line 1',
  })
  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @ApiPropertyOptional({
    example: 'Suite 100',
    description: 'Address line 2',
  })
  @IsString()
  @IsOptional()
  addressLine2?: string;

  @ApiProperty({
    example: 'Montreal',
    description: 'City',
  })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({
    example: 'Quebec',
    description: 'Province/State',
  })
  @IsString()
  @IsNotEmpty()
  province: string;

  @ApiProperty({
    example: 'H3Z 2Y7',
    description: 'Postal code',
  })
  @IsString()
  @IsNotEmpty()
  postalCode: string;

  @ApiProperty({
    example: 'Canada',
    description: 'Country',
  })
  @IsString()
  @IsNotEmpty()
  country: string;

  @ApiProperty({
    example: 'Status',
    description: 'Active',
  })
  @IsString()
  @IsNotEmpty()
  status: UserStatus;

  // Categories
  @ApiProperty({
    type: [CategorySelectionDto],
    description: 'Categories to assign to the customer',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CategorySelectionDto)
  categories: CategorySelectionDto[];
}
