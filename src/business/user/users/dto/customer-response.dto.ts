import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CategoryResponseDto } from '@app/business/user/customer-categories/dto/category-response.dto';

export class CustomerResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id: string;

  @ApiProperty({ example: 'Acme Inc.' })
  companyName: string;

  @ApiProperty({ example: 'John Doe' })
  contactName: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiProperty({ example: 'ACC123456' })
  accountNumber: string | undefined;

  @ApiPropertyOptional({ example: '+1' })
  phoneCountryCode?: string;

  @ApiPropertyOptional({ example: '**********' })
  phoneNumber?: string;

  @ApiPropertyOptional({ example: '123' })
  phoneExtension?: string;

  @ApiPropertyOptional({ example: '+1' })
  faxCountryCode?: string;

  @ApiPropertyOptional({ example: '**********' })
  faxNumber?: string;

  @ApiPropertyOptional({ example: 'https://acme.com' })
  website?: string;

  @ApiProperty({ example: '123 Main St' })
  addressLine1: string;

  @ApiPropertyOptional({ example: 'Suite 100' })
  addressLine2?: string;

  @ApiProperty({ example: 'New York' })
  city: string;

  @ApiProperty({ example: 'NY' })
  province: string;

  @ApiProperty({ example: '10001' })
  postalCode: string;

  @ApiProperty({ example: 'USA' })
  country: string;

  @ApiProperty({ example: 'Active' })
  status: string;

  @ApiProperty({ type: [CategoryResponseDto] })
  categories: CategoryResponseDto[];

  @ApiProperty({ example: '2023-01-15T08:30:00.000Z' })
  createdAt: Date;

  @ApiProperty({ example: '2023-01-15T08:30:00.000Z' })
  updatedAt: Date;
}
