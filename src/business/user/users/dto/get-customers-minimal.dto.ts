import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
export class GetCustomerMinimalDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: 'John Doe' })
  customerName: string;

  @AutoMap()
  @ApiProperty({ example: 'Acme Corp' })
  companyName: string;
}
export class GetAllCustomerMinimalDto {
  @ApiProperty({ type: [GetCustomerMinimalDto] })
  data: GetCustomerMinimalDto[];
}
