import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  name: string;

  @ApiPropertyOptional({
    description: 'Unique account number for the user',
    example: 'ACC123456',
  })
  @IsString()
  @IsOptional()
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Phone number with country code',
    example: '+**********',
  })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Fax number',
    example: '+**********',
  })
  @IsString()
  @IsOptional()
  faxNumber?: string;

  @ApiProperty({
    description: 'Email address (must be unique within tenant)',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}
