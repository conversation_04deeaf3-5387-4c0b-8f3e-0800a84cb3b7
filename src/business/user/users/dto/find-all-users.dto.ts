import { ApiProperty } from '@nestjs/swagger';
import { User } from '../domain/user';

export class FindAllUsersResponseDto {
  @ApiProperty({
    description: 'List of users matching query',
    type: [User],
  })
  data: User[];

  @ApiProperty({
    description: 'Total number of users matching query',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Has next page',
    example: true,
  })
  hasNextPage: boolean;

  @ApiProperty({
    description: 'Has previous page',
    example: false,
  })
  hasPreviousPage: boolean;
}
