/**
 * Represents a customer category with minimal information
 */
export class CustomerCategory {
  id: string;
  name: string;
}

/**
 * Data Transfer Object for customer information with specific format
 * to ensure API compatibility
 */
export class CustomerDto {
  id: string;
  companyName: string;
  contactName: string;
  accountNumber?: string;
  phoneNumberCountryCode?: string;
  phoneNumber?: string;
  phoneExtension?: string;
  faxNumberCountryCode?: string;
  faxNumber?: string;
  website?: string;
  email: string;
  categories: CustomerCategory[];
  status: boolean;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  createdAt: Date;
  updatedAt: Date;
}
