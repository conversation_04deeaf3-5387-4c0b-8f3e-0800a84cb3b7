import { CustomerCategoryEntity } from '../../customer-categories/infrastructure/persistence/relational/entities/customer-category.entity';
import { UserOrigin, UserStatus, UserType } from './user.types';

export class User {
  id: string;
  tenantId: string;

  // Basic Information
  companyName: string;
  contactName: string;
  accountNumber?: string;
  phoneNumberCountryCode?: string;
  phoneNumber?: string;
  phoneExtension?: string;
  faxNumberCountryCode?: string;
  faxNumber?: string;
  website?: string;
  email: string;

  // Authentication
  password: string;
  emailVerified: boolean;
  status: UserStatus;
  lastLoginAt?: Date;
  loginCount: number;
  failedAttempts: number;
  lockedUntil?: Date;

  // Profile
  origin: UserOrigin;
  userType: UserType;

  // Preferences & Metadata
  notificationSettings: {
    channels: {
      sms: boolean;
      email: boolean;
      push: boolean;
      in_app: boolean;
    };
    order: {
      [key: string]: {
        enabled: boolean;
        recipients?: string[];
      };
    };
    payment: {
      [key: string]: {
        enabled: boolean;
      };
    };
    security: {
      [key: string]: {
        enabled: boolean;
      };
    };
  };

  metadata?: Record<string, any>;
  preferences?: Record<string, any>;

  // Address
  addressLine1: string;
  addressLine2?: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  categories: CustomerCategoryEntity[];

  // System Fields
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
