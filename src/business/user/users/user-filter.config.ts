import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const UserFilterConfig = () => {
  const fields = [
    {
      fieldName: 'companyName',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid company name format',
      },
    },
    {
      fieldName: 'accountNumber',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid account number format',
      },
    },
    {
      fieldName: 'contactName',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid contact name format',
      },
    },
    {
      fieldName: 'addressLine1',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid address line 1 format',
      },
    },
    {
      fieldName: 'city',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid city format',
      },
    },
    {
      fieldName: 'phoneNumber',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid phone number format',
      },
    },
    {
      fieldName: 'email',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid email format',
      },
    },
    {
      fieldName: 'postalCode',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid postal code format',
      },
    },
  ];

  const userConfig = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    userConfig.addField(fieldName, options);
  });

  return userConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields([
      'companyName',
      'accountNumber',
      'contactName',
      'addressLine1',
      'city',
      'phoneNumber',
      'email',
      'postalCode',
    ])
    .setSortableFields([
      'companyName',
      'accountNumber',
      'contactName',
      'addressLine1',
      'city',
      'phoneNumber',
      'email',
      'postalCode',
      'createdAt',
    ])
    .build();
};
