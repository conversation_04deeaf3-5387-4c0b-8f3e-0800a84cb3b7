import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';

@Injectable()
export class AccountNumberService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  async generateUniqueAccountNumber(): Promise<string> {
    const prefix = 'ACC';
    let isUnique = false;
    let accountNumber = '';

    while (!isUnique) {
      // Generate a random 6-digit number
      const randomNum = Math.floor(100000 + Math.random() * 900000);
      accountNumber = `${prefix}${randomNum}`;

      // Check if it exists
      const existing = await this.userRepository.findOne({
        where: { accountNumber },
      });

      if (!existing) {
        isUnique = true;
      }
    }

    return accountNumber;
  }
}
