import { forwardRef, Module } from '@nestjs/common';

import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { RelationalUserPersistenceModule } from './infrastructure/relational-persistence.module';
import { CustomerCategoriesModule } from '../customer-categories/customer-categories.module';
import { AccountNumberService } from './services/account-number.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from './infrastructure/entities/user.entity';
import { QueryService } from '@utils/query-creator/query.service';
import { TenantsModule } from '@app/business/user/tenants/tenants.module';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { UserFilterConfig } from './user-filter.config';
import { ContactsModule } from '@app/business/user/contacts/contacts.module';

@Module({
  imports: [
    RelationalUserPersistenceModule,
    TypeOrmModule.forFeature([UserEntity]),
    forwardRef(() => CustomerCategoriesModule),
    TenantsModule,
    forwardRef(() => ContactsModule),
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    AccountNumberService,
    QueryService,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(UserFilterConfig()),
    },
  ],
  exports: [UsersService],
})
export class UsersModule {}
