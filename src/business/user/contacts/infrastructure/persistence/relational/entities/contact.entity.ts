import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  Join<PERSON>olumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Unique,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { ContactPermissions } from '../../../../domain/contact-permissions';

@Entity({
  name: 'contacts',
})
@Unique(['userId', 'email'])
export class ContactEntity extends EntityRelationalHelper {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'uuid' })
  @Index()
  userId: string;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ type: 'jsonb', default: [], nullable: true })
  categoryIds?: Array<string>;

  @Column({ length: 100 })
  name: string;

  @Column()
  email: string;

  @Column({ name: 'phone_country_code', nullable: true })
  phoneCountryCode?: string;

  @Column({ name: 'phone_number', nullable: true })
  phoneNumber?: string;

  @Column({ name: 'phone_extension', nullable: true })
  phoneExtension?: string;

  @Column({ length: 255, select: false })
  password: string;

  @Column({ name: 'email_verified', default: false })
  emailVerified: boolean;

  @Column({
    name: 'last_login_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  lastLoginAt?: Date;

  @Column({ name: 'login_count', default: 0 })
  loginCount: number;

  @Column({ name: 'failed_attempts', default: 0 })
  failedAttempts: number;

  @Column({ name: 'is_primary', default: false })
  isPrimary?: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({
    name: 'locked_until',
    type: 'timestamp with time zone',
    nullable: true,
  })
  lockedUntil?: Date;

  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @Column({ name: 'permissions', type: 'jsonb', default: () => "'{}'" })
  permissions: ContactPermissions;

  @Column({ name: 'is_deleted', default: false })
  isDeleted: boolean;

  @Column({ name: 'deleted_at', nullable: true })
  deletedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;

  @ManyToOne(() => UserEntity, { eager: false })
  @JoinColumn({ name: 'created_by' })
  createdByUser?: UserEntity;

  @ManyToOne(() => UserEntity, { eager: false })
  @JoinColumn({ name: 'updated_by' })
  updatedByUser?: UserEntity;

  @Column({ name: 'created_by', type: 'uuid', nullable: true })
  createdBy?: string;

  @Column({ name: 'updated_by', type: 'uuid', nullable: true })
  updatedBy?: string;

}
