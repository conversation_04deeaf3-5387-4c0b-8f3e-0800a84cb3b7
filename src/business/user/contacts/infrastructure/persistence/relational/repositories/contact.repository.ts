import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactEntity } from '../entities/contact.entity';
import { Contact } from '../../../../domain/contact';
import { ContactRepository } from '../../contact.repository';
import {
  BaseQueryParams,
  PaginatedResult,
} from '@utils/query-creator/interfaces';
import { QueryService } from '@utils/query-creator/query.service';

@Injectable()
export class ContactRelationalRepository implements ContactRepository {
  constructor(
    @InjectRepository(ContactEntity)
    private readonly repository: Repository<ContactEntity>,
    private readonly queryService: QueryService,
  ) {}

  private mapToDomain(entity: ContactEntity): Contact {
    const domain = new Contact();
    domain.id = entity.id;
    domain.userId = entity.userId;
    domain.name = entity.name;
    domain.email = entity.email;
    domain.phoneNumber = entity.phoneNumber;
    domain.password = entity.password;
    domain.emailVerified = entity.emailVerified;
    domain.lastLoginAt = entity.lastLoginAt;
    domain.loginCount = entity.loginCount;
    domain.failedAttempts = entity.failedAttempts;
    domain.lockedUntil = entity.lockedUntil;
    domain.metadata = entity.metadata;
    domain.permissions = entity.permissions;
    domain.isPrimary = entity.isPrimary;
    domain.isDeleted = entity.isDeleted;
    domain.deletedAt = entity.deletedAt || new Date();
    domain.createdAt = entity.createdAt;
    domain.updatedAt = entity.updatedAt;

    return domain;
  }

  async create(contact: Partial<Contact>): Promise<Contact> {
    const entity = this.repository.create(contact);
    const savedEntity = await this.repository.save(entity);
    return this.mapToDomain(savedEntity);
  }

  async findAll(
    userId: string,
    queryParams: BaseQueryParams = {},
  ): Promise<PaginatedResult<Contact>> {
    return this.queryService.executeQuery<ContactEntity, Contact>(
      {
        repository: this.repository,
        queryParams,
        alias: 'contact',
        mapToDomain: (entity) => this.mapToDomain(entity),
        baseConditions: { userId, isDeleted: false },
      },
      (queryBuilder) => {
        if (queryParams.search) {
          queryBuilder.andWhere(
            'LOWER(contact.name) LIKE LOWER(:search) OR LOWER(contact.email) LIKE LOWER(:search)',
            { search: `%${queryParams.search}%` },
          );
        }
      },
    );
  }

  async findById(id: string): Promise<Contact | null> {
    const entity = await this.repository.findOne({
      where: { id, isDeleted: false },
    });

    return entity ? this.mapToDomain(entity) : null;
  }

  async findByEmail(email: string): Promise<Contact | null> {
    const entity = await this.repository.findOne({
      where: { email, isDeleted: false },
      relations: ['user'],
    });

    return entity ? this.mapToDomain(entity) : null;
  }

  async update(id: string, data: Partial<Contact>): Promise<Contact> {
    const entity = await this.repository.findOne({
      where: { id, isDeleted: false },
      relations: ['category'],
    });

    if (!entity) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    const updatedEntity = await this.repository.save({
      ...entity,
      ...data,
      id, // Ensure ID doesn't change
    });

    return this.mapToDomain(updatedEntity);
  }

  async softDelete(id: string): Promise<void> {
    const entity = await this.repository.findOne({
      where: { id, isDeleted: false },
    });

    if (!entity) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    await this.repository.update(id, {
      isDeleted: true,
      deletedAt: new Date(),
    });
  }

  async restore(id: string): Promise<void> {
    const entity = await this.repository.findOne({
      where: { id, isDeleted: true },
    });

    if (!entity) {
      throw new NotFoundException(`Deleted contact with ID ${id} not found`);
    }

    await this.repository.update(id, {
      isDeleted: false,
      deletedAt: new Date(),
    });
  }
}
