import { Contact } from '../../domain/contact';
import {
  BaseQueryParams,
  PaginatedResult,
} from '@utils/query-creator/interfaces';

export abstract class ContactRepository {
  abstract create(contact: Partial<Contact>): Promise<Contact>;
  abstract findAll(
    userId: string,
    queryParams?: BaseQueryParams,
  ): Promise<PaginatedResult<Contact>>;
  abstract findById(id: string): Promise<Contact | null>;
  abstract findByEmail(email: string): Promise<Contact | null>;
  abstract update(id: string, data: Partial<Contact>): Promise<Contact>;
  abstract softDelete(id: string): Promise<void>;
  abstract restore(id: string): Promise<void>;
}
