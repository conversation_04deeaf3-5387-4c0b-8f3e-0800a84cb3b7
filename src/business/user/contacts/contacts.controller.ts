import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { ContactsService } from './contacts.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { UpdateContactPermissionsDto } from './dto/update-contact-permissions.dto';
import { Contact } from './domain/contact';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { UsersService } from '../users/users.service';
import { TenantValidationService } from '@app/business/user/tenants/tenant-validation.service';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';

@ApiTags('Business - User - Contacts')
@ApiBearerAuth()
@Controller({
  path: 'customers/:customerId/contacts',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class ContactsController {
  constructor(
    private readonly contactsService: ContactsService,
    private readonly usersService: UsersService,
    private readonly tenantValidationService: TenantValidationService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new contact for a customer' })
  @ApiParam({ name: 'customerId', type: String, description: 'Customer ID' })
  @ApiBody({ type: CreateContactDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer or department not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Contact with this email already exists',
  })
  async create(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Body() createContactDto: CreateContactDto,
  ): Promise<void> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.usersService.getTenantCustomer(tenantId, customerId);

    await this.contactsService.create(tenantId, customerId, createContactDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Filtering for contacts',
    description: `
Filter contacts using flexible criteria with field:operator=value syntax.

## Example filter queries:
- Find contacts by name: \`/customers/:customerId/contacts/filter?name:contains=John\`
- Find contacts with specific email: \`/customers/:customerId/contacts/filter?email:eq=<EMAIL>\`
- Find verified contacts: \`/customers/:customerId/contacts/filter?emailVerified:eq=true\`
- Find primary contacts: \`/customers/:customerId/contacts/filter?isPrimary:eq=true\`
- Find contacts created after a specific date: \`/customers/:customerId/contacts/filter?createdAt:gt=2023-01-01\`
- Find contacts with specific phone country code: \`/customers/:customerId/contacts/filter?phoneCountryCode:eq=+1\`
    `,
  })
  // Filter examples for each field
  @ApiQuery({
    name: 'name:eq',
    required: false,
    type: String,
    description: 'Filter by exact name match',
  })
  @ApiQuery({
    name: 'name:contains',
    required: false,
    type: String,
    description: 'Filter by name containing text',
  })
  @ApiQuery({
    name: 'email:eq',
    required: false,
    type: String,
    description: 'Filter by exact email match',
  })
  @ApiQuery({
    name: 'email:contains',
    required: false,
    type: String,
    description: 'Filter by email containing text',
  })
  @ApiQuery({
    name: 'emailVerified:eq',
    required: false,
    type: Boolean,
    description: 'Filter by email verification status',
  })
  @ApiQuery({
    name: 'isPrimary:eq',
    required: false,
    type: Boolean,
    description: 'Filter by primary contact status',
  })
  @ApiQuery({
    name: 'phoneCountryCode:eq',
    required: false,
    type: String,
    description: 'Filter by phone country code',
  })
  @ApiQuery({
    name: 'phoneNumber:contains',
    required: false,
    type: String,
    description: 'Filter by phone number containing digits',
  })
  @ApiQuery({
    name: 'createdAt:gt',
    required: false,
    type: String,
    description: 'Filter by creation date greater than',
  })
  @ApiQuery({
    name: 'createdAt:between',
    required: false,
    type: String,
    description: 'Filter by creation date range (comma-separated)',
  })
  @ApiQuery({
    name: 'isDeleted:eq',
    required: false,
    type: Boolean,
    description: 'Include deleted contacts',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Filtered contacts retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid filter parameters',
  })
  async filter(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Query() filter: BaseFilterDto,
  ): Promise<PaginatedResult<Contact>> {
    try {
      // Validate tenant access
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);

      // Verify customer exists and belongs to tenant
      await this.usersService.getTenantCustomer(tenantId, customerId);

      // Process the filter request
      return this.contactsService.filter(customerId, request.query, filter);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        'Error filtering contacts',
        ErrorCode.VALIDATION_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a contact by ID' })
  @ApiParam({ name: 'customerId', type: String, description: 'Customer ID' })
  @ApiParam({ name: 'id', type: String, description: 'Contact ID' })
  @ApiResponse({ status: HttpStatus.OK, type: Contact })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  async findOne(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Param('id') id: string,
  ): Promise<Contact> {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    const contact = await this.contactsService.findById(id);

    // Verify that the contact belongs to the customer
    if (contact.userId !== customerId) {
      throw new AppException(
        'Contact does not belong to this customer',
        ErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    return contact;
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a contact' })
  @ApiParam({ name: 'customerId', type: String, description: 'Customer ID' })
  @ApiParam({ name: 'id', type: String, description: 'Contact ID' })
  @ApiBody({ type: UpdateContactDto })
  @ApiResponse({ status: HttpStatus.OK, type: Contact })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Contact does not belong to this customer',
  })
  async update(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Param('id') id: string,
    @Body() updateContactDto: UpdateContactDto,
  ): Promise<Contact> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.usersService.getTenantCustomer(tenantId, customerId);

    return this.contactsService.update(
      id,
      customerId,
      tenantId,
      updateContactDto,
    );
  }

  @Put(':id/permissions')
  @ApiOperation({ summary: 'Update contact permissions' })
  @ApiParam({ name: 'customerId', type: String, description: 'Customer ID' })
  @ApiParam({ name: 'id', type: String, description: 'Contact ID' })
  @ApiBody({ type: UpdateContactPermissionsDto })
  @ApiResponse({ status: HttpStatus.OK, type: Contact })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Contact does not belong to this customer',
  })
  async updatePermissions(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Param('id') id: string,
    @Body() permissions: UpdateContactPermissionsDto,
  ): Promise<Contact> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.usersService.getTenantCustomer(tenantId, customerId);

    return this.contactsService.updatePermissions(id, customerId, permissions);
  }

  @Post(':id/reset-password')
  @ApiOperation({ summary: 'Reset password for a contact' })
  @ApiParam({ name: 'customerId', type: String, description: 'Customer ID' })
  @ApiParam({ name: 'id', type: String, description: 'Contact ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password reset successfully',
    schema: {
      properties: {
        newPassword: { type: 'string', example: 'a1b2c3d4' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Contact does not belong to this customer',
  })
  async resetPassword(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Param('id') id: string,
  ): Promise<{ newPassword: string }> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.usersService.getTenantCustomer(tenantId, customerId);

    return this.contactsService.resetPassword(id, customerId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a contact' })
  @ApiParam({ name: 'customerId', type: String, description: 'Customer ID' })
  @ApiParam({ name: 'id', type: String, description: 'Contact ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Contact deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Contact does not belong to this customer',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Param('id') id: string,
  ): Promise<void> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.usersService.getTenantCustomer(tenantId, customerId);

    await this.contactsService.delete(id, customerId);
  }

  @Post(':id/restore')
  @ApiOperation({ summary: 'Restore a deleted contact' })
  @ApiParam({ name: 'customerId', type: String, description: 'Customer ID' })
  @ApiParam({ name: 'id', type: String, description: 'Contact ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Contact restored successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Contact not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Contact does not belong to this customer',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async restore(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
    @Param('id') id: string,
  ): Promise<void> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);

    await this.usersService.getTenantCustomer(tenantId, customerId);

    await this.contactsService.restore(id, customerId);
  }
}
