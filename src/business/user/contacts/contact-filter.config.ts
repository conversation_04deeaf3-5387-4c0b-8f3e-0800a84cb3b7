import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const ContactFilterConfig = () => {
  const fields = [
    {
      fieldName: 'name',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid contact name format',
      },
    },
    {
      fieldName: 'email',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid email format',
      },
    },
    {
      fieldName: 'phoneNumber',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid phone number format',
      },
    },
    {
      fieldName: 'phoneCountryCode',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid country code format',
      },
    },
    {
      fieldName: 'isPrimary',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'Invalid primary status value',
      },
    },
    {
      fieldName: 'emailVerified',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'Invalid email verification status',
      },
    },
    {
      fieldName: 'userId',
      options: {
        operators: [FilterOperator.EQ, FilterOperator.IN],
        validationMessage: 'Invalid user ID format',
      },
    },
    {
      fieldName: 'createdAt',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        validate: (value: unknown) =>
          typeof value === 'string' && !isNaN(Date.parse(value)),
        validationMessage: 'Invalid date format',
      },
    },
    {
      fieldName: 'updatedAt',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        validate: (value: unknown) =>
          typeof value === 'string' && !isNaN(Date.parse(value)),
        validationMessage: 'Invalid date format',
      },
    },
    {
      fieldName: 'isDeleted',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'Invalid deletion status',
      },
    },
  ];

  const contactConfig = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    contactConfig.addField(fieldName, options);
  });

  return contactConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields(['name', 'email', 'phoneNumber'])
    .setSortableFields([
      'name',
      'email',
      'phoneNumber',
      'phoneCountryCode',
      'isPrimary',
      'emailVerified',
      'createdAt',
      'updatedAt',
      'isDeleted',
    ])
    .build();
};
