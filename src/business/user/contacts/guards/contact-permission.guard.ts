import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ContactsService } from '../contacts.service';

export const REQUIRED_CONTACT_PERMISSION = 'required_contact_permission';

@Injectable()
export class ContactPermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private contactsService: ContactsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermission = this.reflector.getAllAndOverride<string>(
      REQUIRED_CONTACT_PERMISSION,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermission) {
      return true; // No specific permission required
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Skip check if not a contact user
    if (!user.isContact) {
      return true;
    }

    const contactId = user.sub;
    const hasPermission = await this.contactsService.checkPermission(
      contactId,
      requiredPermission,
    );

    if (!hasPermission) {
      throw new ForbiddenException(
        `Missing required permission: ${requiredPermission}`,
      );
    }

    return true;
  }
}
