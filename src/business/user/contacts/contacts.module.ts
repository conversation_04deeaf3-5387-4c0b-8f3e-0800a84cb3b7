import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactsController } from './contacts.controller';
import { ContactsService } from './contacts.service';
import { ContactEntity } from './infrastructure/persistence/relational/entities/contact.entity';
import { ContactRepository } from './infrastructure/persistence/contact.repository';
import { ContactRelationalRepository } from './infrastructure/persistence/relational/repositories/contact.repository';
import { CustomerCategoriesModule } from '../customer-categories/customer-categories.module';
import { UsersModule } from '../users/users.module';
import { QueryService } from '@utils/query-creator/query.service';
import { ContactPermissionGuard } from './guards/contact-permission.guard';
import { TenantsModule } from '@app/business/user/tenants/tenants.module';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { ContactFilterConfig } from '@app/business/user/contacts/contact-filter.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([ContactEntity]),
    forwardRef(() => UsersModule),
    forwardRef(() => CustomerCategoriesModule),
    forwardRef(() => TenantsModule),
  ],
  controllers: [ContactsController],
  providers: [
    ContactsService,
    {
      provide: ContactRepository,
      useClass: ContactRelationalRepository,
    },
    QueryService,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(ContactFilterConfig()),
    },
    ContactPermissionGuard,
  ],
  exports: [ContactsService, ContactRepository, ContactPermissionGuard],
})
export class ContactsModule {}
