import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';
import { ContactPermissions } from '../domain/contact-permissions';

export class UpdateContactPermissionsDto
  implements Partial<ContactPermissions>
{
  @ApiPropertyOptional({ example: true })
  @IsBoolean()
  @IsOptional()
  address?: boolean;

  @ApiPropertyOptional({ example: true })
  @IsBoolean()
  @IsOptional()
  prices?: boolean;

  @ApiPropertyOptional({ example: true })
  @IsBoolean()
  @IsOptional()
  invoices?: boolean;

  [key: string]: boolean | undefined;
}
