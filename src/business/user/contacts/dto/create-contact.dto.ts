import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEmail,
  IsOptional,
  IsObject,
  IsArray,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { CategorySelectionDto } from '@app/business/user/customer-categories/dto/category-selection.dto';
import { Type } from 'class-transformer';
import { ContactPermissions } from '@app/business/user/contacts/domain/contact-permissions';

export class CreateContactDto {
  @ApiProperty({ example: '<PERSON>' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiPropertyOptional({ example: 'USC' })
  @IsString()
  @IsOptional()
  phoneCountryCode?: string;

  @ApiPropertyOptional({ example: '5551234567' })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({ example: '888' })
  @IsString()
  @IsOptional()
  phoneExtension?: string;

  @ApiPropertyOptional({ example: true })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @ApiProperty({
    type: [CategorySelectionDto],
    description: 'Categories to assign to the customer',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CategorySelectionDto)
  categories?: CategorySelectionDto[];

  @ApiProperty({
    type: ContactPermissions,
    description: 'permissions to assign to the customer',
  })
  @IsObject()
  permissions: Record<string, any>;

  @ApiPropertyOptional({
    example: {
      title: 'Sales Director',
      notes: 'Main point of contact for sales inquiries',
    },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
