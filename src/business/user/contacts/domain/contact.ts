import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ContactPermissions } from './contact-permissions';

export class Contact {
  @ApiProperty({ description: 'Unique identifier of the contact' })
  id: string;

  @ApiProperty({
    description: 'The user (customer) ID this contact belongs to',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'The tenant ID this contact belongs to (via customer)',
  })
  tenantId?: string;

  @ApiProperty({ description: 'Full name of the contact' })
  name: string;

  @ApiProperty({ description: 'Email address of the contact' })
  email: string;

  @ApiPropertyOptional({ description: 'Phone country code' })
  phoneCountryCode?: string;

  @ApiPropertyOptional({ description: 'Phone number' })
  phoneNumber?: string;

  @ApiPropertyOptional({ description: 'Phone extension' })
  phoneExtension?: string;

  @ApiProperty({
    description: 'Whether the email has been verified',
    default: false,
  })
  emailVerified: boolean;

  @ApiPropertyOptional({
    description: 'Date and time of the last login',
    type: Date,
  })
  lastLoginAt?: Date;

  @ApiPropertyOptional({
    description: 'Number of times the contact has logged in',
    default: 0,
  })
  loginCount?: number;

  @ApiPropertyOptional({
    description: 'Number of failed login attempts',
    default: 0,
  })
  failedAttempts?: number;

  @ApiPropertyOptional({
    description: 'Whether this is the primary contact',
    default: false,
  })
  isPrimary?: boolean;

  @ApiPropertyOptional({
    description: 'Date until which the account is locked',
    type: Date,
  })
  lockedUntil?: Date;

  @ApiPropertyOptional({
    description: 'Additional metadata about the contact',
    type: 'object',
    additionalProperties: true,
    default: {},
  })
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'IDs of categories this contact belongs to',
    type: [String],
    isArray: true,
  })
  categoryIds?: string[];

  @ApiProperty({
    description: 'Permissions granted to this contact',
  })
  permissions: ContactPermissions;

  @ApiProperty({
    description: "Contact's password (not returned in responses)",
    writeOnly: true,
  })
  password?: string;

  @ApiPropertyOptional({
    description: 'Full category objects for this contact',
    type: 'array',
    isArray: true,
  })
  categories?: any[];

  @ApiProperty({
    description: 'Whether the contact has been soft-deleted',
    default: false,
  })
  isDeleted: boolean;

  @ApiProperty({
    description: 'Whether the contact is active or not',
    default: true,
  })
  isActive: boolean;

  @ApiPropertyOptional({
    description: 'Date and time when the contact was deleted',
    type: Date,
  })
  deletedAt?: Date;

  @ApiProperty({
    description: 'Date and time when the contact was created',
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date and time when the contact was last updated',
    type: Date,
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Name of the user who created this contact',
  })
  createdByName?: string;

  @ApiPropertyOptional({
    description: 'Name of the user who last updated this contact',
  })
  updatedByName?: string;
}
