import { ApiProperty } from '@nestjs/swagger';

export interface ContactPermission {
  name: string;
  description: string;
  enabled: boolean;
}

export class ContactPermissions {
  @ApiProperty({ example: true })
  address: boolean;

  @ApiProperty({ example: true })
  prices: boolean;

  @ApiProperty({ example: false })
  invoices: boolean;

  [key: string]: boolean;
}

export const DEFAULT_CONTACT_PERMISSIONS: ContactPermissions = {
  address: false,
  prices: false,
  invoices: false,
};
