import { DeepPartial } from '@utils/types/deep-partial.type';
import { NullableType } from '@utils/types/nullable.type';
import { IPaginationOptions } from '@utils/types/pagination-options';
import { Tenant } from '../../domain/tenant';

export interface IFindAllWithPaginationResponse {
  items: Tenant[];
  total: number;
}

export interface IFindAllWithPaginationOptions {
  paginationOptions: IPaginationOptions;
  where?: Record<string, any>;
}

export abstract class TenantRepository {
  abstract create(
    data: Omit<
      Tenant,
      'id' | 'createdAt' | 'updatedAt' | 'isDeleted' | 'deletedAt'
    >,
  ): Promise<Tenant>;

  abstract findAllWithPagination(
    options: IFindAllWithPaginationOptions,
  ): Promise<IFindAllWithPaginationResponse>;

  abstract findById(id: Tenant['id']): Promise<NullableType<Tenant>>;

  abstract findOne(options: {
    where: Partial<
      Omit<Tenant, 'settings' | 'address' | 'metadata' | 'preferences'>
    >;
  }): Promise<NullableType<Tenant>>;

  abstract findByIds(ids: Tenant['id'][]): Promise<Tenant[]>;

  abstract findByCompanyUniqueId(
    companyUniqueId: string,
  ): Promise<NullableType<Tenant>>;

  abstract findByDomain(domain: string): Promise<NullableType<Tenant>>;

  abstract update(
    id: Tenant['id'],
    payload: DeepPartial<Omit<Tenant, 'id' | 'createdAt' | 'companyUniqueId'>>,
  ): Promise<Tenant>;

  abstract softRemove(id: Tenant['id']): Promise<void>;

  abstract hardRemove(id: Tenant['id']): Promise<void>;

  abstract restore(id: Tenant['id']): Promise<void>;
}
