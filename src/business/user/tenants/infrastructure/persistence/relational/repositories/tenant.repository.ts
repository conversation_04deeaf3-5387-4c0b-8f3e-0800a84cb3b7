import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, FindOptionsWhere, Not } from 'typeorm';
import { TenantEntity } from '../entities/tenant.entity';
import { NullableType } from '@utils/types/nullable.type';
import { Tenant } from '../../../../domain/tenant';
import { TenantRepository } from '../../tenant.repository';
import { TenantMapper } from '../mappers/tenant.mapper';
import { IPaginationOptions } from '@utils/types/pagination-options';
import {
  TenantNotFoundException,
  TenantUniqueConstraintException,
  TenantAlreadyActiveException,
} from '@app/utils/errors/exceptions/tenant.exceptions';

@Injectable()
export class TenantRelationalRepository implements TenantRepository {
  constructor(
    @InjectRepository(TenantEntity)
    private readonly tenantRepository: Repository<TenantEntity>,
  ) {}

  async create(data: Tenant): Promise<Tenant> {
    try {
      // Check for existing tenant with same company ID or email
      const [existingCompany, existingEmail] = await Promise.all([
        this.tenantRepository.findOne({
          where: { companyUniqueId: data.companyUniqueId, isDeleted: false },
        }),
        this.tenantRepository.findOne({
          where: { contactEmail: data.contactEmail, isDeleted: false },
        }),
      ]);

      const violations: string[] = [];
      if (existingCompany) violations.push('companyUniqueId');
      if (existingEmail) violations.push('contactEmail');

      if (violations.length > 0) {
        throw new TenantUniqueConstraintException(violations);
      }

      const persistenceModel = TenantMapper.toPersistence(data);
      const newEntity = await this.tenantRepository.save(
        this.tenantRepository.create(persistenceModel),
      );
      return TenantMapper.toDomain(newEntity);
    } catch (error) {
      if (error instanceof TenantUniqueConstraintException) {
        throw error;
      }
      // Re-throw any other errors
      throw error;
    }
  }

  async findAllWithPagination({
    paginationOptions,
    where = {},
  }: {
    paginationOptions: IPaginationOptions;
    where?: FindOptionsWhere<TenantEntity>;
  }): Promise<{ items: Tenant[]; total: number }> {
    const [entities, total] = await this.tenantRepository.findAndCount({
      where: {
        isDeleted: false,
        ...where,
      },
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
      order: {
        createdAt: 'DESC',
      },
    });

    return {
      items: entities.map((entity) => TenantMapper.toDomain(entity)),
      total,
    };
  }

  async findById(id: Tenant['id']): Promise<NullableType<Tenant>> {
    const entity = await this.tenantRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!entity) {
      throw new TenantNotFoundException(id);
    }

    return TenantMapper.toDomain(entity);
  }

  async findOne(options: {
    where: Partial<
      Omit<Tenant, 'settings' | 'address' | 'metadata' | 'preferences'>
    >;
  }): Promise<NullableType<Tenant>> {
    const entity = await this.tenantRepository.findOne({
      where: {
        ...options.where,
        isDeleted: false,
      },
    });

    return entity ? TenantMapper.toDomain(entity) : null;
  }

  async findByIds(ids: Tenant['id'][]): Promise<Tenant[]> {
    const entities = await this.tenantRepository.find({
      where: {
        id: In(ids),
        isDeleted: false,
      },
    });

    if (entities.length !== ids.length) {
      const foundIds = entities.map((e) => e.id);
      const missingIds = ids.filter((id) => !foundIds.includes(id));
      throw new TenantNotFoundException(missingIds[0], {
        missingIds,
        foundIds,
        totalRequested: ids.length,
        totalFound: entities.length,
      });
    }

    return entities.map((entity) => TenantMapper.toDomain(entity));
  }

  async findByCompanyUniqueId(
    companyUniqueId: string,
  ): Promise<NullableType<Tenant>> {
    const entity = await this.tenantRepository.findOne({
      where: {
        companyUniqueId,
        isDeleted: false,
      },
    });

    return entity ? TenantMapper.toDomain(entity) : null;
  }

  async findByDomain(domain: string): Promise<NullableType<Tenant>> {
    const entity = await this.tenantRepository.findOne({
      where: {
        domain,
        isDeleted: false,
      },
    });

    return entity ? TenantMapper.toDomain(entity) : null;
  }

  async update(id: Tenant['id'], payload: Partial<Tenant>): Promise<Tenant> {
    const entity = await this.tenantRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!entity) {
      throw new TenantNotFoundException(id);
    }

    // Check unique constraints if email or company ID is being updated
    if (payload.contactEmail && payload.contactEmail !== entity.contactEmail) {
      const existingEmail = await this.tenantRepository.findOne({
        where: {
          contactEmail: payload.contactEmail,
          id: Not(id),
          isDeleted: false,
        },
      });
      if (existingEmail) {
        throw new TenantUniqueConstraintException(['contactEmail']);
      }
    }

    if (
      payload.companyUniqueId &&
      payload.companyUniqueId !== entity.companyUniqueId
    ) {
      const existingCompany = await this.tenantRepository.findOne({
        where: {
          companyUniqueId: payload.companyUniqueId,
          id: Not(id),
          isDeleted: false,
        },
      });
      if (existingCompany) {
        throw new TenantUniqueConstraintException(['companyUniqueId']);
      }
    }

    const updatedEntity = await this.tenantRepository.save(
      this.tenantRepository.create({
        ...TenantMapper.toDomain(entity),
        ...payload,
        updatedAt: new Date(),
      }),
    );

    return TenantMapper.toDomain(updatedEntity);
  }

  async softRemove(id: Tenant['id']): Promise<void> {
    const entity = await this.tenantRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!entity) {
      throw new TenantNotFoundException(id);
    }

    await this.tenantRepository.save({
      ...entity,
      isDeleted: true,
      deletedAt: new Date(),
    });
  }

  async hardRemove(id: Tenant['id']): Promise<void> {
    const entity = await this.tenantRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new TenantNotFoundException(id);
    }

    await this.tenantRepository.remove(entity);
  }

  async restore(id: Tenant['id']): Promise<void> {
    const entity = await this.tenantRepository.findOne({
      where: {
        id,
        isDeleted: true,
      },
    });

    if (!entity) {
      throw new TenantNotFoundException(id);
    }

    if (!entity.isDeleted) {
      throw new TenantAlreadyActiveException(id);
    }

    entity.isDeleted = false;
    entity.deletedAt = null;
    await this.tenantRepository.save(entity);
  }
}
