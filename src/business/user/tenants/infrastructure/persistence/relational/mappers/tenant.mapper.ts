import { Tenant } from '../../../../domain/tenant';
import { TenantEntity } from '../entities/tenant.entity';

export class TenantMapper {
  static toDomain(raw: TenantEntity): Tenant {
    const tenant = new Tenant();
    tenant.id = raw.id;
    tenant.name = raw.name;
    tenant.companyUniqueId = raw.companyUniqueId;
    tenant.description = raw.description;
    tenant.status = raw.status;
    tenant.settings = raw.settings;
    tenant.domain = raw.domain;
    tenant.contactEmail = raw.contactEmail;
    tenant.contactPhone = raw.contactPhone;
    tenant.address = raw.address;
    tenant.logoUrl = raw.logoUrl;
    tenant.timezone = raw.timezone;
    tenant.isDeleted = raw.isDeleted;
    tenant.metadata = raw.metadata;
    tenant.preferences = raw.preferences;
    tenant.deletedAt = raw.deletedAt || undefined;
    tenant.createdAt = raw.createdAt;
    tenant.updatedAt = raw.updatedAt;

    return tenant;
  }

  static toPersistence(tenant: Tenant): TenantEntity {
    const entity = new TenantEntity();

    // Only set ID if it exists (for creation vs update)
    if (tenant.id) {
      entity.id = tenant.id;
    }

    entity.name = tenant.name;
    entity.companyUniqueId = tenant.companyUniqueId;
    if (tenant.description) entity.description = tenant.description;
    entity.status = tenant.status;
    entity.settings = tenant.settings;
    if (tenant.domain) entity.domain = tenant.domain;
    entity.contactEmail = tenant.contactEmail;
    if (tenant.contactPhone) entity.contactPhone = tenant.contactPhone;
    if (tenant.address) entity.address = tenant.address;
    if (tenant.logoUrl) entity.logoUrl = tenant.logoUrl;
    entity.timezone = tenant.timezone;
    entity.isDeleted = tenant.isDeleted;
    if (tenant.metadata) entity.metadata = tenant.metadata;
    if (tenant.preferences) entity.preferences = tenant.preferences;
    if (tenant.deletedAt) entity.deletedAt = tenant.deletedAt;
    entity.createdAt = tenant.createdAt;
    entity.updatedAt = tenant.updatedAt;

    return entity;
  }

  static toDomainList(entities: TenantEntity[]): Tenant[] {
    return entities.map(this.toDomain);
  }
}
