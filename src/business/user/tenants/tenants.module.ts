import { Modu<PERSON> } from '@nestjs/common';
import { TenantsService } from './tenants.service';
import { TenantsController } from './tenants.controller';
import { RelationalTenantPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { buildTenantFilterConfig } from '@app/business/user/tenants/tenant-filter.config';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantValidationService } from '@app/business/user/tenants/tenant-validation.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([TenantEntity]),
    RelationalTenantPersistenceModule,
    MonitoringModule,
  ],
  controllers: [TenantsController],
  providers: [
    TenantsService,
    TenantValidationService,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(buildTenantFilterConfig()),
    },
  ],
  exports: [
    TenantsService,
    RelationalTenantPersistenceModule,
    TenantValidationService,
  ],
})
export class TenantsModule {}
