import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { TenantsService } from './tenants.service';
import { CreateTenantDto } from './dto/create-tenant.dto';
import { UpdateTenantDto } from './dto/update-tenant.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { Tenant } from './domain/tenant';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '@utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '@utils/infinity-pagination';
import { FindAllTenantsDto } from './dto/find-all-tenants.dto';
import { ILike } from 'typeorm';
import { ErrorSeverity } from '@core/infrastructure/logger/types/severity.types';
import {
  IMonitoringService,
  MONITORING_SERVICE,
} from '@core/infrastructure/monitoring/monitoring.interface';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';
import {
  IErrorTrackingService,
  ERROR_TRACKING_SERVICE,
} from '@core/infrastructure/errorTracking/error-tracking.interface';
import { ErrorCode } from '@utils/errors/error-codes';
import { LogLevel } from '@core/infrastructure/logger/types/log-level.types';
import { ModuleOperationsBuilder } from '@core/infrastructure/monitoring/builders/module-operation.builder';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';
import { BaseFilterDto } from '@app/core/infrastructure/filtering/dtos/base-filter.dto';

export const TenantFilterExamples = {
  SimpleSearch: {
    where: {
      name: { [FilterOperator.LIKE]: 'LLC' },
    },
    take: 10,
    skip: 0,
  },
  ComplexSearch: {
    where: {
      AND: [
        {
          status: { [FilterOperator.EQ]: true },
          'settings.theme': { [FilterOperator.EQ]: 'dark' },
        },
        {
          OR: [
            { timezone: { [FilterOperator.EQ]: 'UTC' } },
            { timezone: { [FilterOperator.EQ]: 'America/New_York' } },
          ],
        },
      ],
    },
    orderBy: { createdAt: 'DESC' },
    take: 10,
    skip: 0,
  },
  DateAndMetadataSearch: {
    where: {
      createdAt: {
        [FilterOperator.BETWEEN]: [
          '2025-01-01T00:00:00.000Z',
          '2025-12-31T23:59:59.999Z',
        ],
      },
      'metadata.employeeCount': { [FilterOperator.GT]: 500 },
    },
    orderBy: { 'metadata.employeeCount': 'DESC' },
    take: 10,
    skip: 0,
  },
  FullTextSearch: {
    searchTerm: 'LLC',
    searchFields: ['name', 'description', 'companyUniqueId'],
    orderBy: { name: 'ASC' },
    take: 10,
    skip: 0,
  },
};

@ApiTags('Business - User - Tenants')
@ApiBearerAuth()
@Controller({
  path: 'tenants',
  version: '1',
})
export class TenantsController {
  private readonly controllerOps: ModuleOperationsBuilder;

  constructor(
    private readonly tenantsService: TenantsService,
    @Inject(MONITORING_SERVICE) private readonly monitoring: IMonitoringService,
    @Inject(LOGGER_SERVICE) private readonly logger: ILoggerService,
    @Inject(ERROR_TRACKING_SERVICE)
    private readonly errorTracking: IErrorTrackingService,
  ) {
    // Configure controller-wide monitoring defaults
    this.controllerOps = monitoring
      .forModule('tenants-controller')
      .withDefaultTags({
        module: 'tenants',
        layer: 'controller',
        version: '1',
      })
      .withDefaultSeverity(ErrorSeverity.HIGH)
      .withDefaultLogging({
        successLevel: LogLevel.INFO,
        errorLevel: LogLevel.ERROR,
      });

    // Setup error subscriptions for critical tenant operations
    this.setupErrorSubscriptions();
  }

  private setupErrorSubscriptions(): void {
    this.errorTracking.onError(ErrorSeverity.CRITICAL, (error) => {
      this.logger.error('Critical tenant operation failed', error, {
        metadata: {
          tenantId: error.details?.tenantId,
          operation: error.details?.operation,
        },
        tags: ['tenant-critical', 'alert', 'controller'],
      });
    });
  }

  @Post()
  @ApiOperation({ summary: 'Create new tenant' })
  @ApiCreatedResponse({
    type: Tenant,
    description: 'The tenant has been successfully created.',
    schema: {
      example: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Acme Corp',
        companyUniqueId: 'acme-123',
        description: 'Technology company',
        status: true,
        settings: { theme: 'light' },
        domain: 'acme.com',
        contactEmail: '<EMAIL>',
        createdAt: '2024-12-23T13:00:00.000Z',
        updatedAt: '2024-12-23T13:00:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Validation failed or unique constraint violation',
    schema: {
      example: {
        statusCode: 422,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants',
        errorCode: 'ERR2001',
        message: 'Unique constraint violation',
        details: {
          fields: ['companyUniqueId', 'contactEmail'],
          violations: {
            companyUniqueId: 'alreadyExists',
            contactEmail: 'alreadyExists',
          },
        },
      },
    },
  })
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createTenantDto: CreateTenantDto) {
    return this.controllerOps
      .operation<Tenant>()
      .forOperation('CREATE_TENANT_ENDPOINT')
      .withAction('http_post')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({
        'http.method': 'POST',
        'http.path': '/tenants',
        'tenant.name': createTenantDto.name,
        'tenant.company_id': createTenantDto.companyUniqueId,
        'request.source': 'api',
        'business.flow': 'tenant_creation',
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: {
          requestContext: {
            endpoint: '/tenants',
            method: 'POST',
            payload: {
              tenantName: createTenantDto.name,
              companyId: createTenantDto.companyUniqueId,
            },
          },
          businessContext: {
            operation: 'tenant_creation',
            expectedOutcome: 'new_tenant_created',
          },
          errorCode: ErrorCode.TENANT_UNIQUE_CONSTRAINT_VIOLATION,
        },
        tags: {
          operation: 'tenant_creation',
          layer: 'controller',
          endpoint: 'create_tenant',
          api_version: 'v1',
        },
      })
      .withLogging({
        successLevel: LogLevel.INFO,
        errorLevel: LogLevel.ERROR,
        context: {
          request: {
            tenantName: createTenantDto.name,
            companyId: createTenantDto.companyUniqueId,
            endpoint: '/tenants',
            method: 'POST',
          },
          operation: 'tenant_creation',
        },
      })
      .execute(async () => {
        this.logger.debug('Received tenant creation request', {
          metadata: {
            tenantName: createTenantDto.name,
            companyId: createTenantDto.companyUniqueId,
            timestamp: new Date().toISOString(),
          },
          tags: ['tenant-creation', 'request-received'],
        });

        try {
          const tenant = await this.tenantsService.create(createTenantDto);

          this.logger.info('Tenant created successfully', {
            resources: {
              tenantId: tenant.id,
              companyId: tenant.companyUniqueId,
            },
            metadata: {
              tenantName: tenant.name,
              companyId: tenant.companyUniqueId,
              createdAt: tenant.createdAt,
            },
            tags: ['tenant-creation', 'success'],
          });

          return tenant;
        } catch (error) {
          this.logger.error('Tenant creation failed', error, {
            metadata: {
              tenantName: createTenantDto.name,
              companyId: createTenantDto.companyUniqueId,
              errorCode: error.code,
              errorDetails: error.details,
              failurePoint: 'tenant_creation',
            },
            tags: ['tenant-creation', 'failure', error.code],
          });
          throw error;
        }
      });
  }

  @Get()
  @ApiOperation({ summary: 'Get all tenants with pagination' })
  @ApiOkResponse({
    type: InfinityPaginationResponse(Tenant),
    description: 'List of tenants retrieved successfully',
    schema: {
      example: {
        data: [
          {
            id: '123e4567-e89b-12d3-a456-426614174000',
            name: 'Acme Corp',
            companyUniqueId: 'acme-123',
            status: true,
            contactEmail: '<EMAIL>',
            createdAt: '2024-12-23T13:00:00.000Z',
          },
        ],
        hasNextPage: true,
        total: 100,
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number, defaults to 1',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 50), defaults to 10',
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: Boolean,
    description: 'Filter by tenant status',
    example: true,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search tenants by name',
    example: 'acme',
  })
  async findAll(@Query() query: FindAllTenantsDto) {
    return this.controllerOps
      .operation<InfinityPaginationResponseDto<Tenant>>()
      .forOperation('LIST_TENANTS_ENDPOINT')
      .withAction('http_get')
      .withTrace({
        'http.method': 'GET',
        'http.path': '/tenants',
        'query.page': query?.page,
        'query.limit': query?.limit,
        'query.search': query?.search,
      })
      .withLogging({
        successLevel: LogLevel.INFO,
        context: {
          pageSize: query?.limit,
          pageNumber: query?.page,
          searchTerm: query?.search,
        },
      })
      .execute(async () => {
        console.log('here');
        const page = query?.page ?? 1;
        let limit = query?.limit ?? 10;

        if (limit > 50) {
          limit = 50;
          this.logger.warn('Requested page limit exceeds maximum', {
            metadata: {
              requestedLimit: query.limit,
              appliedLimit: limit,
            },
            tags: ['pagination', 'limit-exceeded'],
          });
        }

        const { items, total } =
          await this.tenantsService.findAllWithPagination({
            paginationOptions: {
              page,
              limit,
            },
            where: {
              ...(query.status !== undefined && { status: query.status }),
              ...(query.search && {
                name: ILike(`%${query.search}%`),
              }),
            },
          });

        const result = infinityPagination(items, { page, limit, total });

        this.logger.debug('Tenants list retrieved', {
          metrics: {
            totalItems: total,
            pageSize: limit,
            pageCount: Math.ceil(total / limit),
          },
          tags: ['pagination', 'success'],
        });

        return result;
      });
  }

  @Get('company/:companyUniqueId')
  @ApiOperation({ summary: 'Find tenant by company unique ID' })
  @ApiParam({
    name: 'companyUniqueId',
    type: String,
    required: true,
    description: 'Unique company identifier',
    example: 'acme-123',
  })
  @ApiOkResponse({
    type: Tenant,
    description: 'Tenant found successfully',
    schema: {
      example: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Acme Corp',
        companyUniqueId: 'acme-123',
        status: true,
        contactEmail: '<EMAIL>',
        createdAt: '2024-12-23T13:00:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Tenant not found',
    schema: {
      example: {
        statusCode: 404,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/company/acme-123',
        errorCode: 'ERR2000',
        message: 'Tenant not found with identifier: companyUniqueId:acme-123',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/company/acme-123',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  findByCompanyId(@Param('companyUniqueId') companyUniqueId: string) {
    return this.controllerOps
      .operation<Tenant>()
      .forOperation('GET_TENANT_BY_COMPANY_ENDPOINT')
      .withAction('http_get')
      .withTrace({
        'http.method': 'GET',
        'http.path': '/tenants/company/:companyUniqueId',
        'tenant.company_id': companyUniqueId,
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: { companyUniqueId },
        tags: { operation: 'tenant_lookup', type: 'company_id' },
      })
      .execute(() =>
        this.tenantsService.findByCompanyUniqueId(companyUniqueId),
      );
  }

  @Get('domain/:domain')
  @ApiOperation({ summary: 'Find tenant by domain' })
  @ApiParam({
    name: 'domain',
    type: String,
    required: true,
    description: 'Tenant domain',
    example: 'acme.com',
  })
  @ApiOkResponse({
    type: Tenant,
    description: 'Tenant found successfully',
    schema: {
      example: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Acme Corp',
        domain: 'acme.com',
        status: true,
        contactEmail: '<EMAIL>',
        createdAt: '2024-12-23T13:00:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Tenant not found',
    schema: {
      example: {
        statusCode: 404,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/domain/acme.com',
        errorCode: 'ERR2000',
        message: 'Tenant not found with identifier: domain:acme.com',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/domain/acme.com',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  findByDomain(@Param('domain') domain: string) {
    return this.controllerOps
      .operation<Tenant>()
      .forOperation('GET_TENANT_BY_DOMAIN_ENDPOINT')
      .withAction('http_get')
      .withTrace({
        'http.method': 'GET',
        'http.path': '/tenants/domain/:domain',
        'tenant.domain': domain,
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: { domain },
        tags: { operation: 'tenant_lookup', type: 'domain' },
      })
      .execute(() => this.tenantsService.findByDomain(domain));
  }

  @Get(':id')
  @ApiOperation({ summary: 'Find tenant by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
    description: 'Tenant ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponse({
    type: Tenant,
    description: 'Tenant found successfully',
    schema: {
      example: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Acme Corp',
        companyUniqueId: 'acme-123',
        status: true,
        contactEmail: '<EMAIL>',
        createdAt: '2024-12-23T13:00:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Tenant not found',
    schema: {
      example: {
        statusCode: 404,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000',
        errorCode: 'ERR2000',
        message:
          'Tenant not found with identifier: 123e4567-e89b-12d3-a456-426614174000',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  findById(@Param('id') id: string) {
    return this.controllerOps
      .operation<Tenant>()
      .forOperation('GET_TENANT_BY_ID_ENDPOINT')
      .withAction('http_get')
      .withTrace({
        'http.method': 'GET',
        'http.path': '/tenants/:id',
        'tenant.id': id,
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: { tenantId: id },
        tags: { operation: 'tenant_lookup', type: 'id' },
      })
      .execute(() => this.tenantsService.findById(id));
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update tenant' })
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
    description: 'Tenant ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponse({
    type: Tenant,
    description: 'Tenant has been successfully updated',
    schema: {
      example: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Acme Corp Updated',
        companyUniqueId: 'acme-123',
        status: true,
        contactEmail: '<EMAIL>',
        updatedAt: '2024-12-23T14:00:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Tenant not found',
    schema: {
      example: {
        statusCode: 404,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000',
        errorCode: 'ERR2000',
        message:
          'Tenant not found with identifier: 123e4567-e89b-12d3-a456-426614174000',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Validation failed or unique constraint violation',
    schema: {
      example: {
        statusCode: 422,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000',
        errorCode: 'ERR2001',
        message: 'Unique constraint violation',
        details: {
          fields: ['contactEmail'],
          violations: {
            contactEmail: 'alreadyExists',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  update(@Param('id') id: string, @Body() updateTenantDto: UpdateTenantDto) {
    return this.controllerOps
      .operation<Tenant>()
      .forOperation('UPDATE_TENANT_ENDPOINT')
      .withAction('http_patch')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({
        'http.method': 'PATCH',
        'http.path': '/tenants/:id',
        'tenant.id': id,
        'update.fields': Object.keys(updateTenantDto),
        'email.changed': updateTenantDto.contactEmail ? 'true' : 'false',
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: {
          tenantId: id,
          updatedFields: Object.keys(updateTenantDto),
        },
        tags: { operation: 'tenant_update' },
      })
      .withLogging({
        successLevel: LogLevel.INFO,
        errorLevel: LogLevel.ERROR,
        context: {
          tenantId: id,
          updatedFields: Object.keys(updateTenantDto),
        },
      })
      .execute(async () => {
        this.logger.debug('Processing tenant update request', {
          metadata: {
            tenantId: id,
            updatedFields: Object.keys(updateTenantDto),
          },
          tags: ['tenant-update', 'request-processing'],
        });

        const tenant = await this.tenantsService.update(id, updateTenantDto);

        this.logger.info('Tenant updated successfully', {
          resources: { tenantId: id },
          metadata: {
            updatedFields: Object.keys(updateTenantDto),
          },
          tags: ['tenant-update', 'success'],
        });

        return tenant;
      });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete tenant' })
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
    description: 'Tenant ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Tenant has been successfully deleted',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Tenant not found',
    schema: {
      example: {
        statusCode: 404,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000',
        errorCode: 'ERR2000',
        message:
          'Tenant not found with identifier: 123e4567-e89b-12d3-a456-426614174000',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.controllerOps
      .operation<void>()
      .forOperation('SOFT_DELETE_TENANT_ENDPOINT')
      .withAction('http_delete')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({
        'http.method': 'DELETE',
        'http.path': '/tenants/:id',
        'tenant.id': id,
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: { tenantId: id },
        tags: { operation: 'tenant_deletion', type: 'soft_delete' },
      })
      .withLogging({
        successLevel: LogLevel.WARN,
        errorLevel: LogLevel.ERROR,
        context: { tenantId: id },
      })
      .execute(async () => {
        this.logger.warn('Processing tenant soft delete request', {
          resources: { tenantId: id },
          tags: ['tenant-deletion', 'soft-delete', 'success'],
        });
        await this.tenantsService.softRemove(id);
      });
  }

  @Delete(':id/hard')
  @ApiOperation({ summary: 'Hard delete tenant' })
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
    description: 'Tenant ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Tenant has been permanently deleted',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Tenant not found',
    schema: {
      example: {
        statusCode: 404,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000/hard',
        errorCode: 'ERR2000',
        message:
          'Tenant not found with identifier: 123e4567-e89b-12d3-a456-426614174000',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000/hard',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async hardRemove(@Param('id') id: string) {
    await this.controllerOps
      .operation<void>()
      .forOperation('HARD_DELETE_TENANT_ENDPOINT')
      .withAction('http_delete')
      .withSeverity(ErrorSeverity.CRITICAL)
      .withTrace({
        'http.method': 'DELETE',
        'http.path': '/tenants/:id/hard',
        'tenant.id': id,
        'deletion.type': 'hard',
        'operation.type': 'permanent_deletion',
      })
      .withErrorConfig({
        severity: ErrorSeverity.CRITICAL,
        reportToErrorTracking: true,
        context: {
          tenantId: id,
          operation: 'hard_delete',
          timestamp: new Date().toISOString(),
        },
        tags: {
          operation: 'tenant_deletion',
          type: 'hard_delete',
          criticality: 'critical',
        },
      })
      .withLogging({
        successLevel: LogLevel.WARN,
        errorLevel: LogLevel.ERROR,
        context: {
          tenantId: id,
          operation: 'hard_delete',
        },
      })
      .withTransaction({
        enabled: true,
        timeoutMs: 10000,
      })
      .execute(async () => {
        this.logger.warn('Processing tenant hard delete request', {
          resources: { tenantId: id },
          metadata: {
            initiatedAt: new Date().toISOString(),
            operationType: 'permanent_deletion',
          },
          tags: [
            'tenant-deletion',
            'hard-delete',
            'critical-operation',
            'request-processing',
          ],
        });

        await this.tenantsService.hardRemove(id);

        this.logger.warn('Tenant permanently deleted', {
          resources: { tenantId: id },
          metrics: {
            operationTimestamp: Date.now(),
          },
          tags: [
            'tenant-deletion',
            'hard-delete',
            'critical-operation',
            'success',
          ],
        });

        // Track critical operation in error tracking
        await this.errorTracking.trackError(
          new Error('Critical tenant operation: Hard delete performed'),
          {
            severity: ErrorSeverity.CRITICAL,
            context: {
              tenantId: id,
              operationTimestamp: new Date().toISOString(),
            },
            tags: {
              operation: 'tenant_hard_delete',
              type: 'audit',
            },
          },
        );
      });
  }

  @Post(':id/restore')
  @ApiOperation({ summary: 'Restore soft-deleted tenant' })
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
    description: 'Tenant ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Tenant has been successfully restored',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Tenant not found',
    schema: {
      example: {
        statusCode: 404,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000/restore',
        errorCode: 'ERR2000',
        message:
          'Tenant not found with identifier: 123e4567-e89b-12d3-a456-426614174000',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Tenant is not deleted',
    schema: {
      example: {
        statusCode: 422,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000/restore',
        errorCode: 'ERR2002',
        message:
          'Tenant with id 123e4567-e89b-12d3-a456-426614174000 is not deleted and cannot be restored',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    schema: {
      example: {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/api/v1/tenants/123e4567-e89b-12d3-a456-426614174000/restore',
        errorCode: 'ERR1002',
        message: 'Unauthorized',
      },
    },
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async restore(@Param('id') id: string) {
    await this.controllerOps
      .operation<void>()
      .forOperation('RESTORE_TENANT_ENDPOINT')
      .withAction('http_post')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({
        'http.method': 'POST',
        'http.path': '/tenants/:id/restore',
        'tenant.id': id,
        'operation.type': 'restore',
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: {
          tenantId: id,
          operation: 'restore',
          timestamp: new Date().toISOString(),
        },
        tags: {
          operation: 'tenant_restore',
          type: 'state_change',
        },
      })
      .withLogging({
        successLevel: LogLevel.INFO,
        errorLevel: LogLevel.ERROR,
        context: {
          tenantId: id,
          operation: 'restore',
        },
      })
      .withTransaction({
        enabled: true,
        timeoutMs: 5000,
      })
      .execute(async () => {
        this.logger.debug('Processing tenant restore request', {
          resources: { tenantId: id },
          metadata: {
            initiatedAt: new Date().toISOString(),
            operationType: 'restore',
          },
          tags: ['tenant-restore', 'request-processing'],
        });

        await this.tenantsService.restore(id);

        this.logger.info('Tenant restored successfully', {
          resources: { tenantId: id },
          metrics: {
            operationTimestamp: Date.now(),
          },
          tags: ['tenant-restore', 'success', 'state-change'],
        });
      });
  }

  @Get('advanced/filter')
  @ApiOperation({ summary: 'Get all tenants with advanced filtering' })
  @ApiQuery({
    name: 'where',
    required: false,
    schema: {
      type: 'object',
    },
    examples: {
      'Simple Name Search': {
        value: TenantFilterExamples.SimpleSearch.where,
        description: 'Search tenants by name containing "LLC"',
      },
      'Complex Search': {
        value: TenantFilterExamples.ComplexSearch.where,
        description:
          'Search active tenants with dark theme in specific timezones',
      },
      'Date and Metadata': {
        value: TenantFilterExamples.DateAndMetadataSearch.where,
        description: 'Search by date range and employee count',
      },
    },
  })
  @ApiQuery({
    name: 'orderBy',
    required: false,
    schema: {
      type: 'object',
    },
    examples: {
      'Sort by Name': {
        value: { name: 'ASC' },
      },
      'Sort by Creation Date': {
        value: { createdAt: 'DESC' },
      },
      'Sort by Employee Count': {
        value: { 'metadata.employeeCount': 'DESC' },
      },
    },
  })
  @ApiQuery({
    name: 'searchTerm',
    required: false,
    type: 'string',
    examples: {
      'Company Search': {
        value: 'LLC',
        description: 'Search for companies containing "LLC"',
      },
      'Industry Search': {
        value: 'blockchain',
        description: 'Search for blockchain-related companies',
      },
    },
  })
  @ApiQuery({
    name: 'searchFields',
    required: false,
    type: 'array',
    examples: {
      'Basic Fields': {
        value: ['name', 'description', 'companyUniqueId'],
      },
      'Extended Fields': {
        value: ['name', 'description', 'metadata.industry', 'metadata.tags'],
      },
    },
  })
  async findAllAdvanced(@Query() filter: BaseFilterDto) {
    return this.tenantsService.findAll(filter);
  }
}
