import { Injectable, HttpStatus } from '@nestjs/common';
import { TenantRepository } from '@app/business/user/tenants/infrastructure/persistence/tenant.repository';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { TenantNotFoundException } from '@utils/errors/exceptions/tenant.exceptions';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { Tenant } from '@app/business/user/tenants/domain/tenant';

@Injectable()
export class TenantValidationService {
  constructor(private readonly tenantRepository: TenantRepository) {}

  /**
   * Validates tenant access from the request context and checks if tenant exists in database
   * @param request The request with user and tenant context
   * @returns The validated tenant object
   * @throws AppException or TenantNotFoundException if validation fails
   */
  async validateTenantAccess(request: RequestWithUser): Promise<Tenant> {
    // Check if user has tenant access in context
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    // Check if tenantId exists in context
    if (!request.tenantContext.tenantId) {
      throw new TenantNotFoundException(
        'No tenant specified in request context',
      );
    }

    const tenantId = request.tenantContext.tenantId;

    // Verify tenant exists and is active in database
    const tenant = await this.tenantRepository.findById(tenantId);
    if (!tenant) {
      throw new TenantNotFoundException(tenantId);
    }

    if (!tenant.status || tenant.isDeleted) {
      throw new AppException(
        'Tenant is inactive or deleted',
        ErrorCode.TENANT_INACTIVE,
        HttpStatus.FORBIDDEN,
      );
    }

    return tenant;
  }
}
