import { Inject, Injectable } from '@nestjs/common';
import { CreateTenantDto } from './dto/create-tenant.dto';
import { UpdateTenantDto } from './dto/update-tenant.dto';
import { TenantRepository } from './infrastructure/persistence/tenant.repository';
import {
  IPaginationOptions,
  PaginationResult,
} from '@utils/types/pagination-options';
import { Tenant } from './domain/tenant';
import { UniqueConstraintException } from '@utils/exceptions/unique-constraint.exception';
import {
  TenantAlreadyActiveException,
  TenantNotFoundException,
  TenantUniqueConstraintException,
} from '@app/utils/errors/exceptions/tenant.exceptions';
import { ErrorCode } from '@utils/errors/error-codes';
import { ModuleOperationsBuilder } from '@core/infrastructure/monitoring/builders/module-operation.builder';
import { ErrorSeverity } from '@core/infrastructure/logger/types/severity.types';
import { LogLevel } from '@core/infrastructure/logger/types/log-level.types';
import {
  IMonitoringService,
  MONITORING_SERVICE,
} from '@core/infrastructure/monitoring/monitoring.interface';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { buildTenantFilterConfig } from '@app/business/user/tenants/tenant-filter.config';
import { BaseFilterDto } from '@app/core/infrastructure/filtering/dtos/base-filter.dto';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

/**
 * @class TenantsService
 * @description Service for managing tenant operations with comprehensive error tracking and monitoring
 */
@Injectable()
export class TenantsService {
  private readonly tenantOps: ModuleOperationsBuilder;

  constructor(
    private readonly tenantRepository: TenantRepository,
    private readonly filterService: SecureFilterService,
    @Inject(MONITORING_SERVICE) private readonly monitoring: IMonitoringService,
    @Inject(LOGGER_SERVICE) private readonly logger: ILoggerService,
    @InjectRepository(TenantEntity)
    private readonly tenantQuery: Repository<TenantEntity>,
  ) {
    // Configure module-wide defaults
    this.tenantOps = monitoring
      .forModule('tenants')
      .withDefaultTags({
        module: 'tenants',
        service: 'tenant-service',
      })
      .withDefaultSeverity(ErrorSeverity.MEDIUM)
      .withDefaultLogging({
        successLevel: LogLevel.INFO,
        errorLevel: LogLevel.ERROR,
      });

    this.filterService = new SecureFilterService(buildTenantFilterConfig());
  }

  /**
   * Creates a new tenant with comprehensive validation
   */
  async create(createTenantDto: CreateTenantDto): Promise<Tenant> {
    return this.tenantOps
      .operation<Tenant>()
      .forOperation('CREATE_TENANT')
      .withAction('create')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({
        'tenant.name': createTenantDto.name,
        'tenant.company_id': createTenantDto.companyUniqueId,
        'tenant.email': createTenantDto.contactEmail,
        'operation.type': 'tenant_creation',
        'business.context': 'tenant_management',
      })
      .withLogging({
        successLevel: LogLevel.INFO,
        errorLevel: LogLevel.ERROR,
        context: {
          tenantDetails: {
            name: createTenantDto.name,
            companyId: createTenantDto.companyUniqueId,
            email: createTenantDto.contactEmail,
          },
          operation: 'tenant_creation',
          errorCode: ErrorCode.TENANT_UNIQUE_CONSTRAINT_VIOLATION,
        },
      })
      .withErrorConfig({
        severity: ErrorSeverity.HIGH,
        reportToErrorTracking: true,
        context: {
          operationType: 'CREATE_TENANT',
          validationContext: {
            existingCompanyCheck: true,
            existingEmailCheck: true,
          },
          businessData: {
            tenantName: createTenantDto.name,
            companyId: createTenantDto.companyUniqueId,
            email: createTenantDto.contactEmail,
          },
        },
        tags: {
          operation: 'tenant_creation',
          layer: 'service',
          domain: 'tenant_management',
          criticality: 'high',
        },
      })
      .withTransaction({
        enabled: true,
        timeoutMs: 5000,
      })
      .execute(async () => {
        // Validation phase
        this.logger.debug('Starting tenant validation checks', {
          metadata: {
            checks: ['companyId', 'email'],
            tenantName: createTenantDto.name,
          },
          tags: ['tenant-creation', 'validation'],
        });

        const [existingCompany, existingEmail] = await Promise.all([
          this.tenantRepository.findByCompanyUniqueId(
            createTenantDto.companyUniqueId,
          ),
          this.tenantRepository.findOne({
            where: { contactEmail: createTenantDto.contactEmail },
          }),
        ]);

        const uniqueViolations: string[] = [];
        if (existingCompany) {
          uniqueViolations.push('companyUniqueId');
          this.logger.warn('Duplicate company ID detected', {
            metadata: {
              companyId: createTenantDto.companyUniqueId,
              existingTenantId: existingCompany.id,
            },
            tags: ['tenant-creation', 'validation-failed', 'duplicate-company'],
          });
        }
        if (existingEmail) {
          uniqueViolations.push('contactEmail');
          this.logger.warn('Duplicate email detected', {
            metadata: {
              email: createTenantDto.contactEmail,
              existingTenantId: existingEmail.id,
            },
            tags: ['tenant-creation', 'validation-failed', 'duplicate-email'],
          });
        }

        if (uniqueViolations.length > 0) {
          throw new TenantUniqueConstraintException(uniqueViolations);
        }

        // Creation phase
        this.logger.debug('Starting tenant creation', {
          metadata: {
            tenantName: createTenantDto.name,
            companyId: createTenantDto.companyUniqueId,
          },
          tags: ['tenant-creation', 'persistence'],
        });

        return this.tenantRepository.create({
          name: createTenantDto.name,
          companyUniqueId: createTenantDto.companyUniqueId,
          description: createTenantDto.description,
          status: createTenantDto.status ?? true,
          settings: createTenantDto.settings ?? {},
          domain: createTenantDto.domain,
          contactEmail: createTenantDto.contactEmail,
          contactPhone: createTenantDto.contactPhone,
          address: createTenantDto.address,
          logoUrl: createTenantDto.logoUrl,
          timezone: createTenantDto.timezone,
          metadata: createTenantDto.metadata,
          preferences: createTenantDto.preferences,
        });
      });
  }

  /**
   * Retrieves tenants with pagination and optional filtering
   */
  findAllWithPagination({
    paginationOptions,
    where = {},
  }: {
    paginationOptions: IPaginationOptions;
    where?: Record<string, any>;
  }): Promise<PaginationResult<Tenant>> {
    return this.tenantOps
      .operation<PaginationResult<Tenant>>()
      .forOperation('LIST_TENANTS')
      .withAction('list')
      .useCustomTracer()
      .withTrace({
        'pagination.page': paginationOptions.page,
        'pagination.limit': paginationOptions.limit,
        'filter.count': Object.keys(where).length,
        'filter.criteria': JSON.stringify(where),
      })
      .execute(() =>
        this.tenantRepository.findAllWithPagination({
          paginationOptions,
          where,
        }),
      );
  }

  /**
   * Finds a tenant by ID with error handling
   */
  async findById(id: Tenant['id']) {
    return this.tenantOps
      .operation<Tenant>()
      .forOperation('GET_TENANT')
      .withAction('findById')
      .withTrace({ 'tenant.id': id })
      .execute(async () => {
        const tenant = await this.tenantRepository.findById(id);
        if (!tenant) {
          throw new TenantNotFoundException(id);
        }
        return tenant;
      });
  }

  /**
   * Retrieves multiple tenants by their IDs
   */
  findByIds(ids: Tenant['id'][]) {
    return this.tenantOps
      .operation<Tenant[]>()
      .forOperation('GET_TENANTS_BATCH')
      .withAction('findByIds')
      .useCustomTracer()
      .withTrace({
        'tenant.ids': ids.join(','),
        'batch.size': ids.length,
      })
      .execute(() => this.tenantRepository.findByIds(ids));
  }

  /**
   * Finds a tenant by company unique identifier
   */
  async findByCompanyUniqueId(companyUniqueId: string) {
    return this.tenantOps
      .operation<Tenant>()
      .forOperation('GET_TENANT_BY_COMPANY')
      .withAction('findByCompanyId')
      .withTrace({ 'tenant.company_id': companyUniqueId })
      .execute(async () => {
        const tenant =
          await this.tenantRepository.findByCompanyUniqueId(companyUniqueId);
        if (!tenant) {
          throw new TenantNotFoundException(
            `companyUniqueId:${companyUniqueId}`,
          );
        }
        return tenant;
      });
  }

  /**
   * Finds a tenant by domain name
   */
  async findByDomain(domain: string) {
    return this.tenantOps
      .operation<Tenant>()
      .forOperation('GET_TENANT_BY_DOMAIN')
      .withAction('findByDomain')
      .withTrace({ 'tenant.domain': domain })
      .execute(async () => {
        const tenant = await this.tenantRepository.findByDomain(domain);
        if (!tenant) {
          throw new TenantNotFoundException(`domain:${domain}`);
        }
        return tenant;
      });
  }

  /**
   * Updates tenant information with validation
   */
  async update(id: Tenant['id'], updateTenantDto: UpdateTenantDto) {
    return this.tenantOps
      .operation<Tenant>()
      .forOperation('UPDATE_TENANT')
      .withAction('update')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({
        'tenant.id': id,
        'update.fields': Object.keys(updateTenantDto).join(','),
        'email.changed': updateTenantDto.contactEmail ? 'true' : 'false',
      })
      .withTransaction({ enabled: true })
      .execute(async () => {
        const tenant = await this.findById(id);

        if (
          updateTenantDto.contactEmail &&
          updateTenantDto.contactEmail !== tenant.contactEmail
        ) {
          const existingEmail = await this.tenantRepository.findOne({
            where: { contactEmail: updateTenantDto.contactEmail },
          });

          if (existingEmail) {
            throw new UniqueConstraintException(['contactEmail']);
          }
        }

        return this.tenantRepository.update(id, {
          ...updateTenantDto,
          updatedAt: new Date(),
        });
      });
  }

  /**
   * Performs soft deletion of a tenant
   */
  async softRemove(id: Tenant['id']) {
    return this.tenantOps
      .operation()
      .forOperation('SOFT_DELETE_TENANT')
      .withAction('softRemove')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({ 'tenant.id': id })
      .withTransaction({ enabled: true })
      .execute(async () => {
        await this.findById(id);
        return this.tenantRepository.softRemove(id);
      });
  }

  /**
   * Permanently removes a tenant
   */
  async hardRemove(id: Tenant['id']) {
    return this.tenantOps
      .operation()
      .forOperation('HARD_DELETE_TENANT')
      .withAction('hardRemove')
      .withSeverity(ErrorSeverity.CRITICAL)
      .withTrace({ 'tenant.id': id })
      .withTransaction({ enabled: true })
      .withLogging({
        successLevel: LogLevel.WARN,
        errorLevel: LogLevel.ERROR,
        context: { tenantId: id },
      })
      .execute(async () => {
        await this.findById(id);
        return this.tenantRepository.hardRemove(id);
      });
  }

  /**
   * Restores a soft-deleted tenant
   */
  async restore(id: Tenant['id']) {
    return this.tenantOps
      .operation()
      .forOperation('RESTORE_TENANT')
      .withAction('restore')
      .withSeverity(ErrorSeverity.HIGH)
      .withTrace({
        'tenant.id': id,
        'tenant.was_deleted': 'true',
      })
      .withTransaction({ enabled: true })
      .execute(async () => {
        const tenant = await this.tenantRepository.findById(id);
        if (!tenant?.isDeleted) {
          throw new TenantAlreadyActiveException(id);
        }
        return this.tenantRepository.restore(id);
      });
  }

  async findAll(filter: BaseFilterDto) {
    const queryBuilder = this.tenantQuery.createQueryBuilder('tenant');
    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    return result;
  }
}
