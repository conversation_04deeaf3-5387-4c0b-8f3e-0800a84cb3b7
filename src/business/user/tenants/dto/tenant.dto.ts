import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsObject,
  IsBoolean,
  IsDate,
  IsEmail,
  IsOptional,
} from 'class-validator';

export class TenantDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  companyUniqueId: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsBoolean()
  status: boolean;

  @ApiProperty({
    type: 'object',
    additionalProperties: true,
    examples: {
      Default: {
        value: {
          theme: 'light',
          language: 'en',
          notifications: {
            email: true,
            sms: false,
          },
          defaultCurrency: 'USD',
        },
      },
      CustomSettings: {
        value: {
          theme: 'dark',
          language: 'fr',
          notifications: {
            email: false,
            sms: true,
          },
          defaultCurrency: 'EUR',
        },
      },
    },
  })
  @IsObject()
  settings: Record<string, any>;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty()
  @IsEmail()
  contactEmail: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  contactPhone?: string;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      USAddress: {
        value: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zip: '10001',
          country: 'USA',
        },
      },
      CanadianAddress: {
        value: {
          street: '456 Maple Ave',
          city: 'Toronto',
          province: 'ON',
          postalCode: 'M5V 2H1',
          country: 'Canada',
        },
      },
    },
  })
  @IsObject()
  @IsOptional()
  address?: Record<string, any>;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  logoUrl?: string;

  @ApiProperty()
  @IsString()
  timezone: string;

  @ApiProperty()
  @IsBoolean()
  isDeleted: boolean;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      Basic: {
        value: {
          industry: 'Technology',
          employeeCount: 500,
          foundedYear: 2020,
          tags: ['startup', 'saas'],
        },
      },
      Detailed: {
        value: {
          industry: 'Healthcare',
          employeeCount: 1000,
          foundedYear: 2015,
          tags: ['medical', 'insurance'],
          certifications: ['ISO27001', 'HIPAA'],
          branches: ['New York', 'London'],
        },
      },
    },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      Default: {
        value: {
          dashboardLayout: 'grid',
          reportsFormat: 'PDF',
          emailFrequency: 'daily',
        },
      },
      Advanced: {
        value: {
          dashboardLayout: 'list',
          reportsFormat: 'Excel',
          emailFrequency: 'weekly',
          customReports: ['sales', 'inventory'],
          autoBackup: true,
        },
      },
    },
  })
  @IsObject()
  @IsOptional()
  preferences?: Record<string, any>;

  @ApiPropertyOptional()
  @IsDate()
  @IsOptional()
  deletedAt?: Date;

  @ApiProperty()
  @IsDate()
  createdAt: Date;

  @ApiProperty()
  @IsDate()
  updatedAt: Date;
}
