import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsBoolean,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateTenantDto {
  @ApiProperty({ example: 'Acme Corp' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 'acme-corp-123' })
  @IsString()
  @IsNotEmpty()
  companyUniqueId: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ default: true })
  @IsBoolean()
  @IsOptional()
  status: boolean;

  @ApiProperty({
    type: 'object',
    additionalProperties: true,
    example: {
      Default: {
        value: {
          theme: 'light',
          language: 'en',
          notifications: {
            email: true,
            sms: false,
          },
          defaultCurrency: 'USD',
        },
      },
      CustomSettings: {
        value: {
          theme: 'dark',
          language: 'fr',
          notifications: {
            email: false,
            sms: true,
          },
          defaultCurrency: 'EUR',
        },
      },
    },
  })
  @IsObject()
  @ValidateNested()
  @Type(() => Object)
  settings: Record<string, any>;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  contactEmail: string;

  @ApiPropertyOptional({ example: '+1234567890' })
  @IsString()
  @IsOptional()
  contactPhone?: string;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      USAddress: {
        value: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zip: '10001',
          country: 'USA',
        },
      },
      CanadianAddress: {
        value: {
          street: '456 Maple Ave',
          city: 'Toronto',
          province: 'ON',
          postalCode: 'M5V 2H1',
          country: 'Canada',
        },
      },
    },
  })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  address?: Record<string, any>;

  @ApiPropertyOptional()
  @IsOptional()
  logoUrl?: string;

  @ApiProperty({ example: 'UTC' })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      Basic: {
        value: {
          industry: 'Technology',
          employeeCount: 500,
          foundedYear: 2020,
          tags: ['startup', 'saas'],
        },
      },
      Detailed: {
        value: {
          industry: 'Healthcare',
          employeeCount: 1000,
          foundedYear: 2015,
          tags: ['medical', 'insurance'],
          certifications: ['ISO27001', 'HIPAA'],
          branches: ['New York', 'London'],
        },
      },
    },
  })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      Default: {
        value: {
          dashboardLayout: 'grid',
          reportsFormat: 'PDF',
          emailFrequency: 'daily',
        },
      },
      Advanced: {
        value: {
          dashboardLayout: 'list',
          reportsFormat: 'Excel',
          emailFrequency: 'weekly',
          customReports: ['sales', 'inventory'],
          autoBackup: true,
        },
      },
    },
  })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  preferences?: Record<string, any>;
}
