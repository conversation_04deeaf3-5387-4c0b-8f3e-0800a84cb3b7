import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const buildTenantFilterConfig = () => {
  const fields = [
    // Basic fields
    {
      fieldName: 'name',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
        ],
        searchable: true,
        sortable: true,
        validationMessage: 'Invalid tenant fieldName format',
      },
    },
    {
      fieldName: 'companyUniqueId',
      options: {
        operators: [FilterOperator.EQ, FilterOperator.LIKE],
        searchable: true,
        sortable: true,
        validationMessage: 'Invalid company ID format',
      },
    },
    {
      fieldName: 'contactEmail',
      options: {
        operators: [FilterOperator.EQ, FilterOperator.LIKE],
        searchable: true,
        sortable: true,
        validationMessage: 'Invalid email format',
      },
    },
    {
      fieldName: 'status',
      options: {
        operators: [FilterOperator.EQ, FilterOperator.IN],
        sortable: true,
        validate: (value) => typeof value === 'boolean',
        validationMessage: 'Status must be a boolean',
      },
    },
    // Complex JSONB fields
    {
      fieldName: 'settings.theme',
      options: {
        operators: [FilterOperator.EQ],
        validationMessage: 'Invalid theme value',
      },
    },
    {
      fieldName: 'settings.language',
      options: {
        operators: [FilterOperator.EQ, FilterOperator.IN],
        validationMessage: 'Invalid language value',
      },
    },
    {
      fieldName: 'metadata.industry',
      options: {
        operators: [FilterOperator.EQ, FilterOperator.ILIKE],
        searchable: true,
        validationMessage: 'Invalid industry value',
      },
    },
    {
      fieldName: 'metadata.employeeCount',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.GT,
          FilterOperator.LT,
          FilterOperator.BETWEEN,
        ],
        validate: (value) =>
          typeof value === 'number' && Number.isInteger(value) && value > 0,
        validationMessage: 'Employee count must be a positive integer',
      },
    },
    // Date fields
    {
      fieldName: 'createdAt',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.GT,
          FilterOperator.GTE,
          FilterOperator.LT,
          FilterOperator.LTE,
          FilterOperator.BETWEEN,
        ],
        sortable: true,
        validate: (value) =>
          typeof value === 'string' && !isNaN(Date.parse(value)),
        validationMessage: 'Invalid date format',
      },
    },
  ];

  const tenantConfig = new FilterConfigBuilder();

  // Dynamically add fields
  fields.forEach(({ fieldName, options }) => {
    tenantConfig.addField(fieldName, options);
  });

  return tenantConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(50)
    .setDefaultTake(10)
    .setSearchableFields(['name', 'companyUniqueId', 'contactEmail', 'domain'])
    .setSortableFields([
      'name',
      'companyUniqueId',
      'contactEmail',
      'status',
      'domain',
      'createdAt',
      'updatedAt',
    ])
    .build();
};
