import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class Tenant {
  @ApiProperty({ type: String })
  id: string;

  @ApiProperty({ example: 'Acme Corp' })
  name: string;

  @ApiProperty({ example: 'acme-corp-123' })
  companyUniqueId: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty({ default: true })
  status: boolean;

  @ApiProperty({
    type: 'object',
    additionalProperties: true,
    examples: {
      Default: {
        value: {
          theme: 'light',
          language: 'en',
          notifications: {
            email: true,
            sms: false,
          },
          defaultCurrency: 'USD',
        },
      },
      CustomSettings: {
        value: {
          theme: 'dark',
          language: 'fr',
          notifications: {
            email: false,
            sms: true,
          },
          defaultCurrency: 'EUR',
        },
      },
    },
  })
  settings: Record<string, any>;

  @ApiPropertyOptional()
  domain?: string;

  @ApiProperty({ example: '<EMAIL>' })
  contactEmail: string;

  @ApiPropertyOptional({ example: '+1234567890' })
  contactPhone?: string;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      USAddress: {
        value: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zip: '10001',
          country: 'USA',
        },
      },
      CanadianAddress: {
        value: {
          street: '456 Maple Ave',
          city: 'Toronto',
          province: 'ON',
          postalCode: 'M5V 2H1',
          country: 'Canada',
        },
      },
    },
  })
  address?: Record<string, any>;

  @ApiPropertyOptional()
  logoUrl?: string;

  @ApiProperty({ example: 'UTC' })
  timezone: string;

  @ApiProperty({ default: false })
  isDeleted: boolean;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      Basic: {
        value: {
          industry: 'Technology',
          employeeCount: 500,
          foundedYear: 2020,
          tags: ['startup', 'saas'],
        },
      },
      Detailed: {
        value: {
          industry: 'Healthcare',
          employeeCount: 1000,
          foundedYear: 2015,
          tags: ['medical', 'insurance'],
          certifications: ['ISO27001', 'HIPAA'],
          branches: ['New York', 'London'],
        },
      },
    },
  })
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    examples: {
      Default: {
        value: {
          dashboardLayout: 'grid',
          reportsFormat: 'PDF',
          emailFrequency: 'daily',
        },
      },
      Advanced: {
        value: {
          dashboardLayout: 'list',
          reportsFormat: 'Excel',
          emailFrequency: 'weekly',
          customReports: ['sales', 'inventory'],
          autoBackup: true,
        },
      },
    },
  })
  preferences?: Record<string, any>;

  @ApiPropertyOptional()
  deletedAt?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
