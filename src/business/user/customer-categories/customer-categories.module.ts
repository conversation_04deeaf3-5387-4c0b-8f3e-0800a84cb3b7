import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerCategoriesController } from './customer-categories.controller';
import { CustomerCategoriesService } from './customer-categories.service';
import { CustomerCategoryEntity } from './infrastructure/persistence/relational/entities/customer-category.entity';
import { CustomerCategoryRepository } from './infrastructure/persistence/customer-category.repository';
import { CustomerCategoryRelationalRepository } from './infrastructure/persistence/relational/repositories/customer-category.repository';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { UsersModule } from '../users/users.module';
import { QueryService } from '@utils/query-creator/query.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([CustomerCategoryEntity, UserEntity]),
    forwardRef(() => UsersModule),
  ],
  controllers: [CustomerCategoriesController],
  providers: [
    CustomerCategoriesService,
    {
      provide: CustomerCategoryRepository,
      useClass: CustomerCategoryRelationalRepository,
    },
    QueryService,
  ],
  exports: [CustomerCategoriesService, CustomerCategoryRepository],
})
export class CustomerCategoriesModule {}
