import { BadRequestException, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { CustomerCategoryRepository } from './infrastructure/persistence/customer-category.repository';
import { CategoryType, CustomerCategory } from './domain/customer-category';
import { CreateCategoryDto } from './dto/create-category.dto';
import { CategorySelectionDto } from './dto/category-selection.dto';
import {
  CategoryAlreadyExistsException,
  CategoryNotFoundException,
  CategoryOperationNotAllowedException,
} from '@utils/errors/exceptions/category-exceptions';
import { UserNotFoundException } from '@utils/errors/exceptions/user.exceptions';
import { QueryService } from '@utils/query-creator/query.service';
import { ListCategoryResponseDto } from '@app/business/user/customer-categories/dto/category-response.dto';

@Injectable()
export class CustomerCategoriesService {
  constructor(
    private readonly categoryRepository: CustomerCategoryRepository,
    private readonly queryService: QueryService,
    private readonly dataSource: DataSource,
  ) {}

  async create(
    tenantId: string,
    dto: CreateCategoryDto,
  ): Promise<CustomerCategory> {
    const existing = await this.categoryRepository.findByName(
      tenantId,
      dto.name,
    );

    if (existing) {
      throw new CategoryAlreadyExistsException(dto.name, tenantId);
    }

    return this.categoryRepository.create(tenantId, dto.name);
  }

  async createDepartment(
    tenantId: string,
    dto: CreateCategoryDto,
  ): Promise<CustomerCategory> {
    const existing = await this.categoryRepository.findByName(
      tenantId,
      dto.name,
    );
    if (existing) {
      throw new CategoryAlreadyExistsException(dto.name, tenantId);
    }

    // Create a category with type CONTACT
    const department = await this.categoryRepository.create(
      tenantId,
      dto.name,
      CategoryType.CONTACT,
    );

    return department;
  }

  async findAll(
    tenantId: string,
    type: CategoryType,
  ): Promise<ListCategoryResponseDto[]> {
    const category = await this.categoryRepository.findAll(tenantId, type);

    return category;
  }

  async findById(id: string): Promise<CustomerCategory> {
    const category = await this.categoryRepository.findById(id);

    if (!category) {
      throw new CategoryNotFoundException(id);
    }

    return category;
  }

  async findByIds(ids: string[]): Promise<CustomerCategory[]> {
    if (!ids || !ids.length) {
      return [];
    }

    return this.categoryRepository.findByIds(ids);
  }

  async update(
    id: string,
    tenantId: string,
    name: string,
  ): Promise<CustomerCategory> {
    const existing = await this.categoryRepository.findById(id);

    if (!existing) {
      throw new CategoryNotFoundException(id);
    }

    if (existing.tenantId !== tenantId) {
      throw new CategoryOperationNotAllowedException(
        'update',
        'Category does not belong to this tenant',
      );
    }

    if (name !== existing.name) {
      const duplicate = await this.categoryRepository.findByName(
        tenantId,
        name,
      );

      if (duplicate && duplicate.id !== id) {
        throw new CategoryAlreadyExistsException(name, tenantId);
      }
    }

    return this.categoryRepository.update(id, name);
  }

  async delete(id: string, tenantId: string): Promise<void> {
    const existing = await this.categoryRepository.findById(id);

    if (!existing) {
      throw new CategoryNotFoundException(id);
    }

    if (existing.tenantId !== tenantId) {
      throw new CategoryOperationNotAllowedException(
        'delete',
        'Category does not belong to this tenant',
      );
    }

    try {
      await this.categoryRepository.delete(id);
    } catch {
      throw new CategoryOperationNotAllowedException(
        'delete',
        'Cannot delete this category because it is in use',
      );
    }
  }

  async assignToUser(userId: string, categoryIds: string[]): Promise<void> {
    if (!userId) {
      throw new UserNotFoundException('No user ID provided');
    }
    if (!categoryIds || !categoryIds.length) {
      return;
    }

    try {
      // Verify all categories exist
      const categories = await this.categoryRepository.findByIds(categoryIds);

      if (categories.length !== categoryIds.length) {
        const foundIds = categories.map((c) => c.id);
        const missingIds = categoryIds.filter((id) => !foundIds.includes(id));
        throw new CategoryNotFoundException(missingIds[0]);
      }

      await this.categoryRepository.assignToUser(userId, categoryIds);
    } catch (error) {
      if (error instanceof CategoryNotFoundException) {
        throw error;
      }

      throw new UserNotFoundException(userId);
    }
  }

  async removeFromUser(userId: string, categoryIds: string[]): Promise<void> {
    if (!userId) {
      throw new UserNotFoundException('No user ID provided');
    }

    if (!categoryIds || !categoryIds.length) {
      return;
    }

    try {
      await this.categoryRepository.removeFromUser(userId, categoryIds);
    } catch {
      throw new UserNotFoundException(userId);
    }
  }

  async processCategories(
    tenantId: string,
    categories: CategorySelectionDto[],
    forContact: boolean = false,
  ): Promise<string[]> {
    if (!categories || !categories.length) {
      return [];
    }

    const categoryIds: string[] = [];
    const existingCategories = categories.filter((c) => c.id);

    if (existingCategories.length) {
      const ids = existingCategories
        .map((c) => c.id)
        .filter((id) => id !== undefined) as string[];

      const found = await this.categoryRepository.findByIds(ids);
      for (const category of found) {
        if (forContact) {
          if (category.type !== CategoryType.CONTACT) {
            throw new BadRequestException(
              'The provided category is not a valid contact department',
            );
          }
        } else {
          if (category.tenantId !== tenantId) {
            throw new CategoryOperationNotAllowedException(
              'process',
              'Category does not belong to this tenant',
            );
          }
        }

        if (category.id) {
          categoryIds.push(category.id);
        }
      }
    }

    const newCategories = categories.filter((c) => !c.id);
    for (const newCategory of newCategories) {
      let existing: CustomerCategory | null;
      if (forContact) {
        existing = await this.categoryRepository.findByNameAndType(
          tenantId,
          newCategory.name,
          CategoryType.CONTACT,
        );
      } else {
        existing = await this.categoryRepository.findByName(
          tenantId,
          newCategory.name,
        );
      }
      if (existing) {
        if (existing.id) {
          categoryIds.push(existing.id);
        }
      } else {
        let created: CustomerCategory;
        if (forContact) {
          created = await this.categoryRepository.create(
            tenantId,
            newCategory.name,
            CategoryType.CONTACT,
          );
        } else {
          created = await this.categoryRepository.create(
            tenantId,
            newCategory.name,
          );
        }

        if (created.id) {
          categoryIds.push(created.id);
        }
      }
    }

    return categoryIds;
  }
}
