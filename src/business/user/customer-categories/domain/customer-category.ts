import { ApiProperty } from '@nestjs/swagger';

export enum CategoryType {
  CUSTOMER = 'CUSTOMER',
  CONTACT = 'CONTACT',
}

export class CustomerCategory {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id?: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655441111' })
  tenantId: string;

  @ApiProperty({ example: 'Retail' })
  name: string;

  @ApiProperty({ enum: CategoryType, example: CategoryType.CUSTOMER })
  type: CategoryType;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
