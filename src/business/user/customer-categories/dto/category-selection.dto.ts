import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class CategorySelectionDto {
  @ApiPropertyOptional({
    description: 'Category ID (empty for new categories)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4)
  id?: string;

  @ApiProperty({
    description: 'Category name',
    example: 'Retail',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}
