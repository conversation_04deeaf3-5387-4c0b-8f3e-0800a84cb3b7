import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  Put,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiCreatedResponse,
} from '@nestjs/swagger';
import { CustomerCategoriesService } from './customer-categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import {
  CategoryResponseDto,
  ListCategoryResponseDto,
} from './dto/category-response.dto';
import { AssignCategoriesDto } from './dto/assign-categories.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { TenantNotFoundException } from '@utils/errors/exceptions/tenant.exceptions';
import { CategoryType } from './domain/customer-category';
@ApiTags('Business - User - Customer Categories')
@ApiBearerAuth()
@Controller({
  path: 'customer-categories',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class CustomerCategoriesController {
  constructor(private readonly categoriesService: CustomerCategoriesService) {}

  private validateTenantAccess(request: RequestWithUser): string {
    if (!request.tenantContext?.hasTenantAccess) {
      throw new AppException(
        'Insufficient tenant access permissions',
        ErrorCode.UNAUTHORIZED,
        HttpStatus.FORBIDDEN,
      );
    }

    if (!request.tenantContext?.tenantId) {
      throw new TenantNotFoundException(
        'No tenant specified in request context',
      );
    }

    return request.tenantContext.tenantId;
  }

  @Post()
  @ApiOperation({ summary: 'Create a new customer category' })
  @ApiCreatedResponse({
    description: 'Category created successfully',
    type: CategoryResponseDto,
    schema: {
      example: {
        id: '550e8400-e29b-41d4-a716-446655440000',
        tenantId: '550e8400-e29b-41d4-a716-446655441111',
        name: 'Premium',
        createdAt: '2023-01-15T08:30:00.000Z',
        updatedAt: '2023-01-15T08:30:00.000Z',
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiConflictResponse({
    description: 'Category with this name already exists',
  })
  @ApiBody({
    type: CreateCategoryDto,
    description: 'Category data',
    examples: {
      'Basic Category': {
        value: {
          name: 'Premium',
        },
      },
    },
  })
  async create(
    @Req() request: RequestWithUser,
    @Body() createCategoryDto: CreateCategoryDto,
  ): Promise<CategoryResponseDto> {
    const tenantId = this.validateTenantAccess(request);
    return this.categoriesService.create(tenantId, createCategoryDto);
  }

  @Get('/type/:type')
  @ApiOperation({ summary: 'Get all customer categories for current admin' })
  @ApiForbiddenResponse({
    description: 'Insufficient admin access permissions',
  })
  @ApiParam({
    name: 'type',
    required: true,
    description: 'Type or Category contact or customer',
    example: 'customer',
  })
  async findAll(
    @Req() request: RequestWithUser,
  ): Promise<ListCategoryResponseDto[]> {
    const params = request.params;
    const type = params.type as CategoryType;
    const tenantId = this.validateTenantAccess(request);
    return this.categoriesService.findAll(tenantId, type);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get customer category by ID' })
  @ApiOkResponse({
    description: 'Category retrieved successfully',
    type: CategoryResponseDto,
    schema: {
      example: {
        id: '550e8400-e29b-41d4-a716-446655440000',
        tenantId: '550e8400-e29b-41d4-a716-446655441111',
        name: 'Premium',
        createdAt: '2023-01-15T08:30:00.000Z',
        updatedAt: '2023-01-15T08:30:00.000Z',
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Category not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Category ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findOne(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<CategoryResponseDto> {
    this.validateTenantAccess(request);
    return this.categoriesService.findById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update customer category' })
  @ApiOkResponse({
    description: 'Category updated successfully',
    type: CategoryResponseDto,
    schema: {
      example: {
        id: '550e8400-e29b-41d4-a716-446655440000',
        tenantId: '550e8400-e29b-41d4-a716-446655441111',
        name: 'Premium Plus',
        createdAt: '2023-01-15T08:30:00.000Z',
        updatedAt: '2023-02-10T16:45:00.000Z',
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Category not found' })
  @ApiForbiddenResponse({
    description:
      'Insufficient permissions or category belongs to different tenant',
  })
  @ApiConflictResponse({
    description: 'Category with this name already exists',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Category ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    type: CreateCategoryDto,
    description: 'Updated category data',
    examples: {
      'Update Name': {
        value: {
          name: 'Premium Plus',
        },
      },
    },
  })
  async update(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() updateCategoryDto: CreateCategoryDto,
  ): Promise<CategoryResponseDto> {
    const tenantId = this.validateTenantAccess(request);
    return this.categoriesService.update(id, tenantId, updateCategoryDto.name);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete customer category' })
  @ApiNoContentResponse({ description: 'Category deleted successfully' })
  @ApiNotFoundResponse({ description: 'Category not found' })
  @ApiForbiddenResponse({
    description:
      'Insufficient permissions or category belongs to different tenant',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Category ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async remove(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    const tenantId = this.validateTenantAccess(request);
    await this.categoriesService.delete(id, tenantId);
  }

  @Post('user/:userId/assign')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Assign categories to a user' })
  @ApiNoContentResponse({ description: 'Categories assigned successfully' })
  @ApiNotFoundResponse({ description: 'User or category not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'userId',
    required: true,
    description: 'User ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655443333',
  })
  @ApiBody({
    type: AssignCategoriesDto,
    description: 'Categories to assign',
    examples: {
      'Assign Categories': {
        value: {
          categoryIds: [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655442222',
          ],
        },
      },
    },
  })
  async assignToUser(
    @Req() request: RequestWithUser,
    @Param('userId') userId: string,
    @Body() assignDto: AssignCategoriesDto,
  ): Promise<void> {
    this.validateTenantAccess(request);
    await this.categoriesService.assignToUser(userId, assignDto.categoryIds);
  }

  @Post('user/:userId/remove')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remove categories from a user' })
  @ApiNoContentResponse({ description: 'Categories removed successfully' })
  @ApiNotFoundResponse({ description: 'User or category not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'userId',
    required: true,
    description: 'User ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655443333',
  })
  @ApiBody({
    type: AssignCategoriesDto,
    description: 'Categories to remove',
    examples: {
      'Remove Categories': {
        value: {
          categoryIds: ['550e8400-e29b-41d4-a716-446655440000'],
        },
      },
    },
  })
  async removeFromUser(
    @Req() request: RequestWithUser,
    @Param('userId') userId: string,
    @Body() assignDto: AssignCategoriesDto,
  ): Promise<void> {
    this.validateTenantAccess(request);
    await this.categoriesService.removeFromUser(userId, assignDto.categoryIds);
  }
}
