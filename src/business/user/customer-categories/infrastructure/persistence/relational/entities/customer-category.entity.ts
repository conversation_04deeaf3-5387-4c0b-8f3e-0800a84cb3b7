import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  Index,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { CategoryType } from '@app/business/user/customer-categories/domain/customer-category';

@Entity({
  name: 'customer_categories',
})
export class CustomerCategoryEntity extends EntityRelationalHelper {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id', type: 'uuid' })
  @Index()
  tenantId: string;

  @ManyToOne(() => TenantEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tenant_id' })
  tenant: TenantEntity;

  @Column({ length: 100 })
  name: string;

  @ManyToMany(() => UserEntity, (user) => user.categories)
  users: UserEntity[];

  @Column({
    type: 'enum',
    enum: CategoryType,
    default: CategoryType.CUSTOMER,
  })
  type: CategoryType;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;
}
