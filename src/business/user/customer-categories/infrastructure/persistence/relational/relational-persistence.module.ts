import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerCategoryEntity } from './entities/customer-category.entity';
import { CustomerCategoryRepository } from '../customer-category.repository';
import { CustomerCategoryRelationalRepository } from './repositories/customer-category.repository';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CustomerCategoryEntity, UserEntity])],
  providers: [
    {
      provide: CustomerCategoryRepository,
      useClass: CustomerCategoryRelationalRepository,
    },
  ],
  exports: [CustomerCategoryRepository],
})
export class RelationalCustomerCategoryPersistenceModule {}
