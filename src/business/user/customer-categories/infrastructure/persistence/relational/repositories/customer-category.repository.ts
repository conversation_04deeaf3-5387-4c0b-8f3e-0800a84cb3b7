import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CustomerCategoryEntity } from '../entities/customer-category.entity';
import {
  CategoryType,
  CustomerCategory,
} from '../../../../domain/customer-category';
import { CustomerCategoryRepository } from '../../customer-category.repository';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { ListCategoryResponseDto } from '../../../../dto/category-response.dto';

@Injectable()
export class CustomerCategoryRelationalRepository
  implements CustomerCategoryRepository
{
  constructor(
    @InjectRepository(CustomerCategoryEntity)
    private readonly repository: Repository<CustomerCategoryEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  private mapToDomain(entity: CustomerCategoryEntity): CustomerCategory {
    const domain = new CustomerCategory();
    domain.id = entity.id;
    domain.tenantId = entity.tenantId;
    domain.name = entity.name;
    domain.type = entity.type;
    domain.createdAt = entity.createdAt;
    domain.updatedAt = entity.updatedAt;
    return domain;
  }

  private mapToList(entity: CustomerCategoryEntity): CustomerCategory {
    const domain = new CustomerCategory();
    domain.id = entity.id;
    domain.name = entity.name;
    return domain;
  }

  async create(
    tenantId: string,
    name: string,
    type: CategoryType = CategoryType.CUSTOMER,
  ): Promise<CustomerCategory> {
    const category = this.repository.create({
      tenantId,
      name,
      type,
    });

    const savedCategory = await this.repository.save(category);
    return this.mapToDomain(savedCategory);
  }

  async findAll(
    tenantId: string,
    type: CategoryType,
  ): Promise<ListCategoryResponseDto[]> {
    const categories = await this.repository.find({
      where: { tenantId, type },
      order: { createdAt: 'ASC' },
      select: {
        id: true,
        name: true,
      },
    });

    const categoriesList = categories.map((category) =>
      this.mapToDomain(category),
    ) as ListCategoryResponseDto[];

    return categoriesList;
  }

  async findById(id: string): Promise<CustomerCategory | null> {
    const category = await this.repository.findOne({
      where: { id },
    });

    return category ? this.mapToDomain(category) : null;
  }

  async findByName(
    tenantId: string,
    name: string,
  ): Promise<CustomerCategory | null> {
    const category = await this.repository.findOne({
      where: { tenantId, name },
    });

    return category ? this.mapToDomain(category) : null;
  }

  async findByNameAndType(
    tenantId: string,
    name: string,
    type: CategoryType,
  ): Promise<CustomerCategory | null> {
    const category = await this.repository.findOne({
      where: { tenantId, name, type },
    });

    return category ? this.mapToDomain(category) : null;
  }

  async findByIds(ids: string[]): Promise<CustomerCategory[]> {
    if (!ids.length) return [];

    const categories = await this.repository.find({
      where: { id: In(ids) },
    });

    return categories.map((category) => this.mapToDomain(category));
  }

  async assignToUser(userId: string, categoryIds: string[]): Promise<void> {
    if (!categoryIds.length) return;

    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['categories'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const categories = await this.repository.find({
      where: { id: In(categoryIds) },
    });

    // Add only the categories that don't already exist
    const existingCategoryIds = user.categories?.map((c) => c.id) || [];
    const categoriesToAdd = categories.filter(
      (c) => !existingCategoryIds.includes(c.id),
    );

    if (categoriesToAdd.length) {
      await this.repository
        .createQueryBuilder()
        .relation(CustomerCategoryEntity, 'users')
        .of(categoriesToAdd)
        .add(user);
    }
  }

  async removeFromUser(userId: string, categoryIds: string[]): Promise<void> {
    if (!categoryIds.length) return;

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    await this.repository
      .createQueryBuilder()
      .relation(CustomerCategoryEntity, 'users')
      .of(categoryIds)
      .remove(user);
  }

  async update(id: string, name: string): Promise<CustomerCategory> {
    const category = await this.repository.findOne({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    category.name = name;
    const updatedCategory = await this.repository.save(category);
    return this.mapToDomain(updatedCategory);
  }

  async delete(id: string): Promise<void> {
    const result = await this.repository.delete(id);

    if (!result.affected) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
  }
}
