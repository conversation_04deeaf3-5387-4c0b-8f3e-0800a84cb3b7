import { CustomerCategory, CategoryType } from '../../domain/customer-category';
import { ListCategoryResponseDto } from '../../dto/category-response.dto';

export abstract class CustomerCategoryRepository {
  abstract create(
    tenantId: string,
    name: string,
    type?: CategoryType,
  ): Promise<CustomerCategory>;
  abstract findAll(
    tenantId: string,
    type: CategoryType,
  ): Promise<ListCategoryResponseDto[]>;
  abstract findById(id: string): Promise<CustomerCategory | null>;
  abstract findByName(
    tenantId: string,
    name: string,
  ): Promise<CustomerCategory | null>;
  abstract findByNameAndType(
    tenantId: string,
    name: string,
    type: CategoryType,
  ): Promise<CustomerCategory | null>;
  abstract findByIds(ids: string[]): Promise<CustomerCategory[]>;
  abstract assignToUser(userId: string, categoryIds: string[]): Promise<void>;
  abstract removeFromUser(userId: string, categoryIds: string[]): Promise<void>;
  abstract update(id: string, name: string): Promise<CustomerCategory>;
  abstract delete(id: string): Promise<void>;
}
