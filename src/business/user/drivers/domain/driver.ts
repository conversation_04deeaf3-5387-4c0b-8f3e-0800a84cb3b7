import { AutoMap } from '@automapper/classes';
import { EmploymentDto } from '../dto/create-driver.dto';
import { UserStatus, UserType } from '../../users/domain/user.types';
import { DriverStatus } from '../../../mobile/auth/domain/driver';

export class DriverDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  name: string;

  @AutoMap()
  email: string;

  @AutoMap()
  password: string;

  @AutoMap()
  status: UserStatus;

  @AutoMap()
  driverStatus: DriverStatus;

  @AutoMap()
  phoneCountryCode: string;

  @AutoMap()
  phoneNumber: string;

  @AutoMap()
  phoneExtension: string;

  @AutoMap()
  timeZone: string;

  @AutoMap()
  addressLine1: string;

  @AutoMap()
  addressLine2: string;

  @AutoMap()
  city: string;

  @AutoMap()
  province: string;

  @AutoMap()
  postalCode: string;

  @AutoMap()
  country: string;

  @AutoMap()
  userType: UserType;

  @AutoMap()
  employment: EmploymentDto;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}
