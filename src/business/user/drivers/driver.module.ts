import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { DriverController } from './driver.controller';
import { DriverService } from './driver.service';
import { TenantsModule } from '../tenants/tenants.module';
import { DriverProfile } from './infrastructure/mapper/driver.profile';
import { RelationalDriverPersistenceModule } from './infrastructure/relational-persistence.module';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { DriverFilterConfig } from './driver-filter.config';

@Module({
  imports: [
    RelationalDriverPersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    TenantsModule,
  ],
  controllers: [DriverController],
  providers: [
    DriverService,
    DriverProfile,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(DriverFilterConfig()),
    },
  ],
  exports: [DriverService],
})
export class DriverModule {}
