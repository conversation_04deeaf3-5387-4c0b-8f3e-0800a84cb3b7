import { FilterConfigBuilder } from '@core/infrastructure/filtering/config/filter-config';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';

export const DriverFilterConfig = () => {
  const fields = [
    {
      fieldName: 'companyName',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid company name format',
      },
    },
    {
      fieldName: 'contactName',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid contact name format',
      },
    },
    {
      fieldName: 'email',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid email format',
      },
    },
    {
      fieldName: 'phoneNumber',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid phone number format',
      },
    },
    {
      fieldName: 'addressLine1',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid address line 1 format',
      },
    },
    {
      fieldName: 'addressLine2',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid address line 2 format',
      },
    },
    {
      fieldName: 'city',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid city format',
      },
    },
    {
      fieldName: 'province',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid province format',
      },
    },
    {
      fieldName: 'country',
      options: {
        operators: [
          FilterOperator.EQ,
          FilterOperator.NEQ,
          FilterOperator.LIKE,
          FilterOperator.ILIKE,
          FilterOperator.STARTS_WITH,
          FilterOperator.ENDS_WITH,
          FilterOperator.CONTAINS,
          FilterOperator.NOT_CONTAINS,
          FilterOperator.IN,
          FilterOperator.NOT_IN,
        ],
        validationMessage: 'Invalid country format',
      },
    },
  ];

  const vehicleConfig = new FilterConfigBuilder();

  // Add fields dynamically
  fields.forEach(({ fieldName, options }) => {
    vehicleConfig.addField(fieldName, options);
  });

  return vehicleConfig
    .setDefaultSort('createdAt', 'DESC')
    .setMaxTake(100)
    .setDefaultTake(10)
    .setSearchableFields([
      'companyName',
      'contactName',
      'email',
      'phoneNumber',
      'addressLine1',
      'addressLine2',
      'city',
      'province',
      'country',
    ])
    .setSortableFields([
      'companyName',
      'contactName',
      'email',
      'phoneNumber',
      'addressLine1',
      'addressLine2',
      'city',
      'province',
      'country',
      'createdAt',
    ])
    .build();
};
