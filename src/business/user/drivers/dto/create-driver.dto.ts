import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsDateString,
  IsEnum,
  IsArray,
  IsNumber,
} from 'class-validator';
import { EmploymentType } from '../domain/driver.type';

export class EmploymentDto {
  @ApiProperty({ example: '2024-10-01' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ enum: EmploymentType })
  @IsEnum(EmploymentType)
  type: EmploymentType;

  @ApiProperty({ type: [String] })
  @IsOptional()
  @IsArray()
  documents: string[];

  @ApiProperty({ example: 0 })
  @IsOptional()
  @IsNumber()
  dispatcherHourlyRate: number;

  @ApiProperty({ example: 0 })
  @IsOptional()
  @IsNumber()
  dispatcherPerOrderRate: number;

  @ApiProperty({ example: 0 })
  @IsOptional()
  @IsNumber()
  driverCommissionRate: number;

  @ApiProperty({ example: 0 })
  @IsOptional()
  @IsNumber()
  driverDistanceRate: number;

  @ApiProperty({ example: 0 })
  @IsOptional()
  @IsNumber()
  driverHourlyRate: number;

  @ApiProperty({ example: 0 })
  @IsOptional()
  @IsNumber()
  driverPerDeliveryRates: number;
}

export class CreateDriverDto {
  @AutoMap()
  @ApiProperty({ example: 'John Doe' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @AutoMap()
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @AutoMap()
  @ApiProperty({ example: '+1' })
  @IsString()
  @IsNotEmpty()
  phoneCountryCode: string;

  @AutoMap()
  @ApiProperty({ example: '2345678901' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @AutoMap()
  @ApiProperty({ example: '123' })
  @IsString()
  @IsOptional()
  phoneExtension: string;

  @AutoMap()
  @ApiProperty({ example: 'Asia/Kolkata' })
  @IsString()
  @IsOptional()
  timeZone: string;

  @AutoMap()
  @ApiProperty({ example: '123 Main St' })
  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @AutoMap()
  @ApiProperty({ example: 'Suite 100' })
  @IsString()
  @IsOptional()
  addressLine2: string;

  @AutoMap()
  @ApiProperty({ example: 'Montreal' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @AutoMap()
  @ApiProperty({ example: 'Quebec' })
  @IsString()
  @IsNotEmpty()
  province: string;

  @AutoMap()
  @ApiProperty({ example: 'H3Z 2Y7' })
  @IsString()
  @IsNotEmpty()
  postalCode: string;

  @AutoMap()
  @ApiProperty({ example: 'Canada' })
  @IsString()
  @IsNotEmpty()
  country: string;

  @AutoMap()
  @ApiProperty({ type: EmploymentDto })
  @IsOptional()
  @Type(() => EmploymentDto)
  employment: EmploymentDto;
}
