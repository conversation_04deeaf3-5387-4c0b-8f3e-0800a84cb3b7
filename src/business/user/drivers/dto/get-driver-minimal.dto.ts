import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class GetDriverMinimalDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: '<PERSON> Doe' })
  name: string;
}

export class GetAllDriverMinimalDto {
  @ApiProperty({ type: [GetDriverMinimalDto] })
  data: GetDriverMinimalDto[];
}
