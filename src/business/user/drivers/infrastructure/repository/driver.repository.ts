import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, Repository } from 'typeorm';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { UserEntity } from '../../../users/infrastructure/entities/user.entity';
import { DriverDomain } from '../../domain/driver';
import { UserType } from '../../../users/domain/user.types';
import { DriverFilterConfig } from '../../driver-filter.config';
import { hash } from 'bcrypt';

@Injectable()
export class DriverRepository {
  constructor(
    @InjectRepository(UserEntity)
    private readonly driverRepository: Repository<UserEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(DriverFilterConfig());
  }

  async create(data: DriverDomain): Promise<DriverDomain> {
    const requestEntity = this.mapper.map(data, DriverDomain, UserEntity);
    const driverEntity = await this.driverRepository.save(
      this.driverRepository.create({
        ...requestEntity,
        userType: UserType.Driver,
        password: await hash('randomPassword', 10),
      }),
    );
    const responseDomain = this.mapper.map(
      driverEntity,
      UserEntity,
      DriverDomain,
    );
    return responseDomain;
  }

  async find(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<DriverDomain>> {
    const queryBuilder = this.driverRepository
      .createQueryBuilder('driver')
      .where('driver.isDeleted = false')
      .andWhere('driver.userType = :userType', {
        userType: UserType.Driver,
      })
      .andWhere('driver.tenantId = :tenantId', {
        tenantId,
      });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      UserEntity,
      DriverDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findAll(tenantId: string): Promise<DriverDomain[]> {
    const driverEntities = await this.driverRepository.find({
      where: {
        isDeleted: false,
        userType: UserType.Driver,
        tenantId,
      },
      select: ['id', 'contactName'],
    });
    const responseDomain = this.mapper.mapArray(
      driverEntities,
      UserEntity,
      DriverDomain,
    );
    return responseDomain;
  }

  async findOne(
    fields: EntityCondition<DriverDomain>,
  ): Promise<NullableType<DriverDomain>> {
    const requestEntity: Partial<UserEntity> = this.mapper.map(
      fields,
      DriverDomain,
      UserEntity,
    );
    const driverEntity = await this.driverRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<UserEntity>),
        userType: UserType.Driver,
        isDeleted: false,
      },
    });
    if (driverEntity) {
      const responseDomain = this.mapper.map(
        driverEntity,
        UserEntity,
        DriverDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: DriverDomain): Promise<DriverDomain> {
    const requestEntity = this.mapper.map(payload, DriverDomain, UserEntity);
    const driverEntity = await this.driverRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      driverEntity,
      UserEntity,
      DriverDomain,
    );
    return responseDomain;
  }
}
