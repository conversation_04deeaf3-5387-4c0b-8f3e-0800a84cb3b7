import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DriverRepository } from './repository/driver.repository';
import { UserEntity } from '../../users/infrastructure/entities/user.entity';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { DriverFilterConfig } from '../driver-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([UserEntity])],
  providers: [
    DriverRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(DriverFilterConfig()),
    },
  ],
  exports: [DriverRepository],
})
export class RelationalDriverPersistenceModule {}
