import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateDriverDto } from '../../dto/create-driver.dto';
import { DriverDomain } from '../../domain/driver';
import { UserEntity } from '../../../users/infrastructure/entities/user.entity';
import { GetDriverDto } from '../../dto/get-driver.dto';
import { GetDriverMinimalDto } from '../../dto/get-driver-minimal.dto';

@Injectable()
export class DriverProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        CreateDriverDto,
        DriverDomain,
        forMember(
          (dest) => dest.employment,
          mapFrom((src) => src.employment),
        ),
      );

      createMap(
        mapper,
        DriverDomain,
        UserEntity,
        forMember(
          (dest) => dest.contactName,
          mapFrom((src) => src.name),
        ),
        forMember(
          (dest) => dest.employment,
          mapFrom((src) => src.employment),
        ),
      );

      createMap(
        mapper,
        UserEntity,
        DriverDomain,
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.contactName),
        ),
        forMember(
          (dest) => dest.employment,
          mapFrom((src) => src.employment),
        ),
      );

      createMap(
        mapper,
        DriverDomain,
        GetDriverDto,
        forMember(
          (dest) => dest.employment,
          mapFrom((src) => src.employment),
        ),
      );

      createMap(mapper, DriverDomain, GetDriverMinimalDto);
    };
  }
}
