import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { Request } from 'express';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { CreateDriverDto } from './dto/create-driver.dto';
import { TenantValidationService } from '../tenants/tenant-validation.service';
import { DriverService } from './driver.service';
import { DriverDomain } from './domain/driver';
import { GetAllDriverDto, GetDriverDto } from './dto/get-driver.dto';
import { UpdateDriverStatusDto } from './dto/update-driver-status.dto';
import {
  GetAllDriverMinimalDto,
  GetDriverMinimalDto,
} from './dto/get-driver-minimal.dto';

@ApiTags('Business - User - Drivers')
@Controller({
  path: 'drivers',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class DriverController {
  constructor(
    private readonly driverService: DriverService,
    private readonly secureFilterService: SecureFilterService,
    private readonly tenantValidationService: TenantValidationService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Driver' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createDriverDto: CreateDriverDto,
  ): Promise<GetDriverDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);

      const driverDomain = this.mapper.map(
        createDriverDto,
        CreateDriverDto,
        DriverDomain,
      );
      driverDomain.tenantId = tenantId;
      const responseDomain = await this.driverService.create(driverDomain);

      const response = this.mapper.map(
        responseDomain,
        DriverDomain,
        GetDriverDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all drivers with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllDriverDto })
  async getDriverList(
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllDriverDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);

      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );

      const result = await this.driverService.getDriverList(
        combinedFilter,
        tenantId,
      );

      const mappedData = this.mapper.mapArray(
        result.data,
        DriverDomain,
        GetDriverDto,
      );

      const response: GetAllDriverDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('all/minimal')
  @ApiOperation({
    summary: 'Get all drivers (id and name only, no pagination)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllDriverMinimalDto })
  async getAllDriversMinimal(
    @Req() request: Request,
  ): Promise<GetAllDriverMinimalDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);

      const drivers = await this.driverService.getAllDrivers(tenantId);

      const data = this.mapper.mapArray(
        drivers,
        DriverDomain,
        GetDriverMinimalDto,
      );

      return { data };
    } catch (error) {
      throw error;
    }
  }

  @Get(':driverId')
  @ApiOperation({ summary: 'Find driver by driver Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetDriverDto })
  async getDriverDetails(
    @Param('driverId') driverId: string,
  ): Promise<GetDriverDto> {
    try {
      const responseDomain =
        await this.driverService.getDriverDetails(driverId);
      const response = this.mapper.map(
        responseDomain,
        DriverDomain,
        GetDriverDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':driverId')
  @ApiOperation({ summary: 'Update driver by driver Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateDriverDetails(
    @Param('driverId') driverId: string,
    @Body() updateDriverDto: CreateDriverDto,
  ): Promise<void> {
    try {
      const driver = this.mapper.map(
        updateDriverDto,
        CreateDriverDto,
        DriverDomain,
      );
      driver.id = driverId;
      await this.driverService.updateDriverDetails(driver);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Patch(':driverId/status')
  @ApiOperation({ summary: 'Update driver status by driver Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateDriverStatus(
    @Param('driverId') driverId: string,
    @Body() updateDriverStatusDto: UpdateDriverStatusDto,
  ): Promise<void> {
    try {
      const { status } = updateDriverStatusDto;
      await this.driverService.updateDriverStatus(driverId, status);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':driverId')
  @ApiOperation({ summary: 'Soft-delete driver by driver Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteDriver(@Param('driverId') driverId: string): Promise<void> {
    try {
      await this.driverService.deleteDriver(driverId);
      return;
    } catch (error) {
      throw error;
    }
  }
}
