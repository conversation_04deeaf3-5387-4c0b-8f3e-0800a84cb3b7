import { Injectable } from '@nestjs/common';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { CreateHistoryDto } from './dto/create-history.dto';
import { HistoryRepository } from './infrastructure/repositories/history.repository';
import { HistoryDomain } from './domain/history.domain';
import { HistoryEntity } from './infrastructure/entities/history.entity';

@Injectable()
export class HistoryService {
  constructor(
    private readonly historyRepository: HistoryRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(historyDto: CreateHistoryDto): Promise<HistoryDomain> {
    const history = await this.historyRepository.create(historyDto);
    return history;
  }

  async find(entity: string, entityId: string): Promise<HistoryEntity[]> {
    const history = await this.historyRepository.find(entity, entityId);
    return history;
  }
}
