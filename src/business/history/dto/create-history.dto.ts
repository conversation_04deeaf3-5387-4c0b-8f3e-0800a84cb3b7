import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class CreateHistoryDto {
  @AutoMap()
  @ApiProperty({ example: 'Order' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  entity: string;

  @AutoMap()
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  @IsNotEmpty()
  tenantId: string;

  @AutoMap()
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  @IsNotEmpty()
  entityId: string;

  @AutoMap()
  @ApiProperty({ example: 'status' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  property: string;

  @AutoMap()
  @ApiProperty({ example: 'string' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  propertyDataType: string;

  @AutoMap()
  @ApiProperty({ example: 'Draft', required: false })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  oldValue: string;

  @AutoMap()
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  @IsNotEmpty()
  createdBy: string;

  @AutoMap()
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  @IsNotEmpty()
  updatedBy: string;

  @AutoMap()
  @ApiProperty({ example: 'Placed', required: false })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  newValue: string;
}
