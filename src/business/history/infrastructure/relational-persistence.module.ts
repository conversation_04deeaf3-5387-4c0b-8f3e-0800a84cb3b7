import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HistoryEntity } from './entities/history.entity';
import { HistoryRepository } from './repositories/history.repository';
import { UserEntity } from '../../user/users/infrastructure/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      HistoryEntity,
      UserEntity,
    ]),
  ],
  providers: [HistoryRepository],
  exports: [HistoryRepository],
})
export class RelationalHistoryPersistenceModule {}
