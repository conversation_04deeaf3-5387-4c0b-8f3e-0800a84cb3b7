import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';

import { HistoryEntity } from '../entities/history.entity';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';
import { CreateHistoryDto } from '../../dto/create-history.dto';

@Injectable()
export class HistoryRepository {
  constructor(
    @InjectRepository(HistoryEntity)
    private readonly historyRepository: Repository<HistoryEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(createDto: CreateHistoryDto): Promise<HistoryEntity> {
    // const requestEntity = this.mapper.map(data, AddressDomain, AddressEntity);
    // const user = await this.userRepository.findOne({
    //   where: { id: requestEntity.customerId },
    // });
    // if (!user) {
    //   throw new UserNotFoundException(requestEntity.customerId);
    // }

    const HistoryEntity = await this.historyRepository.save(
      this.historyRepository.create(createDto),
    );
    // const responseDomain = this.mapper.map(
    //   addressEntity,
    //   AddressEntity,
    //   AddressDomain,
    // );
    return HistoryEntity;
  }

  async find(entity: string, entityId: string): Promise<HistoryEntity[]> {
    const HistoryEntity = await this.historyRepository.find({
      where: { entity, entityId },
    });

    return HistoryEntity;
  }
}
