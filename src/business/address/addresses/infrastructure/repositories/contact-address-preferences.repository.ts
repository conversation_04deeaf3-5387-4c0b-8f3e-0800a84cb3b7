import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {  Repository } from 'typeorm';
import { ContactAddressPreferenceEntity } from '../entities/contact-address-preferences.entity';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { AddressPreferenceDomain } from '../../../../customer-portal/address/domain/address-preference';

@Injectable()
export class ContactAddressPreferenceRepository {
    constructor(
        @InjectRepository(ContactAddressPreferenceEntity)
        private readonly contactAddressPreferenceRepository: Repository<ContactAddressPreferenceEntity>,
        @InjectMapper() private readonly mapper: Mapper,
    ) { }

    async findDefaultForPickup(): Promise<AddressPreferenceDomain | null> {
        const address = await this.contactAddressPreferenceRepository.findOne({
            where: {
                isDefaultForPickup: true,
                //  isDeleted: false,
            },
        });
        return address
            ? this.mapper.map(address, ContactAddressPreferenceEntity, AddressPreferenceDomain)
            : null;
    }

    async findDefaultForDelivery(): Promise<AddressPreferenceDomain | null> {
        const address = await this.contactAddressPreferenceRepository.findOne({
            where: {
                isDefaultForDelivery: true,
                //    isDeleted: false,
            },
        });
        return address
            ? this.mapper.map(address, ContactAddressPreferenceEntity, AddressPreferenceDomain)
            : null;
    }
}