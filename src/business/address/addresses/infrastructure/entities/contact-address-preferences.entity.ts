import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { AddressEntity } from './address.entity';
import { ContactEntity } from '../../../../user/contacts/infrastructure/persistence/relational/entities/contact.entity';

@Entity('contact_address_preferences')
export class ContactAddressPreferenceEntity extends EntityRelationalHelper {
    @AutoMap()
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @AutoMap()
    @Column('uuid')
    @Index()
    tenantId: string;

    @AutoMap()
    @Column('uuid')
    contactId: string;

    @AutoMap()
    @ManyToOne(() => ContactEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'contactId' })
    contact: ContactEntity;

    @AutoMap()
    @Column('uuid')
    addressId: string;

    @AutoMap()
    @ManyToOne(() => AddressEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'addressId' })
    address: AddressEntity;

    @AutoMap()
    @Column({ default: false })
    isFavoriteForPickup: boolean;

    @AutoMap()
    @Column({ default: false })
    isFavoriteForDelivery: boolean;

    @AutoMap()
    @Column({ default: false, nullable: true })
    isDefaultForPickup: boolean;

    @AutoMap()
    @Column({ default: false, nullable: true })
    isDefaultForDelivery: boolean;

    @AutoMap()
    @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @AutoMap()
    @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

}

