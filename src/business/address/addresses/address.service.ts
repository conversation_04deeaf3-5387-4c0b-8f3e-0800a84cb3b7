import { Injectable } from '@nestjs/common';
import { AddressRepository } from './infrastructure/repositories/address.repository';
import { AddressDomain } from './domain/address';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import {
  AddressNotFoundException,
  AddressOperationNotAllowedException,
} from '@app/utils/errors/exceptions/address-exceptions';
import { PaginatedResult } from '../../../utils/query-creator/interfaces';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';

@Injectable()
export class AddressService {
  constructor(
    private readonly addressRepository: AddressRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(addressDomain: AddressDomain): Promise<AddressDomain> {
    const address = await this.addressRepository.create(addressDomain);
    return address;
  }

  async duplicateAddress(
    addressId: AddressDomain['id'],
  ): Promise<AddressDomain> {
    const addressDomain = await this.addressRepository.findOne({
      id: addressId,
    });
    if (!addressDomain) {
      throw new AddressNotFoundException(addressId);
    }

    const duplicateAddress = this.mapper.map(
      addressDomain,
      AddressDomain,
      AddressDomain,
    );

    const address = await this.addressRepository.create(duplicateAddress);
    return address;
  }

  async getAddressList(
    filter: BaseFilterDto,
    tenantId: string,
    customerId?: AddressDomain['customerId'],
  ): Promise<PaginatedResult<AddressDomain>> {
    const addressDomain = await this.addressRepository.find(
      filter,
      tenantId,
      customerId,
    );
    return addressDomain;
  }

  async getAddressDetails(
    addressId: AddressDomain['id'],
  ): Promise<AddressDomain> {
    const addressDomain = await this.addressRepository.findOne({
      id: addressId,
    });
    if (!addressDomain) {
      throw new AddressNotFoundException(addressId);
    }
    return addressDomain;
  }

  async updateAddressDetails(addressDomain: AddressDomain): Promise<void> {
    const existingAddress = await this.addressRepository.findOne({
      id: addressDomain.id,
    });
    if (!existingAddress) {
      throw new AddressNotFoundException(addressDomain.id);
    }

    await this.addressRepository.update(addressDomain);
    return;
  }

  async deleteAddress(addressId: AddressDomain['id']): Promise<void> {
    const address = await this.addressRepository.findOne({ id: addressId });
    if (!address) {
      throw new AddressNotFoundException(addressId);
    }

    // Prevent deletion of default addresses
    if (address.isDefaultForPickup || address.isDefaultForDelivery) {
      throw new AddressOperationNotAllowedException(
        '',
        'delete',
        'Cannot delete a default address',
      );
    }

    address.isDeleted = true;
    address.deletedAt = new Date();
    await this.addressRepository.update(address);
    return;
  }
}
