import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { RelationalAddressPersistenceModule } from './infrastructure/relational-persistence.module';
import { AddressController } from './address.controller';
import { AddressService } from './address.service';
import { AddressProfile } from './infrastructure/mappers/address.profile';
import { RelationalZonePersistenceModule } from '@app/business/zone/zones/infrastructure/relational-persistence.module';
import { AddressFilterConfig } from './address-filter.config';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { AddressMapperProfile } from './infrastructure/mappers/address-preference.profile';

@Module({
  imports: [
    RelationalAddressPersistenceModule,
    RelationalZonePersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  controllers: [AddressController],
  providers: [
    AddressService,
    AddressProfile,
    AddressMapperProfile,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(AddressFilterConfig()),
    },
  ],
  exports: [AddressService],
})
export class AddressModule {}
