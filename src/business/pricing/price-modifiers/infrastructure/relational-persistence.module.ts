import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PriceModifierRepository } from './repositories/price-modifier.repository';
import { PriceModifierGroupRepository } from './repositories/price-modifier-group.repository';
import { PriceModifierEntity } from './entities/price-modifier.entity';
import { PriceModifierGroupEntity } from './entities/price-modifier-group.entity';
import { PriceModifierGroupMemberEntity } from './entities/price-modifier-group-member.entity';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { PriceModifierFilterConfig } from '../price-modifier-filter.config';
import { PriceModifierGroupFilterConfig } from '../price-modifier-group-filter.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PriceModifierEntity,
      PriceModifierGroupEntity,
      PriceModifierGroupMemberEntity,
    ]),
  ],
  providers: [
    PriceModifierRepository,
    PriceModifierGroupRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(PriceModifierFilterConfig()),
    },
    {
      provide: SecureFilterService,
      useFactory: () =>
        new SecureFilterService(PriceModifierGroupFilterConfig()),
    },
  ],
  exports: [PriceModifierRepository, PriceModifierGroupRepository],
})
export class RelationalPriceModifierPersistenceModule {}
