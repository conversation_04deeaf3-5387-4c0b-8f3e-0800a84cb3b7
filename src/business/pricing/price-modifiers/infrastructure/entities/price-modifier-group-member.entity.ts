import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';

@Entity('price_modifier_group_members')
export class PriceModifierGroupMemberEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid', { name: 'group_id' })
  @Index()
  groupId: string;

  @AutoMap()
  @Column('uuid', { name: 'member_id' })
  @Index()
  memberId: string;

  @AutoMap()
  @Column()
  isGroup: boolean;

  @AutoMap()
  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;
}
