import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { EModifierGroupBehavior } from '../../domain/price-modifier.types';

@Entity('price_modifier_groups')
export class PriceModifierGroupEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @Column({ length: 255 })
  name: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  description: string;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: EModifierGroupBehavior,
  })
  behavior: EModifierGroupBehavior;

  @AutoMap()
  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({
    name: 'updated_at',
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;
}
