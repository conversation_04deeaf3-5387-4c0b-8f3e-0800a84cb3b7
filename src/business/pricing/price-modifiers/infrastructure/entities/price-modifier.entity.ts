import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import {
  ECalculationType,
  TieredRange,
} from '../../domain/price-modifier.types';

@Entity('price_modifiers')
export class PriceModifierEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @Column({ length: 255 })
  name: string;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: ECalculationType,
  })
  calculationType: ECalculationType;

  @AutoMap()
  @Column({ name: 'field_name', length: 100, nullable: true })
  fieldName: string;

  @AutoMap()
  @Column({
    name: 'applicable_range_min',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  applicableRangeMin: number;

  @AutoMap()
  @Column({
    name: 'applicable_range_max',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  applicableRangeMax: number;

  @AutoMap()
  @Column({
    name: 'calculation_base',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  calculationBase: number;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  increment: number;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  amount: number;

  @AutoMap()
  @Column({
    name: 'tiered_ranges',
    type: 'jsonb',
    nullable: true,
    default: '[]',
  })
  tieredRanges: TieredRange[];

  @AutoMap()
  @Column({ nullable: true })
  tieredDefaultValue: number;

  @AutoMap()
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @AutoMap()
  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @AutoMap()
  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({
    name: 'updated_at',
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;
}
