import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, In, Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { PriceModifierGroupEntity } from '../entities/price-modifier-group.entity';
import { PriceModifierGroupDomain } from '../../domain/price-modifier-group';
import { PriceModifierGroupMemberEntity } from '../entities/price-modifier-group-member.entity';
import { PriceModifierEntity } from '../entities/price-modifier.entity';
import { BaseFilterDto } from '../../../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../../../utils/query-creator/interfaces';
import { SecureFilterService } from '../../../../../core/infrastructure/filtering/services/secure-filter.service';
import { PriceModifierGroupFilterConfig } from '../../price-modifier-group-filter.config';

@Injectable()
export class PriceModifierGroupRepository {
  constructor(
    @InjectRepository(PriceModifierGroupEntity)
    private readonly groupRepository: Repository<PriceModifierGroupEntity>,
    @InjectRepository(PriceModifierGroupMemberEntity)
    private readonly groupMemberRepository: Repository<PriceModifierGroupMemberEntity>,
    @InjectRepository(PriceModifierEntity)
    private readonly modifierRepository: Repository<PriceModifierEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(
      PriceModifierGroupFilterConfig(),
    );
  }

  async create(
    data: PriceModifierGroupDomain,
  ): Promise<PriceModifierGroupDomain> {
    const groupEntity = this.groupRepository.create({
      tenantId: data.tenantId,
      name: data.name,
      description: data.description,
      behavior: data.behavior,
    });

    const savedGroup = await this.groupRepository.save(groupEntity);

    const memberEntities = data.members.map((member) =>
      this.groupMemberRepository.create({
        groupId: savedGroup.id,
        memberId: member.id,
        isGroup: member.isGroup,
      }),
    );

    await this.groupMemberRepository.save(memberEntities);

    const responseDomain = this.mapper.map(
      savedGroup,
      PriceModifierGroupEntity,
      PriceModifierGroupDomain,
    );
    responseDomain.members = data.members;
    return responseDomain;
  }

  async find(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PriceModifierGroupDomain>> {
    const queryBuilder = this.groupRepository
      .createQueryBuilder('group')
      .where('group.tenantId =:tenantId', { tenantId });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      PriceModifierGroupEntity,
      PriceModifierGroupDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findOne(
    fields: EntityCondition<PriceModifierGroupDomain>,
  ): Promise<NullableType<PriceModifierGroupDomain>> {
    const requestEntity: Partial<PriceModifierGroupEntity> = this.mapper.map(
      fields,
      PriceModifierGroupDomain,
      PriceModifierGroupEntity,
    );
    const groupEntity = await this.groupRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<PriceModifierGroupEntity>),
      },
    });

    if (groupEntity) {
      const members = await this.groupMemberRepository.find({
        where: { groupId: groupEntity.id },
      });

      const responseDomain = this.mapper.map(
        groupEntity,
        PriceModifierGroupEntity,
        PriceModifierGroupDomain,
      );

      responseDomain.members = [];

      for (const member of members) {
        if (member.isGroup) {
          const group = await this.groupRepository.findOne({
            where: { id: member.memberId },
          });

          responseDomain.members.push({
            id: member.memberId,
            isGroup: member.isGroup,
            name: group?.name,
          });
        } else {
          const modifier = await this.modifierRepository.findOne({
            where: { id: member.memberId },
          });

          responseDomain.members.push({
            id: member.memberId,
            isGroup: member.isGroup,
            name: modifier?.name,
          });
        }
      }

      return responseDomain;
    }
    return null;
  }

  async update(
    data: PriceModifierGroupDomain,
  ): Promise<PriceModifierGroupDomain> {
    const updateResult = await this.groupRepository.save(data);

    await this.groupMemberRepository.delete({ groupId: updateResult.id });

    const memberEntities = data.members.map((member) =>
      this.groupMemberRepository.create({
        groupId: updateResult.id,
        memberId: member.id,
        isGroup: member.isGroup,
      }),
    );
    await this.groupMemberRepository.save(memberEntities);

    const responseDomain = this.mapper.map(
      updateResult,
      PriceModifierGroupEntity,
      PriceModifierGroupDomain,
    );
    responseDomain.members = data.members;
    return responseDomain;
  }

  async delete(id: string): Promise<void> {
    await this.groupRepository.delete(id);
    return;
  }

  // Add method to check if a modifier belongs to any group
  async isModifierInAnyGroup(modifierId: string): Promise<boolean> {
    const count = await this.groupMemberRepository.count({
      where: { memberId: modifierId },
    });

    return count > 0;
  }

  async findByIds(ids: string[]): Promise<PriceModifierGroupDomain[]> {
    const entities = await this.groupRepository.findBy({
      id: In(ids),
    });

    const responseDomain = this.mapper.mapArray(
      entities,
      PriceModifierGroupEntity,
      PriceModifierGroupDomain,
    );

    return responseDomain;
  }

  async findByTenantId(tenantId: string) {
    const entities = await this.groupRepository.find({
      where: { tenantId },
      select: ['id', 'name', 'createdAt'],
    });

    const responseDomain = this.mapper.mapArray(
      entities,
      PriceModifierGroupEntity,
      PriceModifierGroupDomain,
    );

    return responseDomain;
  }
}
