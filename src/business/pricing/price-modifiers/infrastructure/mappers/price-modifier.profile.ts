import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreatePriceModifierDto } from '../../dto/create-price-modifier.dto';
import { PriceModifierDomain } from '../../domain/price-modifier';
import { PriceModifierEntity } from '../entities/price-modifier.entity';
import { GetPriceModifierDto } from '../../dto/get-price-modifier.dto';

@Injectable()
export class PriceModifierProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        CreatePriceModifierDto,
        PriceModifierDomain,
        forMember(
          (dest) => dest.tieredRanges,
          mapFrom((src) => src.tieredRanges),
        ),
        forMember(
          (dest) => dest.metadata,
          mapFrom((src) => src.metadata),
        ),
      );

      createMap(
        mapper,
        PriceModifierDomain,
        PriceModifierEntity,
        forMember(
          (dest) => dest.tieredRanges,
          mapFrom((src) => src.tieredRanges),
        ),
        forMember(
          (dest) => dest.metadata,
          mapFrom((src) => src.metadata),
        ),
      );

      createMap(
        mapper,
        PriceModifierEntity,
        PriceModifierDomain,
        forMember(
          (dest) => dest.tieredRanges,
          mapFrom((src) => src.tieredRanges),
        ),
        forMember(
          (dest) => dest.metadata,
          mapFrom((src) => src.metadata),
        ),
      );

      createMap(
        mapper,
        PriceModifierDomain,
        GetPriceModifierDto,
        forMember(
          (dest) => dest.tieredRanges,
          mapFrom((src) => src.tieredRanges),
        ),
        forMember(
          (dest) => dest.metadata,
          mapFrom((src) => src.metadata),
        ),
      );
    };
  }
}
