import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreatePriceModifierGroupDto } from '../../dto/create-price-modifier-group.dto';
import { PriceModifierGroupDomain } from '../../domain/price-modifier-group';
import { PriceModifierGroupEntity } from '../entities/price-modifier-group.entity';
import { GetPriceModifierGroupDto } from '../../dto/get-price-modifier-group.dto';

@Injectable()
export class PriceModifierGroupProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        CreatePriceModifierGroupDto,
        PriceModifierGroupDomain,
        forMember(
          (dest) => dest.members,
          mapFrom((src) => src.members),
        ),
      );

      createMap(mapper, PriceModifierGroupDomain, PriceModifierGroupEntity);
      createMap(mapper, PriceModifierGroupEntity, PriceModifierGroupDomain);

      createMap(
        mapper,
        PriceModifierGroupDomain,
        GetPriceModifierGroupDto,
        forMember(
          (dest) => dest.members,
          mapFrom((src) => src.members),
        ),
      );
    };
  }
}
