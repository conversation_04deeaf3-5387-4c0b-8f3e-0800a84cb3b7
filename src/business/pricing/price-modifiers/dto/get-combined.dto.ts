import { ApiProperty } from '@nestjs/swagger';

export class CombinedModifierDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: 'Premium Shipping Fees' })
  name: string;

  @ApiProperty({ example: false })
  isGroup: boolean;
}

export class GetCombinedDto {
  @ApiProperty({ type: [CombinedModifierDto] })
  data: CombinedModifierDto[];
}
