import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IOrder } from '@core/pricing/domain/order.interface';

export class OrderDetailsDto implements Partial<IOrder> {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ example: 100.0 })
  @IsNumber()
  @IsNotEmpty()
  basePrice: number;

  @ApiProperty({ example: 150.0 })
  @IsNumber()
  @IsNotEmpty()
  declaredPrice: number;

  @ApiProperty({ example: 5.5 })
  @IsNumber()
  weight?: number;

  @ApiProperty({ example: 50 })
  @IsNumber()
  distance?: number;

  @ApiProperty({ example: 2 })
  @IsNumber()
  quantity?: number;
}

export class CalculatePriceDto {
  @ApiProperty({ type: OrderDetailsDto })
  @IsObject()
  @ValidateNested()
  @Type(() => OrderDetailsDto)
  order: OrderDetailsDto;

  @ApiProperty({
    type: [String],
    description: 'IDs of price modifiers to apply',
    example: ['66fedab8-7820-4cca-b8ca-cf59ade1aada'],
  })
  @IsUUID(4, { each: true })
  memberIds: string[];
}
