import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';
import {
  CreatePriceModifierGroupDto,
  ModifierMemberDto,
} from './create-price-modifier-group.dto';

export class GetPriceModifierGroupDto extends CreatePriceModifierGroupDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ type: () => [GetModifierMemberDto] })
  members: GetModifierMemberDto[];

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;
}

export class GetModifierMemberDto extends ModifierMemberDto {
  @AutoMap()
  @ApiProperty({ example: 'Premium Shipping Fees' })
  name: string;
}

export class GetAllPriceModifierGroupDto extends PageResponse {
  @ApiProperty({ type: [GetPriceModifierGroupDto] })
  data: GetPriceModifierGroupDto[];
}
