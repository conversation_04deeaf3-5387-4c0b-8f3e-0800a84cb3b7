import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { PageResponse } from '@utils/page-response';
import { CreatePriceModifierDto } from './create-price-modifier.dto';

export class GetPriceModifierDto extends CreatePriceModifierDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty()
  createdAt: Date;

  @AutoMap()
  @ApiProperty()
  updatedAt: Date;
}

export class GetAllPriceModifierDto extends PageResponse {
  @ApiProperty({ type: [GetPriceModifierDto] })
  data: GetPriceModifierDto[];
}
