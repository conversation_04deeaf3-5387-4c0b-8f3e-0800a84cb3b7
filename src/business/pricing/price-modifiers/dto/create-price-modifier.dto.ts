import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsNumber,
  IsBoolean,
  IsObject,
  ValidateIf,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';
import {
  ECalculationType,
  ECalculationField,
  TieredRange,
  ERangeFromOperator,
  ERangeToOperator,
} from '../domain/price-modifier.types';

export class CreatePriceModifierDto {
  @AutoMap()
  @ApiProperty({ example: 'Weight Surcharge' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ enum: ECalculationType, example: ECalculationType.FlatAmount })
  @IsEnum(ECalculationType)
  @IsNotEmpty()
  calculationType: ECalculationType;

  @AutoMap()
  @ApiProperty({ enum: ECalculationField, example: ECalculationField.Weight })
  @IsEnum(ECalculationField)
  @ValidateIf((o) => o.calculationType !== ECalculationType.FlatAmount)
  @IsNotEmpty()
  fieldName: ECalculationField;

  @AutoMap()
  @ApiPropertyOptional({
    example: 50.0,
    description: 'Minimum value for applicable range',
  })
  @IsNumber()
  @IsOptional()
  applicableRangeMin?: number;

  @AutoMap()
  @ApiPropertyOptional({
    example: 100.0,
    description: 'Maximum value for applicable range',
  })
  @IsNumber()
  @IsOptional()
  applicableRangeMax?: number;

  @AutoMap()
  @ApiPropertyOptional({
    example: 0,
    description: 'Base value for calculation',
  })
  @IsNumber()
  @ValidateIf((o) =>
    [
      ECalculationType.FlatOverageAmount,
      ECalculationType.FlatOveragePercentage,
      ECalculationType.IncrementalOverageAmount,
      ECalculationType.IncrementalOveragePercentage,
    ].includes(o.calculationType),
  )
  calculationBase?: number;

  @AutoMap()
  @ApiPropertyOptional({ example: 1, description: 'Increment for calculation' })
  @IsNumber()
  @ValidateIf((o) =>
    [
      ECalculationType.IncrementalOverageAmount,
      ECalculationType.IncrementalOveragePercentage,
    ].includes(o.calculationType),
  )
  increment?: number;

  @AutoMap()
  @ApiPropertyOptional({ example: 5.0, description: 'Amount to apply' })
  @IsNumber()
  @IsNotEmpty()
  @ValidateIf(
    (o) => o.calculationType !== ECalculationType.TieredFixedOverageAmount,
  )
  @ValidateIf(
    (o) => o.calculationType !== ECalculationType.TieredFixedOveragePercentage,
  )
  @ValidateIf(
    (o) =>
      o.calculationType !== ECalculationType.TieredIncrementalOverageAmount,
  )
  @ValidateIf(
    (o) =>
      o.calculationType !== ECalculationType.TieredIncrementalOveragePercentage,
  )
  amount: number;

  @AutoMap()
  @ApiPropertyOptional({
    type: [Object],
    example: [
      {
        fromValue: 0,
        fromOperator: 'GreaterThanOrEqual',
        toValue: 50,
        toOperator: 'LessThanOrEqual',
        value: 5,
      },
      {
        fromValue: 51,
        fromOperator: 'GreaterThan',
        toValue: 100,
        toOperator: 'LessThanOrEqual',
        value: 10,
      },
    ],
    description: 'Tiered ranges for calculations with multiple thresholds',
  })
  @IsArray()
  @IsOptional()
  @ValidateIf((o) =>
    [
      ECalculationType.TieredFixedOverageAmount,
      ECalculationType.TieredFixedOveragePercentage,
      ECalculationType.TieredIncrementalOverageAmount,
      ECalculationType.TieredIncrementalOveragePercentage,
    ].includes(o.calculationType),
  )
  @ValidateNested({ each: true })
  @Type(() => TieredRangeDto)
  tieredRanges?: TieredRange[];

  @AutoMap()
  @IsNumber()
  @IsOptional()
  @ValidateIf((o) => o.tieredRanges?.length > 0)
  tieredDefaultValue: number;

  @AutoMap()
  @ApiPropertyOptional({ example: true, default: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @AutoMap()
  @ApiPropertyOptional({
    example: { threshold: 50, isPercentage: false },
    description: 'Additional metadata for the price modifier',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

export class TieredRangeDto implements TieredRange {
  @IsNumber()
  @IsNotEmpty()
  fromValue: number;

  @IsEnum(ERangeFromOperator)
  @IsNotEmpty()
  fromOperator: ERangeFromOperator;

  @IsNumber()
  @IsNotEmpty()
  toValue: number;

  @IsEnum(ERangeToOperator)
  @IsNotEmpty()
  toOperator: ERangeToOperator;

  @IsNumber()
  @IsNotEmpty()
  value: number;
}
