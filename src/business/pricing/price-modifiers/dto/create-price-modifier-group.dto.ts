import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsArray,
  IsUUID,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';
import { EModifierGroupBehavior } from '../domain/price-modifier.types';

export class CreatePriceModifierGroupDto {
  @AutoMap()
  @ApiProperty({ example: 'Premium Shipping Fees' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({
    example: 'Additional fees for premium shipping service',
  })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  description: string;

  @AutoMap()
  @ApiProperty({ enum: EModifierGroupBehavior })
  @IsEnum(EModifierGroupBehavior)
  @IsNotEmpty()
  behavior: EModifierGroupBehavior;

  @AutoMap()
  @ApiProperty({ type: () => [ModifierMemberDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ModifierMemberDto)
  members: ModifierMemberDto[];
}

export class ModifierMemberDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @AutoMap()
  @ApiProperty({ example: false })
  @IsBoolean()
  isGroup: boolean;
}
