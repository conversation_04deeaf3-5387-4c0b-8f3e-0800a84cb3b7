import { ApiProperty } from '@nestjs/swagger';
import {
  ICalculationResult,
  IModifierResult,
} from '@core/pricing/domain/calculation-result.type';
import { CalculationType } from '@core/pricing/domain/price-modifier.interface';

export class ModifierResultDto implements IModifierResult {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: 'Weight Surcharge' })
  name: string;

  @ApiProperty({ enum: CalculationType, example: CalculationType.FLAT_AMOUNT })
  type: CalculationType;

  @ApiProperty({ example: 10.5 })
  amount: number;

  @ApiProperty({ example: false })
  isGroupResult: boolean;

  @ApiProperty({ type: [ModifierResultDto], required: false })
  children?: ModifierResultDto[];
}

export class CalculationResultDto implements ICalculationResult {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  orderId: string;

  @ApiProperty({ example: 100.0 })
  basePrice: number;

  @ApiProperty({ type: [ModifierResultDto] })
  modifiers: ModifierResultDto[];

  @ApiProperty({ example: 110.5 })
  totalPrice: number;

  @ApiProperty({ type: [String], example: [] })
  errors: string[];
}
