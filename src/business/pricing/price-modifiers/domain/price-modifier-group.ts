import { AutoMap } from '@automapper/classes';
import { EModifierGroupBehavior } from './price-modifier.types';

export class PriceModifierGroupDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  name: string;

  @AutoMap()
  description?: string;

  @AutoMap()
  behavior: EModifierGroupBehavior;

  @AutoMap()
  members: ModifierMemberDomain[];

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;
}

export class ModifierMemberDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  isGroup: boolean;

  @AutoMap()
  name?: string;
}
