import { Injectable } from '@nestjs/common';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { DataSource } from 'typeorm';
import { PriceModifierRepository } from './infrastructure/repositories/price-modifier.repository';
import { PriceModifierGroupRepository } from './infrastructure/repositories/price-modifier-group.repository';
import { PriceModifierDomain } from './domain/price-modifier';
import { PriceModifierGroupDomain } from './domain/price-modifier-group';
import {
  PriceModifierAlreadyExistsException,
  PriceModifierGroupAlreadyExistsException,
  PriceModifierGroupNotFoundException,
  PriceModifierGroupOperationNotAllowedException,
  PriceModifierGroupTenantMismatchException,
  PriceModifierNotFoundException,
  PriceModifierOperationNotAllowedException,
  PriceModifierTenantMismatchException,
} from '@utils/errors/exceptions/price-modifier-exceptions';
import {
  ECalculationType,
  EModifierGroupBehavior,
  ERangeFromOperator,
  ERangeToOperator,
} from './domain/price-modifier.types';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { PriceModifierGroupMemberEntity } from '@app/business/pricing/price-modifiers/infrastructure/entities/price-modifier-group-member.entity';
import {
  CalculationField,
  CalculationType,
  GroupBehavior,
  IPriceModifier,
  RangeOperator,
} from '@core/pricing/domain/price-modifier.interface';
import { OrderDetailsDto } from '@app/business/pricing/price-modifiers/dto/calculate-price.dto';
import { ICalculationResult } from '@core/pricing/domain/calculation-result.type';
import { PriceCalculatorService } from '@core/pricing/price-calculator.service';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';

@Injectable()
export class PriceModifiersService {
  constructor(
    private readonly priceModifierRepository: PriceModifierRepository,
    private readonly priceModifierGroupRepository: PriceModifierGroupRepository,
    private readonly dataSource: DataSource,
    private readonly priceCalculatorService: PriceCalculatorService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async getAllPriceModifiersAndGroups(tenantId: string) {
    const modifiers =
      await this.priceModifierRepository.findByTenantId(tenantId);

    const groups =
      await this.priceModifierGroupRepository.findByTenantId(tenantId);

    const modifiedModifiers = modifiers.map((item) => ({
      ...item,
      isGroup: false,
    }));

    const modifiedGroups = groups.map((item) => ({
      ...item,
      isGroup: true,
    }));

    const allModifiersAndGroups = [...modifiedModifiers, ...modifiedGroups];

    // Sort by createdAt
    allModifiersAndGroups.sort((a, b) => {
      if (a.createdAt > b.createdAt) {
        return -1;
      }
      if (a.createdAt < b.createdAt) {
        return 1;
      }
      return 0;
    });

    const response = allModifiersAndGroups.map((item) => ({
      id: item.id,
      name: item.name,
      isGroup: item.isGroup,
    }));

    return response;
  }

  // Individual Price Modifiers
  async createPriceModifier(
    modifierDomain: PriceModifierDomain,
  ): Promise<PriceModifierDomain> {
    const existingModifier = await this.priceModifierRepository.findOne({
      name: modifierDomain.name,
      tenantId: modifierDomain.tenantId,
    });
    if (existingModifier) {
      throw new PriceModifierAlreadyExistsException(
        modifierDomain.name,
        modifierDomain.tenantId,
      );
    }
    // Validate required fields based on calculation type
    this.validateModifierFields(modifierDomain);

    const responseDomain =
      await this.priceModifierRepository.create(modifierDomain);
    return responseDomain;
  }

  async findAllPriceModifier(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PriceModifierDomain>> {
    const priceModifier = await this.priceModifierRepository.find(
      tenantId,
      filter,
    );
    return priceModifier;
  }

  async getPriceModifierById(
    id: string,
    tenantId: string,
  ): Promise<PriceModifierDomain> {
    const modifier = await this.priceModifierRepository.findOne({ id });
    if (!modifier) {
      throw new PriceModifierNotFoundException(id);
    }

    if (modifier.tenantId !== tenantId) {
      throw new PriceModifierTenantMismatchException(id, tenantId);
    }

    return modifier;
  }

  async updatePriceModifier(
    modifierDomain: PriceModifierDomain,
  ): Promise<PriceModifierDomain> {
    const existingModifier = await this.getPriceModifierById(
      modifierDomain.id,
      modifierDomain.tenantId,
    );
    if (modifierDomain.name !== existingModifier.name) {
      const duplicateModifier = await this.priceModifierRepository.findOne({
        name: modifierDomain.name,
        tenantId: modifierDomain.tenantId,
      });
      if (duplicateModifier) {
        throw new PriceModifierAlreadyExistsException(
          modifierDomain.name,
          modifierDomain.tenantId,
        );
      }
    }
    this.validateModifierFields(modifierDomain);
    const priceModifier = this.priceModifierRepository.update(modifierDomain);
    return priceModifier;
  }

  async deletePriceModifier(id: string): Promise<void> {
    const isInGroup =
      await this.priceModifierGroupRepository.isModifierInAnyGroup(id);

    if (isInGroup) {
      throw new PriceModifierOperationNotAllowedException(
        'delete',
        'This price modifier is used in one or more groups. Remove it from all groups first.',
      );
    }

    await this.priceModifierRepository.delete(id);
    return;
  }

  // Price Modifier Groups
  async createPriceModifierGroup(
    groupDomain: PriceModifierGroupDomain,
  ): Promise<PriceModifierGroupDomain> {
    const existingGroup = await this.priceModifierGroupRepository.findOne({
      name: groupDomain.name,
      tenantId: groupDomain.tenantId,
    });

    if (existingGroup) {
      throw new PriceModifierGroupAlreadyExistsException(
        groupDomain.name,
        groupDomain.tenantId,
      );
    }

    if (groupDomain.members.length > 0) {
      const memberIds = groupDomain.members
        .filter((m) => !m.isGroup)
        .map((m) => m.id);
      const groupIds = groupDomain.members
        .filter((m) => m.isGroup)
        .map((m) => m.id);

      const modifiers = await this.priceModifierRepository.findByIds(memberIds);

      if (modifiers.length !== memberIds.length) {
        const foundIds = modifiers.map((m) => m.id);
        const missingIds = memberIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifiers not found',
        );
      }

      const invalidModifiers = modifiers.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidModifiers.length > 0) {
        throw new PriceModifierTenantMismatchException(
          invalidModifiers[0].id,
          groupDomain.tenantId,
        );
      }

      const groups =
        await this.priceModifierGroupRepository.findByIds(groupIds);

      if (groups.length !== groupIds.length) {
        const foundIds = groups.map((m) => m.id);
        const missingIds = groupIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierGroupNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifier groups not found',
        );
      }

      const invalidModifierGroup = groups.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidModifierGroup.length > 0) {
        throw new PriceModifierGroupTenantMismatchException(
          invalidModifierGroup[0].id,
          groupDomain.tenantId,
        );
      }
    }

    const result = await this.priceModifierGroupRepository.create(groupDomain);
    return result;
  }

  async findAllGroups(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PriceModifierGroupDomain>> {
    const priceModifierGroup = await this.priceModifierGroupRepository.find(
      tenantId,
      filter,
    );
    return priceModifierGroup;
  }

  async getPriceModifierGroupById(
    id: string,
    tenantId: string,
  ): Promise<PriceModifierGroupDomain> {
    const group = await this.priceModifierGroupRepository.findOne({
      id,
    });

    if (!group) {
      throw new PriceModifierGroupNotFoundException(id);
    }

    if (group.tenantId !== tenantId) {
      throw new PriceModifierTenantMismatchException(id, tenantId);
    }

    return group;
  }

  async updatePriceModifierGroup(
    groupDomain: PriceModifierGroupDomain,
  ): Promise<PriceModifierGroupDomain> {
    const existingGroup = await this.getPriceModifierGroupById(
      groupDomain.id,
      groupDomain.tenantId,
    );

    if (groupDomain.name !== existingGroup.name) {
      const duplicateGroup = await this.priceModifierGroupRepository.findOne({
        name: groupDomain.name,
        tenantId: groupDomain.tenantId,
      });

      if (duplicateGroup) {
        throw new PriceModifierGroupAlreadyExistsException(
          groupDomain.name,
          groupDomain.tenantId,
        );
      }
    }

    if (groupDomain.members.length > 0) {
      const memberIds = groupDomain.members
        .filter((m) => !m.isGroup)
        .map((m) => m.id);
      const groupIds = groupDomain.members
        .filter((m) => m.isGroup)
        .map((m) => m.id);

      const modifiers = await this.priceModifierRepository.findByIds(memberIds);

      if (modifiers.length !== memberIds.length) {
        const foundIds = modifiers.map((m) => m.id);
        const missingIds = memberIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifiers not found',
        );
      }

      const invalidModifiers = modifiers.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidModifiers.length > 0) {
        throw new PriceModifierTenantMismatchException(
          invalidModifiers[0].id,
          groupDomain.tenantId,
        );
      }

      const groups =
        await this.priceModifierGroupRepository.findByIds(groupIds);

      if (groups.length !== groupIds.length) {
        const foundIds = groups.map((m) => m.id);
        const missingIds = groupIds.filter((id) => !foundIds.includes(id));
        throw new PriceModifierGroupNotFoundException(
          missingIds.length > 0
            ? missingIds[0]
            : 'One or more price modifier groups not found',
        );
      }

      const invalidModifierGroup = groups.filter(
        (m) => m.tenantId !== groupDomain.tenantId,
      );
      if (invalidModifierGroup.length > 0) {
        throw new PriceModifierGroupTenantMismatchException(
          invalidModifierGroup[0].id,
          groupDomain.tenantId,
        );
      }
    }

    const result = this.priceModifierGroupRepository.update(groupDomain);
    return result;
  }

  async deletePriceModifierGroup(id: string, tenantId: string): Promise<void> {
    await this.getPriceModifierGroupById(id, tenantId);

    const isInGroup =
      await this.priceModifierGroupRepository.isModifierInAnyGroup(id);

    if (isInGroup) {
      throw new PriceModifierGroupOperationNotAllowedException(
        'delete',
        'This price modifier group is used in one or more groups. Remove it from all groups first.',
      );
    }
    await this.priceModifierGroupRepository.delete(id);
    return;
  }

  // Helpers
  private validateModifierFields(domain: PriceModifierDomain): void {
    const { calculationType } = domain;

    // Validate required fields based on calculation type
    if (
      [
        ECalculationType.FlatOverageAmount,
        ECalculationType.FlatOveragePercentage,
        ECalculationType.IncrementalOverageAmount,
        ECalculationType.IncrementalOveragePercentage,
      ].includes(calculationType as ECalculationType) &&
      domain.calculationBase === undefined
    ) {
      throw new PriceModifierOperationNotAllowedException(
        'create',
        `calculationBase is required for ${calculationType} calculation type`,
      );
    }

    if (
      [
        ECalculationType.IncrementalOverageAmount,
        ECalculationType.IncrementalOveragePercentage,
      ].includes(calculationType as ECalculationType) &&
      domain.increment === undefined
    ) {
      throw new PriceModifierOperationNotAllowedException(
        'create',
        `increment is required for ${calculationType} calculation type`,
      );
    }

    if (
      [
        ECalculationType.TieredFixedOverageAmount,
        ECalculationType.TieredFixedOveragePercentage,
        ECalculationType.TieredIncrementalOverageAmount,
        ECalculationType.TieredIncrementalOveragePercentage,
      ].includes(calculationType as ECalculationType) &&
      (!domain.tieredRanges || domain.tieredRanges.length === 0)
    ) {
      throw new PriceModifierOperationNotAllowedException(
        'create',
        `tieredRanges are required for ${calculationType} calculation type`,
      );
    }
  }

  async calculatePrice(
    tenantId: string,
    orderDetails: OrderDetailsDto,
    memberIds: string[],
  ): Promise<ICalculationResult> {
    const modifiers = await this.priceModifierRepository.findByIds(memberIds);

    const invalidModifiers = modifiers.filter((m) => m.tenantId !== tenantId);
    if (invalidModifiers.length > 0) {
      throw new PriceModifierTenantMismatchException(
        invalidModifiers[0].id,
        tenantId,
      );
    }

    const groupMembers = await this.dataSource
      .getRepository(PriceModifierGroupMemberEntity)
      .createQueryBuilder('member')
      .where('member.memberId IN (:...memberIds)', { memberIds })
      .getMany();

    const groupIds = [...new Set(groupMembers.map((m) => m.groupId))];

    const groups =
      groupIds.length > 0
        ? await this.priceModifierGroupRepository.findByIds(groupIds)
        : [];

    const priceModifiers = modifiers.map((modifier) =>
      this.mapToPriceModifierInterface(modifier),
    );

    const groupModifiers = groups.map((group) => {
      const groupModifierIds = groupMembers
        .filter((m) => m.groupId === group.id)
        .map((m) => m.memberId);

      const childModifiers = modifiers
        .filter((m) => groupModifierIds.includes(m.id))
        .map((m) => this.mapToPriceModifierInterface(m));

      return {
        id: group.id,
        name: group.name,
        calculationType: 'Group' as any, // This is just a placeholder
        calculationField: 'BasePrice' as any, // This is just a placeholder
        value: 0,
        isGroupModifier: true,
        isEnabled: true,
        modifiers: childModifiers,
        groupBehavior: this.mapGroupBehavior(group.behavior),
      };
    });

    const allModifiers = [
      ...priceModifiers.filter(
        (m) => !groupMembers.some((gm) => gm.memberId === m.id),
      ),
      ...groupModifiers,
    ];

    return this.priceCalculatorService.calculatePrice(
      orderDetails,
      allModifiers,
    );
  }

  private mapToPriceModifierInterface(
    modifier: PriceModifierDomain,
  ): IPriceModifier {
    // Create the applicable range if min/max values exist
    const applicableRange =
      modifier.applicableRangeMin || modifier.applicableRangeMax
        ? {
            from:
              modifier.applicableRangeMin !== undefined
                ? {
                    value: modifier.applicableRangeMin,
                    operator: RangeOperator.GREATER_THAN_OR_EQUAL,
                  }
                : undefined,
            to:
              modifier.applicableRangeMax !== undefined
                ? {
                    value: modifier.applicableRangeMax,
                    operator: RangeOperator.LESS_THAN_OR_EQUAL,
                  }
                : undefined,
          }
        : undefined;

    // Transform tiered ranges to calculation engine format
    const tieredRanges =
      modifier.tieredRanges && modifier.tieredRanges.length > 0
        ? modifier.tieredRanges.map((tier) => ({
            from: {
              value: tier.fromValue,
              operator: this.mapRangeOperator(tier.fromOperator),
            },
            to: {
              value: tier.toValue,
              operator: this.mapRangeOperator(tier.toOperator),
            },
            value: tier.value,
          }))
        : undefined;

    return {
      id: modifier.id,
      name: modifier.name,
      calculationType: modifier.calculationType as unknown as CalculationType,
      calculationField: modifier.fieldName as CalculationField,
      value: modifier.amount || 0,
      increment: modifier.increment,
      calculationStartAfter: modifier.calculationBase,
      applicableRange,
      tieredRanges,
      isGroupModifier: false,
      isEnabled: modifier.isActive,
    };
  }

  private mapRangeOperator(
    operator: ERangeFromOperator | ERangeToOperator,
  ): RangeOperator {
    switch (operator) {
      case ERangeFromOperator.GreaterThan:
        return RangeOperator.GREATER_THAN;
      case ERangeFromOperator.GreaterThanOrEqual:
        return RangeOperator.GREATER_THAN_OR_EQUAL;
      case ERangeToOperator.LessThan:
        return RangeOperator.LESS_THAN;
      case ERangeToOperator.LessThanOrEqual:
        return RangeOperator.LESS_THAN_OR_EQUAL;
      default:
        throw new Error(`Unknown range operator: ${operator}`);
    }
  }

  private mapGroupBehavior(behavior: EModifierGroupBehavior): GroupBehavior {
    switch (behavior) {
      case EModifierGroupBehavior.UseSum:
        return GroupBehavior.USE_SUM;
      case EModifierGroupBehavior.UseHighest:
        return GroupBehavior.USE_HIGHEST;
      case EModifierGroupBehavior.UseLowest:
        return GroupBehavior.USE_LOWEST;
      default:
        return GroupBehavior.USE_SUM;
    }
  }
}
