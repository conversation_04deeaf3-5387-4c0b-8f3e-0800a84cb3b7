import { Module } from '@nestjs/common';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';

import { PriceModifiersController } from './price-modifiers.controller';
import { PriceModifiersService } from './price-modifiers.service';
import { PriceModifierProfile } from './infrastructure/mappers/price-modifier.profile';
import { PriceModifierGroupProfile } from './infrastructure/mappers/price-modifier-group.profile';
import { PriceCalculatorModule } from '@core/pricing/price-calculator.module';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { PriceModifierFilterConfig } from './price-modifier-filter.config';
import { PriceModifierGroupFilterConfig } from './price-modifier-group-filter.config';
import { RelationalPriceModifierPersistenceModule } from './infrastructure/relational-persistence.module';
import { TenantsModule } from '../../user/tenants/tenants.module';

@Module({
  imports: [
    RelationalPriceModifierPersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    PriceCalculatorModule,
    TenantsModule,
  ],
  controllers: [PriceModifiersController],
  providers: [
    PriceModifiersService,
    PriceModifierProfile,
    PriceModifierGroupProfile,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(PriceModifierFilterConfig()),
    },
    {
      provide: SecureFilterService,
      useFactory: () =>
        new SecureFilterService(PriceModifierGroupFilterConfig()),
    },
  ],
  exports: [PriceModifiersService],
})
export class PriceModifiersModule {}
