import { Injectable } from '@nestjs/common';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import { PriceCalculatorService } from '@core/pricing/price-calculator.service';
import {
  ActivePriceSetDto,
  GetActivePriceSetsRequestDto,
} from './dto/get-active-price-sets.dto';
import { IOrder } from '@core/pricing/domain/order.interface';
import { IPriceModifier } from '@core/pricing/domain/price-modifier.interface';
import { AddressService } from '../../customer-portal/address/address.service';
import { ZoneRepository } from '../../zone/zones/infrastructure/repositories/zone.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PriceSetEntity } from '../../pricing/price-sets/infrastructure/entities/price-set.entity';

@Injectable()
export class PricingService {
  constructor(
    private readonly priceSetService: PriceSetsService,
    private readonly priceCalculatorService: PriceCalculatorService,
    private readonly address: AddressService,
    private readonly zoneRepository: ZoneRepository,
    @InjectRepository(PriceSetEntity)
    private readonly priceSetRepository: Repository<PriceSetEntity>,
  ) {}

  /**
   * Get all active price sets for a customer with optional pricing
   */
  async getActivePriceSets(
    customerId: string,
    params: GetActivePriceSetsRequestDto,
    tenantId: string,
  ): Promise<ActivePriceSetDto[]> {
    // Get all active price sets for the customer
    const customerPriceSets =
      await this.priceSetService.getPriceSetsByCustomerId(customerId, tenantId);

    // Map to DTO format
    const priceSets = customerPriceSets.map((priceSet) => ({
      id: priceSet.id,
      name: priceSet.name,
    }));

    // Calculate prices if requested
    if (params.includePricing && params.order) {
      await this.addPricingToPriceSets(priceSets, params.order, tenantId);
    }

    return priceSets;
  }

  /**
   * Add pricing information to the price sets
   */
  private async addPricingToPriceSets(
    priceSets: ActivePriceSetDto[],
    order: IOrder,
    tenantId: string,
  ): Promise<void> {
    for (const priceSet of priceSets) {
      try {
        // Get modifiers for this price set
        const priceModifiers =
          await this.priceSetService.getCompletePriceSetModifiers(priceSet.id);
        // Get zone table for this price set
        await this.priceSetService.getBasePriceByZone(priceSet.id);

        // Get addresses
        const collectionAddress = await this.address.getAddressDetails(
          order.collectionAddressId,
        );
        const deliveryAddress = await this.address.getAddressDetails(
          order.deliveryAddressId,
        );

        // Get postal codes from addresses
        const collectionPostalCode = collectionAddress.postalCode;
        const deliveryPostalCode = deliveryAddress.postalCode;

        // Get zones based on postal codes
        const originZone =
          await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
            collectionPostalCode,
            tenantId,
          );
        const destinationZone =
          await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
            deliveryPostalCode,
            tenantId,
          );

        // Update order with zone IDs if zones were found
        if (originZone && destinationZone) {
          order.originZoneId = originZone.id;
          order.destinationZoneId = destinationZone.id;
        }

        // Convert to IPriceModifier format for calculator
        const modifiersForCalculation: IPriceModifier[] =
          this.mapPriceModifiers(priceModifiers);

        // Create order object with base price from zone if applicable
        const orderWithBasePrice = await this.enrichOrderWithBasePrice(
          order,
          priceSet.id,
        );

        // Calculate price
        const calculationResult = this.priceCalculatorService.calculatePrice(
          orderWithBasePrice,
          modifiersForCalculation,
        );

        // Add to service response
        priceSet.pricing = {
          basePrice: calculationResult.basePrice,
          totalPrice: calculationResult.totalPrice,
          modifiers: calculationResult.modifiers.map((modifier) => ({
            name: modifier.name,
            amount: modifier.amount,
          })),
        };
      } catch {
        // If there's an error calculating price, skip pricing for this price set
        continue;
      }
    }
  }

  /**
   * Map the price modifiers from the repository format to the calculator format
   */
  private mapPriceModifiers(priceModifiers: any[]): IPriceModifier[] {
    return priceModifiers.map((modifier) => ({
      id: modifier.id,
      name: modifier.name,
      calculationType: modifier.calculationType,
      calculationField: modifier.calculationField,
      value: modifier.value,
      increment: modifier.increment,
      calculationStartAfter: modifier.calculationStartAfter,
      applicableRange: modifier.applicableRange,
      tieredRanges: modifier.tieredRanges,
      isGroupModifier: modifier.isGroupModifier || false,
      modifiers: modifier.childModifiers
        ? this.mapPriceModifiers(modifier.childModifiers)
        : undefined,
      groupBehavior: modifier.groupBehavior,
      isEnabled: modifier.isEnabled !== false,
    }));
  }

  /**
   * Add base price to the order based on zones if applicable
   */
  private async enrichOrderWithBasePrice(
    order: IOrder,
    priceSetId: string,
  ): Promise<IOrder> {
    try {
      // If the order already has a base price, use it
      if (order.basePrice && order.basePrice > 0) {
        return order;
      }

      // Try to get zone-based pricing
      if (order.originZoneId && order.destinationZoneId) {
        const zoneTable =
          await this.priceSetService.getBasePriceByZone(priceSetId);

        // Find matching zone value
        const zoneValue = zoneTable.zoneTableValues?.find(
          (value) =>
            value.originZoneId === order.originZoneId &&
            value.destinationZoneId === order.destinationZoneId,
        );

        if (zoneValue && zoneValue.value) {
          return {
            ...order,
            basePrice: zoneValue.value,
          };
        }
      }

      // If no base price found, use a default
      return {
        ...order,
        basePrice: order.basePrice || 0,
      };
    } catch {
      // If there's an error getting zone-based pricing, use the original order
      return order;
    }
  }
}
