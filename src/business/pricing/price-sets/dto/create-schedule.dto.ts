import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  ValidateNested,
  IsBoolean,
  IsArray,
  Min,
  Max,
  IsInt,
  Matches,
  ValidateIf,
  IsObject,
} from 'class-validator';
import {
  AvailabilityType,
  OffsetType,
} from '../../price-sets/domain/price-set.types';

export class OffsetData {
  @ApiProperty({ example: 1 })
  @IsInt()
  @Min(0)
  @IsOptional()
  hours: number | null;

  @ApiProperty({ example: 30 })
  @IsInt()
  @Min(0)
  @Max(59)
  @IsOptional()
  minutes: number | null;

  @ApiProperty({ example: '12:00' })
  @IsString()
  @IsOptional()
  @Matches(/^([01]\d|2[0-3]):[0-5]\d$/, {
    message: 'Time must be in HH:MM format (24-hour clock)',
  })
  time: string | null;

  @ApiProperty({ example: 5 })
  @IsInt()
  @Min(0)
  @IsOptional()
  daysOut: number | null;

  @ApiProperty({ example: true })
  @IsBoolean()
  @IsOptional()
  includeWeekends: boolean | null;
}

export class Schedule {
  @AutoMap()
  @ApiProperty({ example: 'Monday,Wednesday' })
  @Matches(
    /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)(,(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday))*$/,
    {
      message:
        "Days must be a comma-separated list of valid weekdays (e.g., 'Monday,Wednesday')",
    },
  )
  days: string;

  @AutoMap()
  @ApiProperty({ example: '08:00' })
  @IsString()
  @IsNotEmpty()
  startTime: string;

  @AutoMap()
  @ApiProperty({ example: '17:00' })
  @IsString()
  @IsNotEmpty()
  endTime: string;
}

export class CreateScheduleDto {
  @AutoMap()
  @ApiProperty({ enum: AvailabilityType })
  @IsEnum(AvailabilityType)
  availabilityType: AvailabilityType;

  @AutoMap()
  @ApiProperty({ enum: OffsetType })
  @IsEnum(OffsetType)
  @ValidateIf((o) => o.availabilityType !== AvailabilityType.Never)
  offsetType: OffsetType | null;

  @AutoMap()
  @ApiProperty({ type: OffsetData })
  @IsObject()
  @ValidateNested()
  @Type(() => OffsetData)
  @ValidateIf(
    (o) => o.offsetType && o.availabilityType !== AvailabilityType.Never,
  )
  offsetData: OffsetData | null;

  @AutoMap()
  @ApiProperty({ type: [Schedule] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Schedule)
  @ValidateIf((o) => o.availabilityType === AvailabilityType.Weekly)
  schedule: Schedule[] | null;
}
