import { ApiProperty } from '@nestjs/swagger';
import { IsDateString } from 'class-validator';

export class PriceSetParamsDto {
  @ApiProperty({ example: '2023-01-01T12:00:00Z' })
  @IsDateString()
  pickupDate: string;
}

export class PriceSetAvailabilityDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: 'VNP 4 hours Fuel Charges' })
  name: string;

  @ApiProperty()
  deliveryDate: Date;
}

export class GetPriceSetsByDateResponseDto {
  @ApiProperty({ type: [PriceSetAvailabilityDto] })
  data: PriceSetAvailabilityDto[];
}
