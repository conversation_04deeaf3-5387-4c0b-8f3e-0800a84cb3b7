import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
export class GetPriceSetMinimalDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @AutoMap()
  @ApiProperty({ example: 'VNP 4 hours Fuel Charges' })
  name: string;

  @AutoMap()
  @ApiProperty({ example: '4 hours service' })
  internalName: string;
}
export class GetAllPriceSetMinimalDto {
  @ApiProperty({ type: [GetPriceSetMinimalDto] })
  data: GetPriceSetMinimalDto[];
}
