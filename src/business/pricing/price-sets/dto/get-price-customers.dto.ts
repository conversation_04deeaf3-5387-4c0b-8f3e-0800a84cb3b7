import { ApiProperty } from '@nestjs/swagger';

export class CustomerDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: '5f7a7a7a-7a7a-7a7a-7a7a-7a7a7a7a7a7a' })
  customerId: string;

  @ApiProperty({ example: 'Acme Corporation' })
  companyName: string;

  @ApiProperty({ example: 'John Doe' })
  contactName: string;
}

export class GetPriceSetCustomersResponseDto {
  @ApiProperty({ type: [CustomerDto] })
  data: CustomerDto[];
}
