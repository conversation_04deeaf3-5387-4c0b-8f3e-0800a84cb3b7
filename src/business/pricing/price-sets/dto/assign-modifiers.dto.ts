import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, ValidateNested } from 'class-validator';
import { ConfigurationType } from '../domain/price-set.types';
import { AutoMap } from '@automapper/classes';
import { ModifierMemberDto } from '../../price-modifiers/dto/create-price-modifier-group.dto';
import { Type } from 'class-transformer';

export class AssignModifiersDto {
  @AutoMap()
  @ApiProperty({ type: () => [ModifierMemberDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ModifierMemberDto)
  members: ModifierMemberDto[];
}

export class EditConfigurationDto {
  @AutoMap()
  @ApiProperty({ enum: ConfigurationType })
  @IsEnum(ConfigurationType)
  @IsNotEmpty()
  configuration: ConfigurationType;
}
