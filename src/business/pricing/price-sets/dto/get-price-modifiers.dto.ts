import { ApiProperty } from '@nestjs/swagger';
import { ConfigurationType } from '../domain/price-set.types';

export class PriceModifierDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: 'Extra Cleaning Fee' })
  name: string;

  @ApiProperty({ example: '5f7a7a7a-7a7a-7a7a-7a7a-7a7a7a7a7a7a' })
  memberId: string;

  @ApiProperty({ example: false })
  isGroup: boolean;

  @ApiProperty({ enum: ConfigurationType })
  configuration: ConfigurationType;
}

export class GetPriceSetModifiersResponseDto {
  @ApiProperty({ type: [PriceModifierDto] })
  data: PriceModifierDto[];
}
