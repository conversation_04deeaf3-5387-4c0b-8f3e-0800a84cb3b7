import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsEnum } from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';
import { PriceSetPaymentOption } from '../domain/price-set.types';

export class CreatePriceSetDto {
  @AutoMap()
  @ApiProperty({ example: 'VNP 4 hours Fuel Charges' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ example: 'VNP 4 hours Fuel Charges' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  internalName: string;

  @AutoMap()
  @ApiProperty({ enum: PriceSetPaymentOption })
  @IsEnum(PriceSetPaymentOption)
  @IsNotEmpty()
  paymentOption: string;

  @AutoMap()
  @ApiProperty({ example: 'Description' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  description: string;

  @AutoMap()
  @ApiProperty({ default: 'Notes' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  notes: string;
}
