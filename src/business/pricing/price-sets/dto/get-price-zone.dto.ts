import { ApiProperty } from '@nestjs/swagger';

export class GetPriceSetZoneResponseDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: 'Transit app zone table' })
  name: string;

  @ApiProperty({ type: () => [ZoneTableValueDto] })
  zoneTableValues: ZoneTableValueDto[];
}

export class ZoneTableValueDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  originZoneId: string;

  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aadb' })
  destinationZoneId: string;

  @ApiProperty({ example: 33.5 })
  value: number;
}
