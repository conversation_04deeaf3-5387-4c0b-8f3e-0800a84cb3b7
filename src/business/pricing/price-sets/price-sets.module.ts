import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { RelationalPriceSetPersistenceModule } from './infrastructure/relational-persistence.module';
import { PriceSetsController } from './price-sets.controller';
import { PriceSetsService } from './price-sets.service';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { PriceSetProfile } from './infrastructure/mappers/price-set.profile';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { PriceSetFilterConfig } from './price-set-filter.config';
import { TenantsModule } from '../../user/tenants/tenants.module';
import { ZoneTableModule } from '../../zone/zone-tables/zone-table.module';

@Module({
  imports: [
    RelationalPriceSetPersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    TenantsModule,
    ZoneTableModule,
  ],
  controllers: [PriceSetsController],
  providers: [
    PriceSetsService,
    PriceSetProfile,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(PriceSetFilterConfig()),
    },
  ],
  exports: [PriceSetsService],
})
export class PriceSetsModule {}
