import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import {
  PriceSetPaymentOption,
  AvailabilityType,
  OffsetType,
} from '../../domain/price-set.types';

@Entity('price_sets')
export class PriceSetEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @Column()
  name: string;

  @AutoMap()
  @Column()
  internalName: string;

  @AutoMap()
  @Column({ type: 'enum', enum: PriceSetPaymentOption })
  paymentOption: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  description: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  notes: string;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: AvailabilityType,
    nullable: true,
  })
  availabilityType: AvailabilityType;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: OffsetType,
    nullable: true,
  })
  offsetType: OffsetType | null;

  @Column({ type: 'jsonb', nullable: true })
  offsetData: {
    hours: number | null;
    minutes: number | null;
    time: string | null;
    daysOut: number | null;
    includeWeekends: boolean | null;
  } | null;

  @AutoMap()
  @Column({ default: true })
  isActive: boolean;

  @AutoMap()
  @Column({ default: false })
  isDeleted: boolean;

  
  @AutoMap()
  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt: Date;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;
}
