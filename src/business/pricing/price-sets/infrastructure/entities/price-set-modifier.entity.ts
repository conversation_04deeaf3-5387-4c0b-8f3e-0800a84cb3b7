import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { ConfigurationType } from '../../domain/price-set.types';

@Entity('price_set_modifier')
export class PriceSetModifierEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  setId: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  memberId: string;

  @AutoMap()
  @Column()
  isGroup: boolean;

  @Column({
    type: 'enum',
    enum: ConfigurationType,
    default: ConfigurationType.None,
  })
  configuration: ConfigurationType;
}
