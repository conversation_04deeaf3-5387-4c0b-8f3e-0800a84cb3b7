import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';

@Entity('schedule')
export class ScheduleEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  priceSetId: string;

  @AutoMap()
  @Column()
  days: string;

  @AutoMap()
  @Column('time')
  startTime: string;

  @AutoMap()
  @Column('time')
  endTime: string;
}
