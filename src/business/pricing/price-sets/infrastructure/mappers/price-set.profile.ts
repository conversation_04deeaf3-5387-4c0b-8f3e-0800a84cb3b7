import { createMap, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { PriceSetDomain } from '../../domain/price-set';
import { CreatePriceSetDto } from '../../dto/create-price-set.dto';
import { PriceSetEntity } from '../entities/price-set.entity';
import { GetPriceSetDto } from '../../dto/get-price-set.dto';
import { GetPriceSetMinimalDto } from '../../dto/get-price-set-minimal.dto';

@Injectable()
export class PriceSetProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, CreatePriceSetDto, PriceSetDomain);
      createMap(mapper, PriceSetDomain, PriceSetEntity);
      createMap(mapper, PriceSetEntity, PriceSetDomain);
      createMap(mapper, PriceSetDomain, GetPriceSetDto);
      createMap(mapper, PriceSetDomain, GetPriceSetMinimalDto);
    };
  }
}
