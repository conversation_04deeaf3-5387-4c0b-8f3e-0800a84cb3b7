import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PriceSetEntity } from './entities/price-set.entity';
import { PriceSetRepository } from './repositories/price-set.repository';
import { ScheduleEntity } from './entities/schedule.entity';
import { PriceSetModifierEntity } from './entities/price-set-modifier.entity';
import { PriceModifierEntity } from '../../price-modifiers/infrastructure/entities/price-modifier.entity';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';
import { PriceSetCustomerEntity } from './entities/price-set-customer.entity';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { PriceSetFilterConfig } from '../price-set-filter.config';
import { PriceModifierGroupEntity } from '../../price-modifiers/infrastructure/entities/price-modifier-group.entity';
import { ZoneTableValueEntity } from '../../../zone/zone-tables/infrastructure/entities/zone-table-value.entity';
import { ZoneTableEntity } from '../../../zone/zone-tables/infrastructure/entities/zone-table.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PriceSetEntity,
      ScheduleEntity,
      PriceModifierEntity,
      PriceModifierGroupEntity,
      PriceSetModifierEntity,
      UserEntity,
      PriceSetCustomerEntity,
      ZoneTableEntity,
      ZoneTableValueEntity,
    ]),
  ],
  providers: [
    PriceSetRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(PriceSetFilterConfig()),
    },
  ],
  exports: [PriceSetRepository],
})
export class RelationalPriceSetPersistenceModule {}
