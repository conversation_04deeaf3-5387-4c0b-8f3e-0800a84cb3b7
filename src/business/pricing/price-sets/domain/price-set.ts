import { AutoMap } from '@automapper/classes';
import { PriceSetPaymentOption } from './price-set.types';

export class PriceSetDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  name: string;

  @AutoMap()
  internalName: string;

  @AutoMap()
  paymentOption: PriceSetPaymentOption;

  @AutoMap()
  description: string;

  @AutoMap()
  notes: string;

  @AutoMap()
  isActive: boolean;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}
