import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import {
  DEFAULT_TIMEZONE,
  DEFAULT_DATE_FORMAT,
  DEFAULT_TIME_FORMAT,
  DEFAULT_DATETIME_FORMAT,
} from '../constants/timezone.constants';

// Configure dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

/**
 * Converts any date input to a dayjs object in the default timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @returns Dayjs object in the default timezone
 */
export const toTenantTimezone = (
  date: string | Date | dayjs.Dayjs,
): dayjs.Dayjs => {
  return dayjs(date).tz(DEFAULT_TIMEZONE);
};

/**
 * Formats a date to the default date format in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @returns Formatted date string
 */
export const formatDate = (date: string | Date | dayjs.Dayjs): string => {
  return toTenantTimezone(date).format(DEFAULT_DATE_FORMAT);
};

/**
 * Formats a time to the default time format in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @returns Formatted time string
 */
export const formatTime = (date: string | Date | dayjs.Dayjs): string => {
  return toTenantTimezone(date).format(DEFAULT_TIME_FORMAT);
};

/**
 * Formats a datetime to the default datetime format in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @returns Formatted datetime string
 */
export const formatDateTime = (date: string | Date | dayjs.Dayjs): string => {
  return toTenantTimezone(date).format(DEFAULT_DATETIME_FORMAT);
};

/**
 * Gets the day of week name for a date in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @returns Day of week name (e.g., "Monday", "Tuesday", etc.)
 */
export const getDayOfWeek = (date: string | Date | dayjs.Dayjs): string => {
  return toTenantTimezone(date).format('dddd');
};

/**
 * Gets the time portion of a date in HH:mm:ss format in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @returns Time string in HH:mm:ss format
 */
export const getTimeString = (date: string | Date | dayjs.Dayjs): string => {
  return toTenantTimezone(date).format('HH:mm:ss');
};

/**
 * Adds hours and minutes to a date in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @param hours Number of hours to add
 * @param minutes Number of minutes to add
 * @returns Dayjs object with added time
 */
export const addTime = (
  date: string | Date | dayjs.Dayjs,
  hours: number = 0,
  minutes: number = 0,
): dayjs.Dayjs => {
  return toTenantTimezone(date).add(hours, 'hour').add(minutes, 'minute');
};

/**
 * Sets the time of a date in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @param hours Hours to set
 * @param minutes Minutes to set
 * @param seconds Seconds to set
 * @returns Dayjs object with set time
 */
export const setTime = (
  date: string | Date | dayjs.Dayjs,
  hours: number,
  minutes: number,
  seconds: number = 0,
): dayjs.Dayjs => {
  return toTenantTimezone(date).hour(hours).minute(minutes).second(seconds);
};

/**
 * Adds days to a date in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @param days Number of days to add
 * @returns Dayjs object with added days
 */
export const addDays = (
  date: string | Date | dayjs.Dayjs,
  days: number,
): dayjs.Dayjs => {
  return toTenantTimezone(date).add(days, 'day');
};

/**
 * Checks if a date is a weekend (Saturday or Sunday) in the tenant timezone
 *
 * @param date Date input (string, Date object, dayjs object, etc.)
 * @returns True if the date is a weekend, false otherwise
 */
export const isWeekend = (date: string | Date | dayjs.Dayjs): boolean => {
  const day = toTenantTimezone(date).day();
  return day === 0 || day === 6; // 0 is Sunday, 6 is Saturday
};

/**
 * Compares two dates in the tenant timezone
 *
 * @param date1 First date
 * @param date2 Second date
 * @returns -1 if date1 < date2, 0 if date1 = date2, 1 if date1 > date2
 */
export const compareDates = (
  date1: string | Date | dayjs.Dayjs,
  date2: string | Date | dayjs.Dayjs,
): number => {
  const d1 = toTenantTimezone(date1);
  const d2 = toTenantTimezone(date2);

  if (d1.isBefore(d2)) return -1;
  if (d1.isAfter(d2)) return 1;
  return 0;
};
