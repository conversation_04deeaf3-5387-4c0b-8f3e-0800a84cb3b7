import { NestFactory } from '@nestjs/core';
import { SeedModule } from './seed.module';
import { TenantSeedService } from './tenant/tenant-seed.service';
import { VehicleSeedService } from './vehicle/vehicle-seed.service';
import { ZoneSeedService } from './zone/zone-seed.service';
import { ZoneTableSeedService } from './zone-table/zone-table-seed.service';
import { AddressSeedService } from './address/address-seed.service';
import { RbacSeedService } from './rbac/rbac-seed.service';
import { MasterAdminSeedService } from './master-admin/master-admin-seed.service';
import { NotificationTemplateSeedService } from './notification-templates/notification-template-seed.service';

const runSeed = async () => {
  const app = await NestFactory.create(SeedModule);

  await app.get(TenantSeedService).run();
  await app.get(VehicleSeedService).run();
  await app.get(ZoneSeedService).run();
  await app.get(ZoneTableSeedService).run();
  await app.get(AddressSeedService).run();
  await app.get(RbacSeedService).run();
  await app.get(MasterAdminSeedService).run();
  await app.get(NotificationTemplateSeedService).run();

  await app.close();
};

void runSeed();
