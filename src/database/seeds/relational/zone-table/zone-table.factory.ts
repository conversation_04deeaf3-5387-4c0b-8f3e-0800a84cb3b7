import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { ZoneTableEntity } from '@app/business/zone/zone-tables/infrastructure/entities/zone-table.entity';

@Injectable()
export class ZoneTableFactory {
  constructor(
    @InjectRepository(ZoneTableEntity)
    private repository: Repository<ZoneTableEntity>,
  ) {}

  createRandomZoneTable() {
    return () => {
      return this.repository.create({
        tenantId: faker.string.uuid(),
        name: faker.location.city(),
      });
    };
  }
}
