import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { ZoneTableFactory } from './zone-table.factory';
import { ZoneTableEntity } from '@app/business/zone/zone-tables/infrastructure/entities/zone-table.entity';
import { ZoneTableValueFactory } from './zone-table-value.factory';
import { ZoneTableValueEntity } from '@app/business/zone/zone-tables/infrastructure/entities/zone-table-value.entity';

@Injectable()
export class ZoneTableSeedService {
  constructor(
    @InjectRepository(ZoneTableEntity)
    private zoneTableRepository: Repository<ZoneTableEntity>,
    @InjectRepository(ZoneTableValueEntity)
    private zoneTableValueRepository: Repository<ZoneTableValueEntity>,
    private zoneTableFactory: ZoneTableFactory,
    private zoneTableValueFactory: ZoneTableValueFactory,
  ) {}

  async run() {
    const count = await this.zoneTableRepository.count();
    if (count === 0) {
      await this.zoneTableRepository.save(
        faker.helpers.multiple(this.zoneTableFactory.createRandomZoneTable(), {
          count: 10,
        }),
      );
      await this.zoneTableValueRepository.save(
        await this.zoneTableValueFactory.createRandomZoneTableValue(),
      );
    }
  }
}
