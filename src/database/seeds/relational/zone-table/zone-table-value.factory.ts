import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { ZoneTableEntity } from '@app/business/zone/zone-tables/infrastructure/entities/zone-table.entity';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';
import { ZoneTableValueEntity } from '@app/business/zone/zone-tables/infrastructure/entities/zone-table-value.entity';

@Injectable()
export class ZoneTableValueFactory {
  constructor(
    @InjectRepository(ZoneEntity)
    private zoneRepository: Repository<ZoneEntity>,
    @InjectRepository(ZoneTableEntity)
    private zoneTableRepository: Repository<ZoneTableEntity>,
    @InjectRepository(ZoneTableValueEntity)
    private repository: Repository<ZoneTableValueEntity>,
  ) {}

  async createRandomZoneTableValue() {
    const zones = await this.zoneRepository.find();
    const zoneTables = await this.zoneTableRepository.find();

    const zoneTableValues: ZoneTableValueEntity[] = [];

    for (const zoneTable of zoneTables) {
      const generatedValues = faker.helpers.multiple(
        () => this.createZoneTableValue(zones, zoneTable.id),
        { count: 5 },
      );

      zoneTableValues.push(...generatedValues);
    }
    return zoneTableValues;
  }

  private createZoneTableValue(
    zones: ZoneEntity[],
    zoneTableId: string,
  ): ZoneTableValueEntity {
    const originZone = faker.helpers.arrayElement(zones);
    let destinationZone = faker.helpers.arrayElement(zones);

    while (destinationZone.id === originZone.id) {
      destinationZone = faker.helpers.arrayElement(zones);
    }

    return this.repository.create({
      zoneTableId,
      originZoneId: originZone.id,
      destinationZoneId: destinationZone.id,
      value: parseFloat(faker.commerce.price({ min: 10, max: 200, dec: 2 })),
    });
  }
}
