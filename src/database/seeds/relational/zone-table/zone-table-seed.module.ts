import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ZoneTableSeedService } from './zone-table-seed.service';
import { ZoneTableEntity } from '@app/business/zone/zone-tables/infrastructure/entities/zone-table.entity';
import { ZoneTableValueEntity } from '@app/business/zone/zone-tables/infrastructure/entities/zone-table-value.entity';
import { ZoneTableFactory } from './zone-table.factory';
import { ZoneTableValueFactory } from './zone-table-value.factory';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ZoneTableEntity,
      ZoneTableValueEntity,
      ZoneEntity,
    ]),
  ],
  providers: [ZoneTableSeedService, ZoneTableFactory, ZoneTableValueFactory],
})
export class ZoneTableSeedModule {}
