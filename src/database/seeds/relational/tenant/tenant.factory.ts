import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';

@Injectable()
export class TenantFactory {
  constructor(
    @InjectRepository(TenantEntity)
    private repository: Repository<TenantEntity>,
  ) {}

  createRandomTenant() {
    return () => {
      const companyName = faker.company.name();
      const domain = faker.internet.domainName();

      return this.repository.create({
        name: companyName,
        companyUniqueId: faker.helpers.slugify(companyName).toLowerCase(),
        description: faker.company.catchPhrase(),
        status: true,
        settings: {
          theme: faker.helpers.arrayElement(['light', 'dark']),
          language: faker.helpers.arrayElement(['en', 'es', 'fr']),
          notifications: {
            email: faker.datatype.boolean(),
            sms: faker.datatype.boolean(),
          },
          defaultCurrency: faker.helpers.arrayElement(['USD', 'EUR', 'GBP']),
        },
        domain: domain,
        contactEmail: `admin@${domain}`,
        contactPhone: faker.phone.number(),
        address: {
          street: faker.location.street(),
          city: faker.location.city(),
          state: faker.location.state(),
          zip: faker.location.zipCode(),
          country: faker.location.country(),
        },
        logoUrl: faker.image.urlPicsumPhotos(),
        timezone: faker.helpers.arrayElement([
          'UTC',
          'America/New_York',
          'Europe/London',
          'Asia/Tokyo',
        ]),
        metadata: {
          industry: faker.company.buzzPhrase(),
          employeeCount: faker.number.int({ min: 10, max: 1000 }),
          foundedYear: faker.date.past({ years: 20 }).getFullYear(),
          tags: Array.from({ length: 3 }, () => faker.company.buzzNoun()),
        },
        preferences: {
          dashboardLayout: faker.helpers.arrayElement(['grid', 'list']),
          reportsFormat: faker.helpers.arrayElement(['PDF', 'Excel', 'CSV']),
          emailFrequency: faker.helpers.arrayElement([
            'daily',
            'weekly',
            'monthly',
          ]),
        },
        isDeleted: false,
      });
    };
  }
}
