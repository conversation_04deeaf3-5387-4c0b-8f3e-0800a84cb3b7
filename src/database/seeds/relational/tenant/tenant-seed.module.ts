import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantSeedService } from './tenant-seed.service';
import { TenantFactory } from './tenant.factory';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TenantEntity])],
  providers: [TenantSeedService, TenantFactory],
  exports: [TenantSeedService],
})
export class TenantSeedModule {}
