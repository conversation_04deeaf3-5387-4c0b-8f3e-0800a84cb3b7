import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';
import { TenantFactory } from './tenant.factory';
import { faker } from '@faker-js/faker';

@Injectable()
export class TenantSeedService {
  constructor(
    @InjectRepository(TenantEntity)
    private repository: Repository<TenantEntity>,
    private tenantFactory: TenantFactory,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (count === 0) {
      const demoTenant = this.repository.create({
        name: 'Demo Company',
        companyUniqueId: 'demo-company',
        description: 'Main demonstration tenant for testing purposes',
        status: true,
        settings: {
          theme: 'light',
          language: 'en',
          notifications: {
            email: true,
            sms: false,
          },
          defaultCurrency: 'USD',
        },
        domain: 'demo-company.example.com',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0123',
        address: {
          street: '123 Demo Street',
          city: 'Demo City',
          state: 'DS',
          zip: '12345',
          country: 'United States',
        },
        logoUrl: 'https://via.placeholder.com/150',
        timezone: 'UTC',
        metadata: {
          industry: 'Technology',
          employeeCount: 100,
          foundedYear: 2024,
          tags: ['demo', 'test', 'example'],
        },
        preferences: {
          dashboardLayout: 'grid',
          reportsFormat: 'PDF',
          emailFrequency: 'daily',
        },
        isDeleted: false,
      });

      await this.repository.save(demoTenant);

      await this.repository.save(
        faker.helpers.multiple(this.tenantFactory.createRandomTenant(), {
          count: 15,
        }),
      );
    }
  }
}
