import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { MasterAdminEntity } from '@app/admin/master/infrastructure/entities/master-admin.entity';
import { hash } from 'bcrypt';
import { MasterAdminStatus } from '@app/admin/master/domain/master-admin.types';

@Injectable()
export class MasterAdminFactory {
  constructor(
    @InjectRepository(MasterAdminEntity)
    private repository: Repository<MasterAdminEntity>,
  ) {}

  async createDefaultMasterAdmin() {
    const hashedPassword = await hash('Admin@123', 10);

    return this.repository.create({
      name: 'Master Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      emailVerified: true,
      status: MasterAdminStatus.Active,
      loginCount: 0,
      failedAttempts: 0,
      isDeleted: false,
    });
  }
}
