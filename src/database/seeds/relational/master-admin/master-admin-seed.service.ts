import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MasterAdminEntity } from '@app/admin/master/infrastructure/entities/master-admin.entity';
import { MasterAdminFactory } from './master-admin.factory';

@Injectable()
export class MasterAdminSeedService {
  constructor(
    @InjectRepository(MasterAdminEntity)
    private repository: Repository<MasterAdminEntity>,
    private masterAdminFactory: MasterAdminFactory,
  ) {}

  async run() {
    const count = await this.repository.count();
    if (count === 0) {
      const defaultAdmin =
        await this.masterAdminFactory.createDefaultMasterAdmin();
      await this.repository.save(defaultAdmin);
      console.log('Default master admin created');
    }
  }
}
