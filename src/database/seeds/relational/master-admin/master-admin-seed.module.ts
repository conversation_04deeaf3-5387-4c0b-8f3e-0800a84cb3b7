import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MasterAdminEntity } from '@app/admin/master/infrastructure/entities/master-admin.entity';
import { MasterAdminSeedService } from './master-admin-seed.service';
import { MasterAdminFactory } from './master-admin.factory';

@Module({
  imports: [TypeOrmModule.forFeature([MasterAdminEntity])],
  providers: [MasterAdminSeedService, MasterAdminFactory],
})
export class MasterAdminSeedModule {}
