import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { ZoneFactory } from './zone.factory';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';

@Injectable()
export class ZoneSeedService {
  constructor(
    @InjectRepository(ZoneEntity)
    private zoneRepository: Repository<ZoneEntity>,
    private zoneFactory: ZoneFactory,
  ) {}

  async run() {
    const count = await this.zoneRepository.count();
    if (count === 0) {
      await this.zoneRepository.save(
        faker.helpers.multiple(this.zoneFactory.createRandomZone(), {
          count: 10,
        }),
      );
    }
  }
}
