import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';
import { ZoneSeedService } from './zone-seed.service';
import { ZoneFactory } from './zone.factory';

@Module({
  imports: [TypeOrmModule.forFeature([ZoneEntity])],
  providers: [ZoneSeedService, ZoneFactory],
})
export class ZoneSeedModule {}
