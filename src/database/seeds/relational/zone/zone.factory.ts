import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';

@Injectable()
export class ZoneFactory {
  constructor(
    @InjectRepository(ZoneEntity)
    private repository: Repository<ZoneEntity>,
  ) {}

  createRandomZone() {
    return () => {
      return this.repository.create({
        tenantId: faker.string.uuid(),
        name: faker.location.city(),
        postalCodes: Array.from(
          { length: faker.number.int({ min: 1, max: 10 }) },
          () => faker.location.zipCode(),
        ),
        notes: faker.lorem.sentence(),
      });
    };
  }
}
