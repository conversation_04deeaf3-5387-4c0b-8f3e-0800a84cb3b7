import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AddressEntity } from '@app/business/address/addresses/infrastructure/entities/address.entity';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { AddressSeedService } from './address-seed.service';
import { AddressFactory } from './address.factory';

@Module({
  imports: [TypeOrmModule.forFeature([AddressEntity, UserEntity])],
  providers: [AddressSeedService, AddressFactory],
})
export class AddressSeedModule {}
