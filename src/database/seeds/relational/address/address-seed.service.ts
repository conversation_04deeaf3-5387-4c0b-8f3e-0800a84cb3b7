import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { AddressEntity } from '@app/business/address/addresses/infrastructure/entities/address.entity';
import { AddressFactory } from './address.factory';

@Injectable()
export class AddressSeedService {
  constructor(
    @InjectRepository(AddressEntity)
    private addressRepository: Repository<AddressEntity>,
    private addressFactory: AddressFactory,
  ) {}

  async run() {
    const count = await this.addressRepository.count();
    if (count === 0) {
      await this.addressRepository.save(
        faker.helpers.multiple(
          await this.addressFactory.createRandomAddress(),
          {
            count: 10,
          },
        ),
      );
    }
  }
}
