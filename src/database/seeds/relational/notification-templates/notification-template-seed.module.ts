import { Module } from '@nestjs/common';
import { NotificationRelationalPersistenceModule } from '../../../../core/notification/infrastructure/relational-persistence.module';
import { NotificationTemplateSeedService } from './notification-template-seed.service';

@Module({
  imports: [NotificationRelationalPersistenceModule],
  providers: [NotificationTemplateSeedService],
  exports: [NotificationTemplateSeedService],
})
export class NotificationTemplateSeedModule {}
