import { Injectable, Logger } from '@nestjs/common';
import { NotificationTemplateRepository } from '../../../../core/notification/infrastructure/repositories/notification-template.repository';
import {
  NotificationChannel,
  NotificationType,
} from '../../../../core/notification/types/notification.types';

@Injectable()
export class NotificationTemplateSeedService {
  private readonly logger = new Logger(NotificationTemplateSeedService.name);

  // Map template names to their types
  private readonly templateTypeMap = {
    'order-created-email': NotificationType.ORDER_CREATED,
    'order-created-sms': NotificationType.ORDER_CREATED,
    'order-status-changed-email': NotificationType.ORDER_STATUS_CHANGED,
    'order-status-changed-sms': NotificationType.ORDER_STATUS_CHANGED,
    'order-assigned-email': NotificationType.ORDER_ASSIGNED,
    'order-assigned-sms': NotificationType.ORDER_ASSIGNED,
    'order-started-email': NotificationType.ORDER_STARTED,
    'order-started-sms': NotificationType.ORDER_STARTED,
    'order-completed-email': NotificationType.ORDER_COMPLETED,
    'order-completed-sms': NotificationType.ORDER_COMPLETED,
    'driver-assigned-email': NotificationType.DRIVER_ASSIGNED,
    'driver-assigned-sms': NotificationType.DRIVER_ASSIGNED,
    'delivery-reminder-email': NotificationType.DELIVERY_REMINDER,
    'delivery-reminder-sms': NotificationType.DELIVERY_REMINDER,
  };

  // Map for email subjects
  private readonly subjectMap = {
    [NotificationType.ORDER_CREATED]: 'Your Order Confirmation',
    [NotificationType.ORDER_ASSIGNED]: 'Your Order Has Been Assigned',
    [NotificationType.ORDER_STARTED]: 'Your Order Is In Progress',
    [NotificationType.ORDER_COMPLETED]: 'Your Order Has Been Completed',
    [NotificationType.ORDER_STATUS_CHANGED]: 'Order Status Update',
    [NotificationType.DRIVER_ASSIGNED]: 'Driver Assignment Notification',
    [NotificationType.DELIVERY_REMINDER]: 'Upcoming Delivery Reminder',
  };

  // Template content
  private readonly templates = {
    'order-created-email': `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Confirmation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .order-details {
      margin-bottom: 30px;
    }
    .order-details table {
      width: 100%;
      border-collapse: collapse;
    }
    .order-details th, .order-details td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: left;
    }
    .order-details th {
      background-color: #f5f5f5;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order Confirmation</h1>
      <p>Thank you for your order!</p>
    </div>

    <div class="order-details">
      <h2>Order Details</h2>
      <p><strong>Order Number:</strong> {{order.trackingNumber}}</p>
      <p><strong>Order Date:</strong> {{formatDate order.createdAt}}</p>

      <h3>Collection Details</h3>
      <p><strong>Address:</strong> {{order.collection.address}}</p>
      <p><strong>Contact:</strong> {{order.collection.contactName}}</p>
      <p><strong>Phone:</strong> {{order.collection.contactPhone}}</p>
      <p><strong>Date:</strong> {{formatDate order.collection.date}}</p>
      <p><strong>Time Window:</strong> {{order.collection.timeWindow}}</p>

      <h3>Delivery Details</h3>
      <p><strong>Address:</strong> {{order.delivery.address}}</p>
      <p><strong>Contact:</strong> {{order.delivery.contactName}}</p>
      <p><strong>Phone:</strong> {{order.delivery.contactPhone}}</p>
      <p><strong>Date:</strong> {{formatDate order.delivery.date}}</p>
      <p><strong>Time Window:</strong> {{order.delivery.timeWindow}}</p>

      {{#if order.items.length}}
      <h3>Items</h3>
      <table>
        <thead>
          <tr>
            <th>Description</th>
            <th>Quantity</th>
            <th>Weight</th>
            <th>Dimensions</th>
          </tr>
        </thead>
        <tbody>
          {{#each order.items}}
          <tr>
            <td>{{this.description}}</td>
            <td>{{this.quantity}}</td>
            <td>{{this.weight}} {{this.weightUnit}}</td>
            <td>{{this.dimensions}}</td>
          </tr>
          {{/each}}
        </tbody>
      </table>
      {{/if}}

      <h3>Special Instructions</h3>
      <p>{{#if order.specialInstructions}}{{order.specialInstructions}}{{else}}None{{/if}}</p>
    </div>

    <p>You can track your order status by visiting your customer portal or contacting customer service.</p>

    <div class="footer">
      <p>&copy; {{currentYear}} Transport App. All rights reserved.</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>`,
    'order-created-sms':
      'Your order #{{order.trackingNumber}} has been confirmed. Collection: {{formatDate order.collection.date}} ({{order.collection.timeWindow}}). Delivery: {{formatDate order.delivery.date}} ({{order.delivery.timeWindow}}). Track your order on our website.',
    'order-status-changed-email': `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Status Update</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status-update {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .order-details {
      margin-bottom: 30px;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order Status Update</h1>
    </div>

    <div class="status-update">
      <h2>Status Change</h2>
      <p>Your order #{{order.trackingNumber}} status has been updated to: <strong>{{order.currentStatus}}</strong></p>
      {{#if order.statusNotes}}
        <p><strong>Notes:</strong> {{order.statusNotes}}</p>
      {{/if}}
    </div>

    <div class="order-details">
      <h2>Order Details</h2>
      <p><strong>Order Number:</strong> {{order.trackingNumber}}</p>
      <p><strong>Order Date:</strong> {{formatDate order.createdAt}}</p>
      
      <h3>Collection Details</h3>
      <p><strong>Address:</strong> {{order.collection.address}}</p>
      <p><strong>Date:</strong> {{formatDate order.collection.date}}</p>
      
      <h3>Delivery Details</h3>
      <p><strong>Address:</strong> {{order.delivery.address}}</p>
      <p><strong>Date:</strong> {{formatDate order.delivery.date}}</p>
    </div>

    <p>For more details, please visit your customer portal or contact our customer service team.</p>

    <div class="footer">
      <p>&copy; {{currentYear}} Transport App. All rights reserved.</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>`,
    'order-status-changed-sms':
      'Your order #{{order.trackingNumber}} status has been updated to {{order.currentStatus}}. {{#if order.statusNotes}}Note: {{order.statusNotes}}{{/if}}',
    'order-assigned-email': `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Assignment</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .driver-info {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .order-details {
      margin-bottom: 30px;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Driver Assignment</h1>
    </div>

    <div class="driver-info">
      <h2>Driver Information</h2>
      <p>Your order #{{order.trackingNumber}} has been assigned to:</p>
      <p><strong>Driver:</strong> {{driver.name}}</p>
      <p><strong>Vehicle:</strong> {{driver.vehicle}}</p>
      {{#if driver.contactNumber}}
        <p><strong>Contact:</strong> {{driver.contactNumber}}</p>
      {{/if}}
    </div>

    <div class="order-details">
      <h2>Order Details</h2>
      <p><strong>Order Number:</strong> {{order.trackingNumber}}</p>
      <p><strong>Order Date:</strong> {{formatDate order.createdAt}}</p>
      
      <h3>Collection Details</h3>
      <p><strong>Address:</strong> {{order.collection.address}}</p>
      <p><strong>Date:</strong> {{formatDate order.collection.date}}</p>
      
      <h3>Delivery Details</h3>
      <p><strong>Address:</strong> {{order.delivery.address}}</p>
      <p><strong>Date:</strong> {{formatDate order.delivery.date}}</p>
    </div>

    <p>For more details, please visit your customer portal or contact our customer service team.</p>

    <div class="footer">
      <p>&copy; {{currentYear}} Transport App. All rights reserved.</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>`,
    'order-assigned-sms':
      'Your order #{{order.trackingNumber}} has been assigned to {{driver.name}}. For any questions, please contact our customer service.',
    'driver-assigned-email': `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New Order Assignment</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .order-details {
      margin-bottom: 30px;
    }
    .important {
      background-color: #fff3cd;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>New Order Assignment</h1>
    </div>

    <div class="important">
      <h2>New Order Assigned</h2>
      <p>You have been assigned order #{{order.trackingNumber}}.</p>
      <p>Please check your driver app for full details.</p>
    </div>

    <div class="order-details">
      <h2>Order Details</h2>
      <p><strong>Order Number:</strong> {{order.trackingNumber}}</p>
      
      <h3>Collection Details</h3>
      <p><strong>Address:</strong> {{order.collection.address}}</p>
      <p><strong>Contact:</strong> {{order.collection.contactName}}</p>
      <p><strong>Date:</strong> {{formatDate order.collection.date}}</p>
      <p><strong>Time Window:</strong> {{order.collection.timeWindow}}</p>
      
      <h3>Delivery Details</h3>
      <p><strong>Address:</strong> {{order.delivery.address}}</p>
      <p><strong>Contact:</strong> {{order.delivery.contactName}}</p>
      <p><strong>Date:</strong> {{formatDate order.delivery.date}}</p>
      <p><strong>Time Window:</strong> {{order.delivery.timeWindow}}</p>
    </div>

    <p>Please log into your driver app for more details.</p>

    <div class="footer">
      <p>&copy; {{currentYear}} Transport App. All rights reserved.</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>`,
    'driver-assigned-sms':
      'New order #{{order.trackingNumber}} has been assigned to you. Collection: {{formatDate order.collection.date}}. Delivery: {{formatDate order.delivery.date}}. Please check your app for details.',
    'order-started-email': `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order In Progress</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status-update {
      background-color: #d1e7dd;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .order-details {
      margin-bottom: 30px;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order In Progress</h1>
    </div>

    <div class="status-update">
      <h2>Your Order Is On The Way</h2>
      <p>Your order #{{order.trackingNumber}} is now in progress. Our driver has collected your items and is on the way to the delivery location.</p>
      {{#if driver}}
        <p><strong>Driver:</strong> {{driver.name}}</p>
        {{#if driver.contactNumber}}
          <p><strong>Contact:</strong> {{driver.contactNumber}}</p>
        {{/if}}
      {{/if}}
    </div>

    <div class="order-details">
      <h2>Order Details</h2>
      <p><strong>Order Number:</strong> {{order.trackingNumber}}</p>
      
      <h3>Collection Details</h3>
      <p><strong>Address:</strong> {{order.collection.address}}</p>
      <p><strong>Date:</strong> {{formatDate order.collection.date}}</p>
      
      <h3>Delivery Details</h3>
      <p><strong>Address:</strong> {{order.delivery.address}}</p>
      <p><strong>Date:</strong> {{formatDate order.delivery.date}}</p>
      <p><strong>Contact:</strong> {{order.delivery.contactName}}</p>
    </div>

    <p>For more details, please visit your customer portal or contact our customer service team.</p>

    <div class="footer">
      <p>&copy; {{currentYear}} Transport App. All rights reserved.</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>`,
    'order-started-sms':
      'Your order #{{order.trackingNumber}} is now in progress. Our driver has collected your items and is on the way to the delivery location.',
    'order-completed-email': `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Completed</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status-update {
      background-color: #d1e7dd;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .order-details {
      margin-bottom: 30px;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order Completed</h1>
    </div>

    <div class="status-update">
      <h2>Your Order Has Been Delivered</h2>
      <p>Good news! Your order #{{order.trackingNumber}} has been successfully delivered.</p>
    </div>

    <div class="order-details">
      <h2>Order Details</h2>
      <p><strong>Order Number:</strong> {{order.trackingNumber}}</p>
      <p><strong>Delivery Date:</strong> {{formatDate order.actualDeliveryTime}}</p>
      
      <h3>Delivery Address</h3>
      <p>{{order.delivery.address}}</p>
    </div>

    <p>Thank you for choosing our service. We hope you are satisfied with the delivery. If you have any feedback, please visit your customer portal or contact our customer service team.</p>

    <div class="footer">
      <p>&copy; {{currentYear}} Transport App. All rights reserved.</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>`,
    'order-completed-sms':
      'Good news! Your order #{{order.trackingNumber}} has been successfully delivered. Thank you for choosing our service.',
    'delivery-reminder-email': `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Delivery Reminder</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .reminder {
      background-color: #cfe2ff;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .order-details {
      margin-bottom: 30px;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Delivery Reminder</h1>
    </div>

    <div class="reminder">
      <h2>Upcoming Delivery</h2>
      <p>This is a reminder that your order #{{order.trackingNumber}} is scheduled for delivery soon.</p>
      <p><strong>Scheduled Delivery:</strong> {{formatDate order.scheduledDeliveryTime}}</p>
    </div>

    <div class="order-details">
      <h2>Order Details</h2>
      <p><strong>Order Number:</strong> {{order.trackingNumber}}</p>
      
      <h3>Delivery Details</h3>
      <p><strong>Address:</strong> {{order.delivery.address}}</p>
      <p><strong>Contact:</strong> {{order.delivery.contactName}}</p>
      <p><strong>Phone:</strong> {{order.delivery.contactPhone}}</p>
    </div>

    <p>Please ensure someone is available to receive the delivery. If you need to make any changes, please contact our customer service team as soon as possible.</p>

    <div class="footer">
      <p>&copy; {{currentYear}} Transport App. All rights reserved.</p>
      <p>This is an automated message, please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>`,
    'delivery-reminder-sms':
      'REMINDER: Your order #{{order.trackingNumber}} is scheduled for delivery on {{formatDate order.scheduledDeliveryTime}}. Please ensure someone is available to receive it.',
  };

  constructor(
    private readonly notificationTemplateRepository: NotificationTemplateRepository,
  ) {}

  async run(): Promise<void> {
    this.logger.log('Seeding notification templates...');

    try {
      // Process each template
      for (const [templateName, content] of Object.entries(this.templates)) {
        // Extract template type and channel
        const type = this.templateTypeMap[templateName];
        const channel = templateName.endsWith('-email')
          ? NotificationChannel.EMAIL
          : NotificationChannel.SMS;

        // Only email templates need a subject
        const subject =
          channel === NotificationChannel.EMAIL ? this.subjectMap[type] : null;

        // Check if template already exists
        const existingTemplate =
          await this.notificationTemplateRepository.findByName(templateName);

        if (!existingTemplate) {
          // Create the template
          await this.notificationTemplateRepository.create({
            name: templateName,
            type,
            channel,
            content,
            subject,
            isActive: true,
            description: `Template for ${type} notifications via ${channel}`,
          });

          this.logger.log(`Created template: ${templateName}`);
        } else {
          this.logger.log(`Template already exists: ${templateName}`);
        }
      }

      this.logger.log('Notification template seeding completed');
    } catch (error) {
      this.logger.error('Error seeding notification templates', error);
      throw error;
    }
  }
}
