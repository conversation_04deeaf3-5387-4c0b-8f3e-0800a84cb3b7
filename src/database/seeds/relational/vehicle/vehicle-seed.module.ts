import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { VehicleTypeEntity } from '@app/business/vehicle/vehicle-types/infrastructure/entities/vehicle-type.entity';
import { TimeClockSessionEntity } from '@app/business/vehicle/time-clock-session/infrastructure/entities/time-clock-session.entity';
import { VehicleSeedService } from './vehicle-seed.service';
import { VehicleFactory } from './vehicle.factory';
import { VehicleTypeFactory } from './vehicle-type.factory';
import { TimeClockSessionFactory } from './time-clock-session.factory';
import { UserEntity } from '../../../../business/user/users/infrastructure/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      VehicleEntity,
      VehicleTypeEntity,
      TimeClockSessionEntity,
      UserEntity,
    ]),
  ],
  providers: [
    VehicleSeedService,
    VehicleFactory,
    VehicleTypeFactory,
    TimeClockSessionFactory,
  ],
})
export class VehicleSeedModule {}
