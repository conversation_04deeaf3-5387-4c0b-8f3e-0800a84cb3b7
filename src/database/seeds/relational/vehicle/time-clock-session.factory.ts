import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { TimeClockSessionEntity } from '@app/business/vehicle/time-clock-session/infrastructure/entities/time-clock-session.entity';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { TimeClockSessionSource } from '@app/business/vehicle/time-clock-session/domain/time-clock-session.types';
import { UserEntity } from '../../../../business/user/users/infrastructure/entities/user.entity';

@Injectable()
export class TimeClockSessionFactory {
  constructor(
    @InjectRepository(TimeClockSessionEntity)
    private repository: Repository<TimeClockSessionEntity>,
    @InjectRepository(VehicleEntity)
    private vehicleRepository: Repository<VehicleEntity>,
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
  ) {}

  async createRandomTimeClockSession() {
    const vehicles = await this.vehicleRepository.find();
    const drivers = await this.userRepository.find();
    return () => {
      const randomVehicle =
        vehicles[Math.floor(Math.random() * vehicles.length)];
      return this.repository.create({
        tenantId: faker.string.uuid(),
        vehicleId: randomVehicle?.id,
        driverId: faker.helpers.arrayElement(drivers)?.id,
        distanceTraveled: faker.number.int({ min: 1, max: 1000 }),
        startTime: faker.date.past().toISOString(),
        endTime: faker.date.future().toISOString(),
        source: faker.helpers.arrayElement(
          Object.values(TimeClockSessionSource),
        ),
        addedBy: faker.person.firstName(),
      });
    };
  }
}
