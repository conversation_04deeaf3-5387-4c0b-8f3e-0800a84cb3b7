import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { VehicleTypeEntity } from '@app/business/vehicle/vehicle-types/infrastructure/entities/vehicle-type.entity';

@Injectable()
export class VehicleFactory {
  constructor(
    @InjectRepository(VehicleTypeEntity)
    private vehicleTypeRepository: Repository<VehicleTypeEntity>,
    @InjectRepository(VehicleEntity)
    private repository: Repository<VehicleEntity>,
  ) {}

  async createRandomVehicle() {
    const vehicleTypes = await this.vehicleTypeRepository.find();
    return () => {
      const randomVehicleType =
        vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)];

      return this.repository.create({
        tenantId: faker.string.uuid(),
        vehicleTypeId: randomVehicleType?.id,
        fleetId: faker.vehicle.vrm().toUpperCase(),
        make: faker.vehicle.manufacturer(),
        model: faker.vehicle.model(),
        year: faker.date
          .between({ from: '2000-01-01', to: new Date() })
          .getFullYear(),
        licensePlate: faker.vehicle.vrm(),
        vin: faker.vehicle.vin(),
        packageType: Array.from({ length: 2 }, () => faker.commerce.product()),
        maxWeight: faker.number.int({ min: 500, max: 5000 }),
        branch: `${faker.location.city()} Branch`,
        currentOdometer: faker.number.int({ min: 0, max: 300000 }),
        ownedBy: faker.person.firstName(),
        notes: faker.lorem.sentence(),
      });
    };
  }
}
