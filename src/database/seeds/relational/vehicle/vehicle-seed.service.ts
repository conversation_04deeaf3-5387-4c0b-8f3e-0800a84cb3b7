import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { VehicleFactory } from './vehicle.factory';
import { VehicleTypeFactory } from './vehicle-type.factory';
import { VehicleTypeEntity } from '@app/business/vehicle/vehicle-types/infrastructure/entities/vehicle-type.entity';
import { TimeClockSessionEntity } from '@app/business/vehicle/time-clock-session/infrastructure/entities/time-clock-session.entity';
import { TimeClockSessionFactory } from './time-clock-session.factory';

@Injectable()
export class VehicleSeedService {
  constructor(
    @InjectRepository(VehicleEntity)
    private vehicleRepository: Repository<VehicleEntity>,
    @InjectRepository(VehicleTypeEntity)
    private vehicleTypeRepository: Repository<VehicleTypeEntity>,
    @InjectRepository(TimeClockSessionEntity)
    private timeClockSessionRepository: Repository<TimeClockSessionEntity>,
    private vehicleTypeFactory: VehicleTypeFactory,
    private vehicleFactory: VehicleFactory,
    private timeClockSessionFactory: TimeClockSessionFactory,
  ) {}

  async run() {
    await this.vehicleTypeRepository.save(
      faker.helpers.multiple(
        this.vehicleTypeFactory.createRandomVehicleType(),
        { count: 10 },
      ),
    );
    await this.vehicleRepository.save(
      faker.helpers.multiple(await this.vehicleFactory.createRandomVehicle(), {
        count: 100,
      }),
    );
    await this.timeClockSessionRepository.save(
      faker.helpers.multiple(
        await this.timeClockSessionFactory.createRandomTimeClockSession(),
        { count: 100 },
      ),
    );
  }
}
