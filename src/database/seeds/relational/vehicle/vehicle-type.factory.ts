import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { VehicleTypeEntity } from '@app/business/vehicle/vehicle-types/infrastructure/entities/vehicle-type.entity';

@Injectable()
export class VehicleTypeFactory {
  constructor(
    @InjectRepository(VehicleTypeEntity)
    private repository: Repository<VehicleTypeEntity>,
  ) {}

  createRandomVehicleType() {
    return () => {
      return this.repository.create({
        tenantId: faker.string.uuid(),
        name: faker.vehicle.type(),
        description: faker.commerce.productDescription(),
      });
    };
  }
}
