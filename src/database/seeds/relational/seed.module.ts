import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DataSource, DataSourceOptions } from 'typeorm';
import { TypeOrmConfigService } from '../../typeorm-config.service';
import databaseConfig from '../../config/database.config';
import appConfig from '../../../config/app.config';
import { TenantSeedModule } from './tenant/tenant-seed.module';
import { VehicleSeedModule } from './vehicle/vehicle-seed.module';
import { ZoneSeedModule } from './zone/zone-seed.module';
import { ZoneTableSeedModule } from './zone-table/zone-table-seed.module';
import { AddressSeedModule } from './address/address-seed.module';
import { RbacSeedModule } from './rbac/rbac-seed.module';
import { MasterAdminSeedModule } from './master-admin/master-admin-seed.module';
import { NotificationTemplateSeedModule } from './notification-templates/notification-template-seed.module';

@Module({
  imports: [
    TenantSeedModule,
    VehicleSeedModule,
    ZoneSeedModule,
    ZoneTableSeedModule,
    AddressSeedModule,
    RbacSeedModule,
    MasterAdminSeedModule,
    NotificationTemplateSeedModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, appConfig],
      envFilePath: ['.env'],
    }),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
      dataSourceFactory: async (options: DataSourceOptions) => {
        return new DataSource(options).initialize();
      },
    }),
  ],
})
export class SeedModule {}
