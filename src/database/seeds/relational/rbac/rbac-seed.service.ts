import { Injectable } from '@nestjs/common';
import { RoleRepository } from '@core/rbac/infrastructure/repositories/role.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { TenantEntity } from '../../../../business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';

@Injectable()
export class RbacSeedService {
  constructor(
    private readonly roleRepository: RoleRepository,
    @InjectRepository(TenantEntity)
    private readonly tenantRepository: Repository<TenantEntity>,
  ) {}

  async run() {
    const tenant = await this.tenantRepository.find();
    const tenantId = faker.helpers.arrayElement(tenant).id;
    const admin = {
      name: 'admin',
      isSystemRole: true,
      permissionGroups: [
        {
          name: 'customer',
          permissions: [],
          subGroups: [
            {
              name: 'customers',
              permissions: [
                {
                  name: 'customers.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'customers.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'customers.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'customers.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'partner',
              permissions: [
                {
                  name: 'partner.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partner.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partner.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partner.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'billing',
              permissions: [
                {
                  name: 'billing.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'billing.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'billing.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'billing.view',
                  isPermissionAllowed: true,
                },
              ],
            },
          ],
        },
        {
          name: 'logistic',
          permissions: [],
          subGroups: [
            {
              name: 'orders',
              permissions: [
                {
                  name: 'orders.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'orders.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'orders.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'orders.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'dispatcher',
              permissions: [
                {
                  name: 'dispatcher.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'dispatcher.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'dispatcher.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'dispatcher.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'vehicle',
              permissions: [
                {
                  name: 'vehicle.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'vehicle.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'vehicle.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'vehicle.view',
                  isPermissionAllowed: true,
                },
              ],
            },
          ],
        },
        {
          name: 'location',
          permissions: [],
          subGroups: [
            {
              name: 'address',
              permissions: [
                {
                  name: 'address.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'address.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'address.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'address.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'zone',
              permissions: [
                {
                  name: 'zone.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'zone.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'zone.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'zone.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'zoneTable',
              permissions: [
                {
                  name: 'zoneTable.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'zoneTable.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'zoneTable.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'zoneTable.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'routes',
              permissions: [
                {
                  name: 'routes.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'routes.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'routes.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'routes.view',
                  isPermissionAllowed: true,
                },
              ],
            },
          ],
        },
        {
          name: 'prices',
          permissions: [],
          subGroups: [
            {
              name: 'priceSets',
              permissions: [
                {
                  name: 'priceSets.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'priceSets.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'priceSets.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'priceSets.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'priceModifiers',
              permissions: [
                {
                  name: 'priceModifiers.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'priceModifiers.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'priceModifiers.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'priceModifiers.view',
                  isPermissionAllowed: true,
                },
              ],
            },
          ],
        },
        {
          name: 'settings',
          permissions: [],
          subGroups: [
            {
              name: 'general',
              permissions: [
                {
                  name: 'general.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'general.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'general.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'general.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'partners',
              permissions: [
                {
                  name: 'partners.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partners.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partners.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partners.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'templates',
              permissions: [
                {
                  name: 'templates.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'templates.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'templates.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'templates.view',
                  isPermissionAllowed: true,
                },
              ],
            },
          ],
        },
      ],
    };

    const driver = {
      name: 'driver',
      isSystemRole: true,
      permissionGroups: [
        {
          name: 'customer',
          permissions: [],
          subGroups: [
            {
              name: 'customers',
              permissions: [
                {
                  name: 'customers.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'customers.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'customers.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'customers.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'partner',
              permissions: [
                {
                  name: 'partner.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'partner.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'partner.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'partner.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'billing',
              permissions: [
                {
                  name: 'billing.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'billing.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'billing.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'billing.view',
                  isPermissionAllowed: false,
                },
              ],
            },
          ],
        },
        {
          name: 'logistic',
          permissions: [],
          subGroups: [
            {
              name: 'orders',
              permissions: [
                {
                  name: 'orders.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'orders.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'orders.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'orders.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'dispatcher',
              permissions: [
                {
                  name: 'dispatcher.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'dispatcher.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'dispatcher.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'dispatcher.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'vehicle',
              permissions: [
                {
                  name: 'vehicle.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'vehicle.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'vehicle.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'vehicle.view',
                  isPermissionAllowed: false,
                },
              ],
            },
          ],
        },
        {
          name: 'location',
          permissions: [],
          subGroups: [
            {
              name: 'address',
              permissions: [
                {
                  name: 'address.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'address.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'address.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'address.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'zone',
              permissions: [
                {
                  name: 'zone.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'zone.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'zone.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'zone.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'zoneTable',
              permissions: [
                {
                  name: 'zoneTable.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'zoneTable.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'zoneTable.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'zoneTable.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'routes',
              permissions: [
                {
                  name: 'routes.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'routes.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'routes.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'routes.view',
                  isPermissionAllowed: false,
                },
              ],
            },
          ],
        },
        {
          name: 'prices',
          permissions: [],
          subGroups: [
            {
              name: 'priceSets',
              permissions: [
                {
                  name: 'priceSets.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'priceSets.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'priceSets.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'priceSets.view',
                  isPermissionAllowed: false,
                },
              ],
            },
            {
              name: 'priceModifiers',
              permissions: [
                {
                  name: 'priceModifiers.create',
                  isPermissionAllowed: false,
                },
                {
                  name: 'priceModifiers.update',
                  isPermissionAllowed: false,
                },
                {
                  name: 'priceModifiers.delete',
                  isPermissionAllowed: false,
                },
                {
                  name: 'priceModifiers.view',
                  isPermissionAllowed: false,
                },
              ],
            },
          ],
        },
        {
          name: 'settings',
          permissions: [],
          subGroups: [
            {
              name: 'general',
              permissions: [
                {
                  name: 'general.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'general.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'general.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'general.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'partners',
              permissions: [
                {
                  name: 'partners.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partners.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partners.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'partners.view',
                  isPermissionAllowed: true,
                },
              ],
            },
            {
              name: 'templates',
              permissions: [
                {
                  name: 'templates.create',
                  isPermissionAllowed: true,
                },
                {
                  name: 'templates.update',
                  isPermissionAllowed: true,
                },
                {
                  name: 'templates.delete',
                  isPermissionAllowed: true,
                },
                {
                  name: 'templates.view',
                  isPermissionAllowed: true,
                },
              ],
            },
          ],
        },
      ],
    };

    await this.roleRepository.create(tenantId, admin);
    await this.roleRepository.create(tenantId, driver);
  }
}
