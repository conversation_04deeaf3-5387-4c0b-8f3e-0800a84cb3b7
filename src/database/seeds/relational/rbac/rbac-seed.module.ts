import { Module } from '@nestjs/common';
import { RbacSeedService } from './rbac-seed.service';
import { RelationalRbacPersistenceModule } from '../../../../core/rbac/infrastructure/relational-persistence.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantEntity } from '../../../../business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([TenantEntity]),
    RelationalRbacPersistenceModule,
  ],
  providers: [RbacSeedService, RelationalRbacPersistenceModule],
})
export class RbacSeedModule {}
