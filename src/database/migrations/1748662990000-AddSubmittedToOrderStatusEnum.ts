import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSubmittedToOrderStatusEnum1748662990000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the 'Submitted' value to the order_status enum type
    await queryRunner.query(`
      ALTER TYPE "order_status_enum" ADD VALUE IF NOT EXISTS 'Submitted' AFTER 'Draft';
    `);
  }

  public down(): Promise<void> {
    // PostgreSQL doesn't support removing enum values directly,
    // so we would need to create a new type, update the column, and drop the old type
    // This is not implemented as it's complex and rarely needed
    console.log('Cannot remove enum value in down migration. Skipping.');
    return Promise.resolve();
  }
}
