import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMasterAdminTable1747309807353 implements MigrationInterface {
  name = 'CreateMasterAdminTable1747309807353';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create master_admin_status enum
    await queryRunner.query(`
            CREATE TYPE "public"."master_admin_status_enum" AS ENUM('Active', 'Inactive', 'Suspended');
        `);

    // Create master_admins table
    await queryRunner.query(`
            CREATE TABLE "master_admins" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying(100) NOT NULL,
                "email" character varying(255) NOT NULL,
                "password" character varying(255) NOT NULL,
                "email_verified" boolean NOT NULL DEFAULT false,
                "status" "public"."master_admin_status_enum" NOT NULL DEFAULT 'Active',
                "last_login_at" TIMESTAMP WITH TIME ZONE,
                "login_count" integer NOT NULL DEFAULT 0,
                "failed_attempts" integer NOT NULL DEFAULT 0,
                "locked_until" TIMESTAMP WITH TIME ZONE,
                "is_deleted" boolean NOT NULL DEFAULT false,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "pk_master_admins" PRIMARY KEY ("id")
            );
        `);

    // Create index on email
    await queryRunner.query(`
            CREATE UNIQUE INDEX "idx_master_admins_email" ON "master_admins" ("email");
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index
    await queryRunner.query(`
            DROP INDEX "idx_master_admins_email";
        `);

    // Drop table
    await queryRunner.query(`
            DROP TABLE "master_admins";
        `);

    // Drop enum
    await queryRunner.query(`
            DROP TYPE "public"."master_admin_status_enum";
        `);
  }
}
