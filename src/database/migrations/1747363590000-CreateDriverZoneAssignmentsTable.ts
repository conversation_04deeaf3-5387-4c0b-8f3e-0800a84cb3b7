import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDriverZoneAssignmentsTable1747363590000
  implements MigrationInterface
{
  name = 'CreateDriverZoneAssignmentsTable1747363590000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "driver_zone_assignments" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "tenant_id" uuid NOT NULL,
        "driver_id" uuid NOT NULL,
        "zone_id" uuid NOT NULL,
        "is_deleted" boolean NOT NULL DEFAULT false,
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "created_by" uuid,
        "updated_by" uuid,
        CONSTRAINT "PK_driver_zone_assignments" PRIMARY KEY ("id")
      )
    `);

    // Add unique constraint to ensure a zone is assigned to only one driver (when not deleted)
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_driver_zone_assignments_zone_id_unique"
      ON "driver_zone_assignments" ("tenant_id", "zone_id")
      WHERE "is_deleted" = false
    `);

    // Add indexes for faster lookups
    await queryRunner.query(`
      CREATE INDEX "IDX_driver_zone_assignments_tenant_id" ON "driver_zone_assignments" ("tenant_id")
    `);
    await queryRunner.query(`
      CREATE INDEX "IDX_driver_zone_assignments_driver_id" ON "driver_zone_assignments" ("driver_id")
    `);
    await queryRunner.query(`
      CREATE INDEX "IDX_driver_zone_assignments_zone_id" ON "driver_zone_assignments" ("zone_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "IDX_driver_zone_assignments_zone_id"`);
    await queryRunner.query(
      `DROP INDEX "IDX_driver_zone_assignments_driver_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "IDX_driver_zone_assignments_tenant_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "IDX_driver_zone_assignments_zone_id_unique"`,
    );
    await queryRunner.query(`DROP TABLE "driver_zone_assignments"`);
  }
}
