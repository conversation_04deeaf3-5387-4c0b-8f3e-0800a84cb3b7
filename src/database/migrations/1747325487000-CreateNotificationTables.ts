import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateNotificationTables1747325487000
  implements MigrationInterface
{
  name = 'CreateNotificationTables1747325487000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum type for notification channels
    await queryRunner.query(`
      CREATE TYPE "public"."notification_templates_channel_enum" AS ENUM(
        'email',
        'sms'
      )
    `);

    // Create notification templates table
    await queryRunner.query(`
      CREATE TABLE "notification_templates" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(100) NOT NULL,
        "type" character varying(100) NOT NULL,
        "channel" "public"."notification_templates_channel_enum" NOT NULL,
        "content" text NOT NULL,
        "subject" character varying(255),
        "isActive" boolean NOT NULL DEFAULT true,
        "description" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_notification_templates" PRIMARY KEY ("id")
      )
    `);

    // Create index on template name
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_notification_templates_name" ON "notification_templates" ("name")
    `);

    // Create index on template type and channel
    await queryRunner.query(`
      CREATE INDEX "IDX_notification_templates_type_channel" ON "notification_templates" ("type", "channel")
    `);

    // Create notification logs table
    await queryRunner.query(`
      CREATE TABLE "notification_logs" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "type" character varying(100) NOT NULL,
        "channel" "public"."notification_templates_channel_enum" NOT NULL,
        "recipientEmail" character varying(255),
        "recipientPhone" character varying(50),
        "templateId" character varying(255) NOT NULL,
        "tenantId" uuid NOT NULL,
        "payload" jsonb,
        "success" boolean NOT NULL DEFAULT true,
        "errorMessage" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_notification_logs" PRIMARY KEY ("id")
      )
    `);

    // Create indexes on notification logs for querying
    await queryRunner.query(`
      CREATE INDEX "IDX_notification_logs_tenant" ON "notification_logs" ("tenantId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_notification_logs_type_channel" ON "notification_logs" ("type", "channel")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_notification_logs_created_at" ON "notification_logs" ("createdAt")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_notification_logs_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_notification_logs_type_channel"`);
    await queryRunner.query(`DROP INDEX "IDX_notification_logs_tenant"`);
    await queryRunner.query(
      `DROP INDEX "IDX_notification_templates_type_channel"`,
    );
    await queryRunner.query(`DROP INDEX "IDX_notification_templates_name"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "notification_logs"`);
    await queryRunner.query(`DROP TABLE "notification_templates"`);

    // Drop enum type
    await queryRunner.query(
      `DROP TYPE "public"."notification_templates_channel_enum"`,
    );
  }
}
