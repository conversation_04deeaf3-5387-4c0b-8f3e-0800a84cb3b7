import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePaymentTables1698765432000 implements MigrationInterface {
  name = 'CreatePaymentTables1698765432000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create payment_providers table
    await queryRunner.query(`
      CREATE TYPE "public"."payment_provider_enum" AS ENUM('Stripe');
      
      CREATE TABLE "payment_providers" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "tenant_id" uuid NOT NULL,
        "provider" "public"."payment_provider_enum" NOT NULL,
        "name" character varying(100) NOT NULL,
        "is_enabled" boolean NOT NULL DEFAULT true,
        "is_default" boolean NOT NULL DEFAULT false,
        "configuration" jsonb,
        "metadata" jsonb,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "created_by" uuid NOT NULL,
        "updated_by" uuid,
        CONSTRAINT "pk_payment_providers" PRIMARY KEY ("id")
      );
      
      CREATE INDEX "idx_payment_providers_tenant_id" ON "payment_providers" ("tenant_id");
    `);

    // Create payment_methods table
    await queryRunner.query(`
      CREATE TYPE "public"."payment_method_type_enum" AS ENUM('CreditCard', 'BankTransfer', 'DigitalWallet', 'Cash', 'Other');
      
      CREATE TABLE "payment_methods" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "tenant_id" uuid NOT NULL,
        "customer_id" uuid NOT NULL,
        "type" "public"."payment_method_type_enum" NOT NULL,
        "provider" "public"."payment_provider_enum" NOT NULL,
        "provider_method_id" character varying(255),
        "is_default" boolean NOT NULL DEFAULT false,
        "is_enabled" boolean NOT NULL DEFAULT true,
        "name" character varying(100) NOT NULL,
        "display_name" character varying(100) NOT NULL,
        "last_four" character varying(4),
        "expiry_month" smallint,
        "expiry_year" smallint,
        "brand" character varying(50),
        "country" character varying(2),
        "metadata" jsonb,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "created_by" uuid NOT NULL,
        "updated_by" uuid,
        CONSTRAINT "pk_payment_methods" PRIMARY KEY ("id")
      );
      
      CREATE INDEX "idx_payment_methods_tenant_id" ON "payment_methods" ("tenant_id");
      CREATE INDEX "idx_payment_methods_customer_id" ON "payment_methods" ("customer_id");
    `);

    // Create payments table
    await queryRunner.query(`
      CREATE TYPE "public"."payment_status_enum" AS ENUM('Pending', 'Processing', 'Completed', 'Failed', 'Refunded', 'PartiallyRefunded', 'Cancelled');
      CREATE TYPE "public"."payment_trigger_type_enum" AS ENUM('Manual', 'OrderCreation', 'OrderCompletion', 'Scheduled', 'Milestone');
      
      CREATE TABLE "payments" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "tenant_id" uuid NOT NULL,
        "amount" decimal(10,2) NOT NULL,
        "currency" character varying(3) NOT NULL DEFAULT 'USD',
        "status" "public"."payment_status_enum" NOT NULL DEFAULT 'Pending',
        "provider" "public"."payment_provider_enum" NOT NULL,
        "provider_transaction_id" character varying(255),
        "provider_fee" decimal(10,2),
        "payment_method_id" uuid,
        "payment_method_type" "public"."payment_method_type_enum" NOT NULL,
        "payment_method_details" jsonb,
        "trigger_type" "public"."payment_trigger_type_enum" NOT NULL DEFAULT 'Manual',
        "description" text,
        "metadata" jsonb,
        "entity_type" character varying(50) NOT NULL,
        "entity_id" uuid NOT NULL,
        "customer_id" uuid NOT NULL,
        "customer_email" character varying(255),
        "customer_name" character varying(255),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "completed_at" TIMESTAMP WITH TIME ZONE,
        "failed_at" TIMESTAMP WITH TIME ZONE,
        "cancelled_at" TIMESTAMP WITH TIME ZONE,
        "created_by" uuid NOT NULL,
        "updated_by" uuid,
        "error_message" text,
        "error_code" character varying(50),
        "receipt_url" character varying(255),
        "receipt_number" character varying(50),
        CONSTRAINT "pk_payments" PRIMARY KEY ("id")
      );
      
      CREATE INDEX "idx_payments_tenant_id" ON "payments" ("tenant_id");
      CREATE INDEX "idx_payments_entity_id" ON "payments" ("entity_id");
      CREATE INDEX "idx_payments_customer_id" ON "payments" ("customer_id");
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "payment_methods" 
      ADD CONSTRAINT "fk_payment_methods_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
      
      ALTER TABLE "payment_methods" 
      ADD CONSTRAINT "fk_payment_methods_updated_by" 
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
      
      ALTER TABLE "payment_providers" 
      ADD CONSTRAINT "fk_payment_providers_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
      
      ALTER TABLE "payment_providers" 
      ADD CONSTRAINT "fk_payment_providers_updated_by" 
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
      
      ALTER TABLE "payments" 
      ADD CONSTRAINT "fk_payments_payment_method" 
      FOREIGN KEY ("payment_method_id") REFERENCES "payment_methods"("id") ON DELETE SET NULL ON UPDATE NO ACTION;
      
      ALTER TABLE "payments" 
      ADD CONSTRAINT "fk_payments_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
      
      ALTER TABLE "payments" 
      ADD CONSTRAINT "fk_payments_updated_by" 
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "payments" DROP CONSTRAINT "fk_payments_updated_by";
      ALTER TABLE "payments" DROP CONSTRAINT "fk_payments_created_by";
      ALTER TABLE "payments" DROP CONSTRAINT "fk_payments_payment_method";
      ALTER TABLE "payment_providers" DROP CONSTRAINT "fk_payment_providers_updated_by";
      ALTER TABLE "payment_providers" DROP CONSTRAINT "fk_payment_providers_created_by";
      ALTER TABLE "payment_methods" DROP CONSTRAINT "fk_payment_methods_updated_by";
      ALTER TABLE "payment_methods" DROP CONSTRAINT "fk_payment_methods_created_by";
    `);

    // Drop tables
    await queryRunner.query(`DROP TABLE "payments"`);
    await queryRunner.query(`DROP TABLE "payment_methods"`);
    await queryRunner.query(`DROP TABLE "payment_providers"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "public"."payment_trigger_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_method_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_provider_enum"`);
  }
}
