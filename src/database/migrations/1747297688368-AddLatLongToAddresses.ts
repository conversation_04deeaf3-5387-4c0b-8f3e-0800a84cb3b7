import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLatLongToAddresses1747297688368 implements MigrationInterface {
  name = 'AddLatLongToAddresses1747297688368';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add latitude and longitude columns to addresses table
    await queryRunner.query(`
            ALTER TABLE "addresses"
            ADD COLUMN "latitude" float NULL,
            ADD COLUMN "longitude" float NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove latitude and longitude columns from addresses table
    await queryRunner.query(`
            ALTER TABLE "addresses"
            DROP COLUMN "latitude",
            DROP COLUMN "longitude"
        `);
  }
}
