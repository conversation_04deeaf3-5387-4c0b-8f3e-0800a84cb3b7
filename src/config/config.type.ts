import { AppConfig } from './app-config.type';
import { DatabaseConfig } from '@database/config/database-config.type';
import { MailConfig } from '@core/mail/config/mail-config.type';
import { TracingConfig } from '@core/infrastructure/tracing/types/tracing.types';
import { PaymentConfig } from '@core/payment/config/payment-config.type';
import { AnalyticsConfig } from '@core/analytics/config/analytics-config.type';

export type AllConfigType = {
  app: AppConfig;
  database: DatabaseConfig;
  mail: MailConfig;
  tracing: TracingConfig;
  payment: PaymentConfig;
  analytics: AnalyticsConfig;
};
