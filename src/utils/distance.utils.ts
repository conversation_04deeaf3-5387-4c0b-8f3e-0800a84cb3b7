import { AddressDomain } from '@app/business/address/addresses/domain/address';
import { DistanceUnit } from '@app/business/order/orders/domain/order.types';
import axios from 'axios';
import * as momentTZ from 'moment-timezone';

/**
 * Interface representing coordinates (latitude and longitude)
 */
export interface Coordinates {
  latitude: number;
  longitude: number;
}

/**
 * Calculate the distance between two points using Google Maps Distance Matrix API
 * @param lat1 Latitude of first point
 * @param lon1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lon2 Longitude of second point
 * @returns Distance between the two points in kilometers, or 0 if calculation fails
 */
export async function getDistance(
  lat1: number | undefined | null,
  lon1: number | undefined | null,
  lat2: number | undefined | null,
  lon2: number | undefined | null,
): Promise<number> {
  if (!lat1 || !lon1 || !lat2 || !lon2) {
    console.log('Missing coordinates for distance calculation');
    return 0;
  }
  console.log(`origins=${lat1},${lon1}&destinations=${lat2},${lon2}`);

  try {
    const url = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${lat1},${lon1}&destinations=${lat2},${lon2}&key=AIzaSyCTIl6p6e-IRX0h4RqI1J51Rs_DMqsgn6E`;
    const { data } = await axios.get(url);
    console.log({ data });
    if (
      !data.rows ||
      !data.rows[0] ||
      !data.rows[0].elements ||
      !data.rows[0].elements[0] ||
      data.rows[0].elements[0].status === 'ZERO_RESULTS'
    ) {
      console.log('No route found between the coordinates');
      return 0;
    }

    // Get distance in kilometers (Google returns in meters)
    let distanceInKm = data.rows[0].elements[0].distance.value / 1000;

    // Round to 2 decimal places
    distanceInKm = Math.round(distanceInKm * 100) / 100;

    return distanceInKm;
  } catch (error) {
    console.error('Error calculating distance with Google Maps API:', error);
    return 0;
  }
}

/**
 * Convert distance from kilometers to miles
 * @param distanceKm Distance in kilometers
 * @returns Distance in miles
 */
export function convertKmToMiles(distanceKm: number): number {
  return distanceKm * 0.621371;
}

/**
 * Calculate the distance between two addresses
 * @param address1 First address
 * @param address2 Second address
 * @param unit Unit of distance (default: kilometers)
 * @returns Promise resolving to the distance between the two addresses in the specified unit, or 0 if coordinates are missing
 */
export async function calculateAddressDistance(
  address1: AddressDomain,
  address2: AddressDomain,
  unit: DistanceUnit = DistanceUnit.Kilometers,
): Promise<number> {
  // Get distance in kilometers
  const distanceKm = await getDistance(
    address1.latitude,
    address1.longitude,
    address2.latitude,
    address2.longitude,
  );

  // Convert to miles if needed
  if (unit === DistanceUnit.Miles) {
    return convertKmToMiles(distanceKm);
  }

  return distanceKm;
}

/**
 * Calculate the distance between two addresses using their IDs
 * This function is meant to be implemented by a service that can retrieve addresses by ID
 *
 * @param addressService The address service instance
 * @param address1Id First address ID
 * @param address2Id Second address ID
 * @param unit Unit of distance (default: kilometers)
 * @returns Promise resolving to the distance between the two addresses in the specified unit, or 0 if addresses or coordinates are missing
 */
/**
 * Calculate the distance between two points using their coordinates directly
 * @param lat1 Latitude of first point
 * @param lon1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lon2 Longitude of second point
 * @param unit Unit of distance (default: kilometers)
 * @returns Promise resolving to the distance between the two points in the specified unit
 */
export async function calculateDistanceByCoordinates(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
  unit: DistanceUnit = DistanceUnit.Kilometers,
): Promise<number> {
  // Get distance in kilometers
  const distanceKm = await getDistance(lat1, lon1, lat2, lon2);

  // Convert to miles if needed
  if (unit === DistanceUnit.Miles) {
    return convertKmToMiles(distanceKm);
  }

  return distanceKm;
}

/**
 * Calculate the distance between two addresses using their IDs
 * This function now accepts coordinates directly instead of fetching addresses
 *
 * @param collectionLat Latitude of collection address
 * @param collectionLng Longitude of collection address
 * @param deliveryLat Latitude of delivery address
 * @param deliveryLng Longitude of delivery address
 * @param unit Unit of distance (default: kilometers)
 * @returns Promise resolving to the distance between the two addresses in the specified unit
 */
export async function calculateAddressDistanceById(
  addressService: any,
  address1Id: string,
  address2Id: string,
  unit: DistanceUnit = DistanceUnit.Kilometers,
  collectionLat?: number,
  collectionLng?: number,
  deliveryLat?: number,
  deliveryLng?: number,
): Promise<number> {
  try {
    // If coordinates are provided, use them directly
    if (
      collectionLat !== undefined &&
      collectionLng !== undefined &&
      deliveryLat !== undefined &&
      deliveryLng !== undefined
    ) {
      return calculateDistanceByCoordinates(
        collectionLat,
        collectionLng,
        deliveryLat,
        deliveryLng,
        unit,
      );
    }

    // Otherwise, fall back to retrieving addresses by ID
    const address1 = await addressService.findById(address1Id);
    const address2 = await addressService.findById(address2Id);

    if (!address1 || !address2) {
      console.log('One or both addresses not found');
      return 0;
    }

    return calculateAddressDistance(address1, address2, unit);
  } catch (error) {
    console.error('Error calculating distance between addresses:', error);
    return 0;
  }
}

/**
 * Convert a date to a specific timezone
 * @param date Date to convert
 * @param timezone Timezone to convert to (default: 'America/Montreal')
 * @returns Formatted date string in the specified timezone
 */
export function convertToTimeZone(
  date: Date | string,
  timezone: string = 'America/Montreal',
): string {
  return momentTZ.utc(date).tz(timezone).format('YYYY-MM-DD HH:mm');
}
