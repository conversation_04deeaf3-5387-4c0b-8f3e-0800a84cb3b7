import {
  HttpStatus,
  ValidationError,
  ValidationPipeOptions,
} from '@nestjs/common';
import { AppException } from './errors/app.exception';
import { ErrorCode } from './errors/error-codes';

function generateErrors(errors: ValidationError[]) {
  return errors.reduce(
    (accumulator, currentValue) => ({
      ...accumulator,
      [currentValue.property]:
        (currentValue.children?.length ?? 0) > 0
          ? generateErrors(currentValue.children ?? [])
          : Object.values(currentValue.constraints ?? {}).join(', '),
    }),
    {},
  );
}

const validationOptions: ValidationPipeOptions = {
  transform: true,
  whitelist: true,
  errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
  exceptionFactory: (errors: ValidationError[]) => {
    return new AppException(
      'Validation failed',
      ErrorCode.VALIDATION_ERROR,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        errors: generateErrors(errors),
      },
    );
  },
};

export default validationOptions;
