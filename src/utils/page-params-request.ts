import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional, IsPositive } from 'class-validator';

export class PageParamsRequest {
  @ApiPropertyOptional({ example: 1 })
  @Transform(({ value }) => (value ? Number(value) : 1))
  @IsInt()
  @IsPositive()
  @IsOptional()
  pageNumber: number;

  @ApiPropertyOptional({ example: 10 })
  @Transform(({ value }) => (value ? Number(value) : 10))
  @IsInt()
  @IsPositive()
  @IsOptional()
  pageSize: number;
}
