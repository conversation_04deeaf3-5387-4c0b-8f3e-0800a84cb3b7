import { HttpStatus, UnprocessableEntityException } from '@nestjs/common';

export class UniqueConstraintException extends UnprocessableEntityException {
  constructor(fields: string[]) {
    super({
      statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      message: 'Unique constraint violation',
      errors: fields.reduce(
        (acc, field) => ({
          ...acc,
          [field]: 'alreadyExists',
        }),
        {},
      ),
    });
  }
}
