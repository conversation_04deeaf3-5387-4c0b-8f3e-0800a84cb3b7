import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

export class CategoryNotFoundException extends AppException {
  constructor(id: string) {
    super('Category not found', ErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND, {
      id,
    });
  }
}

export class CategoryAlreadyExistsException extends AppException {
  constructor(name: string, tenantId: string) {
    super(
      'Category with this name already exists',
      ErrorCode.DUPLICATE_ENTRY,
      HttpStatus.CONFLICT,
      {
        name,
        tenantId,
        constraint: 'unique_category_name_per_tenant',
      },
    );
  }
}

export class CategoryOperationNotAllowedException extends AppException {
  constructor(operation: string, reason: string) {
    super(
      `Cannot ${operation} category: ${reason}`,
      ErrorCode.CONSTRAINT_VIOLATION,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}
