import { AppException } from '../app.exception';
import { ErrorCode } from '../error-codes';
import { HttpStatus } from '@nestjs/common';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class SettingNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple settings not found',
        ErrorCode.SETTING_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested settings were not found',
        ErrorCode.SETTING_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Setting not found',
      ErrorCode.SETTING_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class SettingAlreadyExistsException extends AppException {
  constructor(key: string) {
    super(
      'Setting with this key already exists',
      ErrorCode.SETTING_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        key,
        constraint: 'unique_key_per_user_settings',
      },
    );
  }
}
