import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { HttpStatus } from '@nestjs/common';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class AddressNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple addresses not found',
        ErrorCode.ADDRESS_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested addresses were not found',
        ErrorCode.ADDRESS_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Address not found',
      ErrorCode.ADDRESS_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class AddressAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Address is already deleted',
      ErrorCode.ADDRESS_DELETED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class AddressOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} address: ${reason}`,
      ErrorCode.ADDRESS_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class AddressValidationException extends AppException {
  constructor(errors: Record<string, string>) {
    super(
      'Address validation failed',
      ErrorCode.ADDRESS_VALIDATION_ERROR,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        errors,
        errorType: 'VALIDATION_ERROR',
      },
    );
  }
}

export class AddressDefaultDuplicateException extends AppException {
  constructor(type: 'pickup' | 'delivery', existingId: string) {
    super(
      `Another address is already set as default for ${type}`,
      ErrorCode.ADDRESS_DUPLICATE_DEFAULT,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        type,
        existingId,
        errorType: 'DUPLICATE_DEFAULT',
      },
    );
  }
}

export class AddressZoneNotFoundException extends AppException {
  constructor(zoneId: string) {
    super(
      'Zone not found for address',
      ErrorCode.ADDRESS_ZONE_NOT_FOUND,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        zoneId,
        errorType: 'ZONE_NOT_FOUND',
      },
    );
  }
}

export class AddressInvalidPhoneException extends AppException {
  constructor(phoneNumber: string, countryCode: string) {
    super(
      'Invalid phone number format',
      ErrorCode.ADDRESS_INVALID_PHONE,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        phoneNumber,
        countryCode,
        errorType: 'INVALID_PHONE',
      },
    );
  }
}

export class AddressInvalidEmailException extends AppException {
  constructor(email: string) {
    super(
      'Invalid email format',
      ErrorCode.ADDRESS_INVALID_EMAIL,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        email,
        errorType: 'INVALID_EMAIL',
      },
    );
  }
}
