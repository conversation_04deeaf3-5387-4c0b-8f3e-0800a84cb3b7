import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '../error-codes';
import { HttpStatus } from '@nestjs/common';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class RoleNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple roles not found',
        ErrorCode.ROLE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested roles were not found',
        ErrorCode.ROLE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super('Role not found', ErrorCode.ROLE_NOT_FOUND, HttpStatus.NOT_FOUND, {
      id,
    });
  }
}

export class RoleAssignmentNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple role assignments not found',
        ErrorCode.ROLE_ASSIGNMENT_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested role assignments were not found',
        ErrorCode.ROLE_ASSIGNMENT_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Role Assignment not found',
      ErrorCode.ROLE_ASSIGNMENT_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      {
        id,
      },
    );
  }
}

export class PermissionDeniedException extends AppException {
  constructor(permissionName: string) {
    super(
      `Permission denied: ${permissionName}`,
      ErrorCode.PERMISSION_DENIED,
      HttpStatus.FORBIDDEN,
      { permissionName },
    );
  }
}
