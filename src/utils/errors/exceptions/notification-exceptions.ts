import { HttpStatus } from '@nestjs/common';
import { AppException } from '../app.exception';
import { ErrorCode } from '../error-codes';

export class NotificationTemplateNotFoundException extends AppException {
  constructor(templateName: string) {
    super(
      `Notification template not found: ${templateName}`,
      ErrorCode.NOTIFICATION_TEMPLATE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { templateName },
    );
  }
}

export class NotificationTemplateRenderingFailedException extends AppException {
  constructor(templateName: string) {
    super(
      `Failed to render notification template: ${templateName}`,
      ErrorCode.NOTIFICATION_TEMPLATE_RENDERING_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      { templateName },
    );
  }
}

export class NotificationSendFailedException extends AppException {
  constructor(message: string) {
    super(
      `Failed to send notification: ${message}`,
      ErrorCode.NOTIFICATION_SEND_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      { errorDetails: message },
    );
  }
}

export class NotificationRecipientEmailRequiredException extends AppException {
  constructor() {
    super(
      'Email address is required for email notifications',
      ErrorCode.NOTIFICATION_RECIPIENT_EMAIL_REQUIRED,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class NotificationRecipientPhoneRequiredException extends AppException {
  constructor() {
    super(
      'Phone number is required for SMS notifications',
      ErrorCode.NOTIFICATION_RECIPIENT_PHONE_REQUIRED,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class NotificationTemplateCreationFailedException extends AppException {
  constructor(message: string) {
    super(
      `Failed to create notification template: ${message}`,
      ErrorCode.NOTIFICATION_TEMPLATE_CREATION_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      { errorDetails: message },
    );
  }
}

export class SmsSendFailedException extends AppException {
  constructor(message: string) {
    super(
      `Failed to send SMS: ${message}`,
      ErrorCode.SMS_SEND_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      { errorDetails: message },
    );
  }
}

export class SmsProviderConfigurationException extends AppException {
  constructor(message: string) {
    super(
      `SMS provider configuration error: ${message}`,
      ErrorCode.SMS_PROVIDER_CONFIG_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      { errorDetails: message },
    );
  }
}

export class SmsProviderServiceException extends AppException {
  constructor(message: string) {
    super(
      `SMS provider service error: ${message}`,
      ErrorCode.SMS_PROVIDER_SERVICE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      { errorDetails: message },
    );
  }
}
