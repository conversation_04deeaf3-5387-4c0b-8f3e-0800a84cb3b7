import { AppException } from '@utils/errors/app.exception';
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@utils/errors/error-codes';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class VehicleNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple vehicles not found',
        ErrorCode.VEHICLE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested vehicles were not found',
        ErrorCode.VEHICLE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Vehicle not found',
      ErrorCode.VEHICLE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class VehicleAlreadyExistsException extends AppException {
  constructor(licensePlate: string, fleetId: string) {
    super(
      'Vehicle with this license plate already exists',
      ErrorCode.VEHICLE_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        licensePlate,
        fleetId,
        constraint: 'unique_license_plate_per_fleet',
      },
    );
  }
}

export class VehicleInactiveException extends AppException {
  constructor(id: string) {
    super(
      'Vehicle is inactive',
      ErrorCode.VEHICLE_INACTIVE,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class VehicleAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Vehicle is already deleted',
      ErrorCode.VEHICLE_DELETED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class VehicleInvalidStatusException extends AppException {
  constructor(id: string, currentStatus: string, requiredStatus: string) {
    super(
      'Invalid vehicle status for operation',
      ErrorCode.VEHICLE_INVALID_STATUS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        currentStatus,
        requiredStatus,
        message: `Vehicle status must be ${requiredStatus} but is ${currentStatus}`,
      },
    );
  }
}

export class VehicleOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} vehicle: ${reason}`,
      ErrorCode.VEHICLE_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class VehicleMaintenanceOverdueException extends AppException {
  constructor(
    id: string,
    lastMaintenanceDate: Date,
    maintenanceIntervalKm: number,
  ) {
    super(
      'Vehicle maintenance is overdue',
      ErrorCode.VEHICLE_MAINTENANCE_OVERDUE,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        lastMaintenanceDate,
        maintenanceIntervalKm,
        errorType: 'MAINTENANCE_OVERDUE',
      },
    );
  }
}

// Vehicle Type Exceptions
export class VehicleTypeNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple vehicle types not found',
        ErrorCode.VEHICLE_TYPE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested vehicle types were not found',
        ErrorCode.VEHICLE_TYPE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Vehicle type not found',
      ErrorCode.VEHICLE_TYPE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class VehicleTypeAlreadyExistsException extends AppException {
  constructor(name: string) {
    super(
      'Vehicle type with this name already exists',
      ErrorCode.VEHICLE_TYPE_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        name,
        constraint: 'unique_vehicle_type_name',
      },
    );
  }
}

export class VehicleTypeInactiveException extends AppException {
  constructor(id: string) {
    super(
      'Vehicle type is inactive',
      ErrorCode.VEHICLE_TYPE_INACTIVE,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class VehicleTypeAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Vehicle type is already deleted',
      ErrorCode.VEHICLE_TYPE_DELETED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class VehicleTypeInvalidStatusException extends AppException {
  constructor(id: string, currentStatus: string, requiredStatus: string) {
    super(
      'Invalid vehicle type status for operation',
      ErrorCode.VEHICLE_TYPE_INVALID_STATUS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        currentStatus,
        requiredStatus,
        message: `Vehicle type status must be ${requiredStatus} but is ${currentStatus}`,
      },
    );
  }
}

export class VehicleTypeInUseException extends AppException {
  constructor(id: string, vehicleCount: number) {
    super(
      'Vehicle type is currently in use by vehicles',
      ErrorCode.VEHICLE_TYPE_IN_USE,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        vehicleCount,
        errorType: 'TYPE_IN_USE',
      },
    );
  }
}

export class VehicleTypeOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} vehicle type: ${reason}`,
      ErrorCode.VEHICLE_TYPE_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

// Time Clock Session Exceptions
export class TimeClockSessionNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple time clock sessions not found',
        ErrorCode.TIME_CLOCK_SESSION_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested time clock sessions were not found',
        ErrorCode.TIME_CLOCK_SESSION_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Time clock session not found',
      ErrorCode.TIME_CLOCK_SESSION_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class TimeClockSessionAlreadyExistsException extends AppException {
  constructor(vehicleId: string, startTime: Date) {
    super(
      'Time clock session already exists for this vehicle at the specified time',
      ErrorCode.TIME_CLOCK_SESSION_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        vehicleId,
        startTime,
        constraint: 'unique_vehicle_time_session',
      },
    );
  }
}

export class TimeClockSessionInvalidStatusException extends AppException {
  constructor(id: string, currentStatus: string, requiredStatus: string) {
    super(
      'Invalid time clock session status for operation',
      ErrorCode.TIME_CLOCK_SESSION_INVALID_STATUS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        currentStatus,
        requiredStatus,
        message: `Time clock session status must be ${requiredStatus} but is ${currentStatus}`,
      },
    );
  }
}

export class TimeClockSessionAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Time clock session is already deleted',
      ErrorCode.TIME_CLOCK_SESSION_DELETED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class TimeClockSessionOverlapException extends AppException {
  constructor(vehicleId: string, startTime: Date, endTime: Date) {
    super(
      'Time clock session overlaps with existing session',
      ErrorCode.TIME_CLOCK_SESSION_OVERLAP,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        vehicleId,
        startTime,
        endTime,
        errorType: 'SESSION_OVERLAP',
      },
    );
  }
}

export class TimeClockSessionDurationExceededException extends AppException {
  constructor(id: string, duration: number, maxDuration: number) {
    super(
      'Time clock session duration exceeds maximum allowed',
      ErrorCode.TIME_CLOCK_SESSION_DURATION_EXCEEDED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        duration,
        maxDuration,
        errorType: 'DURATION_EXCEEDED',
      },
    );
  }
}

export class TimeClockSessionOperationNotAllowedException extends AppException {
  constructor(
    id: string,
    operation: string,
    reason: string,
    details?: Record<string, unknown>,
  ) {
    super(
      `Cannot ${operation} time clock session: ${reason}`,
      ErrorCode.TIME_CLOCK_SESSION_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
        ...details,
      },
    );
  }
}
