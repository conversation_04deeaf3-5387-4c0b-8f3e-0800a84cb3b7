import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

export class InvalidCredentialsException extends AppException {
  constructor(email: string) {
    super(
      'Invalid credentials',
      ErrorCode.INVALID_CREDENTIALS,
      HttpStatus.UNAUTHORIZED,
      { email },
    );
  }
}

export class TokenExpiredException extends AppException {
  constructor(tokenType: 'access' | 'refresh') {
    super(
      `${tokenType.charAt(0).toUpperCase() + tokenType.slice(1)} token has expired`,
      ErrorCode.TOKEN_EXPIRED,
      HttpStatus.UNAUTHORIZED,
      { tokenType },
    );
  }
}

export class TokenInvalidException extends AppException {
  constructor(tokenType: 'access' | 'refresh') {
    super(
      `Invalid ${tokenType} token`,
      ErrorCode.TOKEN_INVALID,
      HttpStatus.UNAUTHORIZED,
      { tokenType },
    );
  }
}

export class TokenMissingException extends AppException {
  constructor(tokenType: 'access' | 'refresh') {
    super(`Invalid Token`, ErrorCode.TOKEN_MISSING, HttpStatus.UNAUTHORIZED, {
      tokenType,
    });
  }
}

export class CompanyIdAlreadyExistsException extends AppException {
  constructor(companyUniqueId: string) {
    super(
      'Company ID already exists',
      ErrorCode.TENANT_UNIQUE_CONSTRAINT_VIOLATION,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        companyUniqueId,
        constraint: 'unique_company_id',
        field: 'companyUniqueId',
      },
    );
  }
}

export class EmailAlreadyExistsException extends AppException {
  constructor(email: string) {
    super(
      'Email already exists',
      ErrorCode.USER_EMAIL_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        email,
        constraint: 'unique_email',
        field: 'email',
      },
    );
  }
}

export class RegistrationFailedException extends AppException {
  constructor(reason: string, details?: Record<string, unknown>) {
    super(
      `Registration failed: ${reason}`,
      ErrorCode.USER_OPERATION_NOT_ALLOWED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      {
        reason,
        operation: 'register',
        errorType: 'REGISTRATION_FAILED',
        ...details,
      },
    );
  }
}
