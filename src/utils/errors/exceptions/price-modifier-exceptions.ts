import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class PriceModifierNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple price modifiers not found',
        ErrorCode.PRICE_MODIFIER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested price modifiers were not found',
        ErrorCode.PRICE_MODIFIER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Price modifier not found',
      ErrorCode.PRICE_MODIFIER_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class PriceModifierAlreadyExistsException extends AppException {
  constructor(name: string, tenantId: string) {
    super(
      'Price modifier with this name already exists for this tenant',
      ErrorCode.PRICE_MODIFIER_ALREADY_EXISTS,
      HttpStatus.CONFLICT,
      {
        name,
        tenantId,
        constraint: 'unique_price_modifier_name_per_tenant',
      },
    );
  }
}

export class PriceModifierOperationNotAllowedException extends AppException {
  constructor(operation: string, reason: string) {
    super(
      `Cannot ${operation} price modifier: ${reason}`,
      ErrorCode.PRICE_MODIFIER_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class PriceModifierInvalidTypeException extends AppException {
  constructor(type: string, allowedTypes: string[]) {
    super(
      'Invalid price modifier calculation type',
      ErrorCode.PRICE_MODIFIER_INVALID_STATUS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        type,
        allowedTypes,
        errorType: 'INVALID_TYPE',
      },
    );
  }
}

export class PriceModifierGroupNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple price modifier groups not found',
        ErrorCode.PRICE_MODIFIER_GROUP_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested price modifier groups were not found',
        ErrorCode.PRICE_MODIFIER_GROUP_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Price modifier group not found',
      ErrorCode.PRICE_MODIFIER_GROUP_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class PriceModifierGroupAlreadyExistsException extends AppException {
  constructor(name: string, tenantId: string) {
    super(
      'Price modifier group with this name already exists for this tenant',
      ErrorCode.PRICE_MODIFIER_GROUP_ALREADY_EXISTS,
      HttpStatus.CONFLICT,
      {
        name,
        tenantId,
        constraint: 'unique_price_modifier_group_name_per_tenant',
      },
    );
  }
}

export class PriceModifierGroupOperationNotAllowedException extends AppException {
  constructor(operation: string, reason: string) {
    super(
      `Cannot ${operation} price modifier group: ${reason}`,
      ErrorCode.PRICE_MODIFIER_GROUP_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class PriceModifierDuplicateInGroupException extends AppException {
  constructor(modifierId: string, groupId: string) {
    super(
      'Price modifier already exists in this group',
      ErrorCode.PRICE_MODIFIER_GROUP_OPERATION_NOT_ALLOWED,
      HttpStatus.CONFLICT,
      {
        modifierId,
        groupId,
        constraint: 'unique_group_modifier',
      },
    );
  }
}

export class PriceModifierTenantMismatchException extends AppException {
  constructor(modifierId: string, tenantId: string) {
    super(
      'Price modifier does not belong to this tenant',
      ErrorCode.PRICE_MODIFIER_TENANT_MISMATCH,
      HttpStatus.FORBIDDEN,
      {
        modifierId,
        tenantId,
        errorType: 'TENANT_MISMATCH',
      },
    );
  }
}

export class PriceModifierGroupTenantMismatchException extends AppException {
  constructor(groupId: string, tenantId: string) {
    super(
      'Price modifier group does not belong to this tenant',
      ErrorCode.PRICE_MODIFIER_GROUP_TENANT_MISMATCH,
      HttpStatus.FORBIDDEN,
      {
        groupId,
        tenantId,
        errorType: 'TENANT_MISMATCH',
      },
    );
  }
}
