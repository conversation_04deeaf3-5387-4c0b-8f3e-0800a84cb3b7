import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class PackageTemplateNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    // Handle batch operations
    if (Array.isArray(id)) {
      super(
        'Multiple package templates not found',
        ErrorCode.PACKAGE_TEMPLATE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }

    // Handle single ID with batch context
    if (context && typeof context === 'object') {
      super(
        'Some requested package templates were not found',
        ErrorCode.PACKAGE_TEMPLATE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }

    // Simple single package template not found
    super(
      'Package template not found',
      ErrorCode.PACKAGE_TEMPLATE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class PackageTemplateAlreadyExistsException extends AppException {
  constructor(name: string, tenantId: string) {
    super(
      'Package template with this name already exists',
      ErrorCode.PACKAGE_TEMPLATE_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        name,
        tenantId,
        constraint: 'unique_name_per_tenant',
      },
    );
  }
}

export class PackageTemplateInactiveException extends AppException {
  constructor(id: string) {
    super(
      'Package template is inactive',
      ErrorCode.PACKAGE_TEMPLATE_INACTIVE,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class PackageTemplateAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Package template is already deleted',
      ErrorCode.PACKAGE_TEMPLATE_DELETED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class PackageTemplateInvalidStatusException extends AppException {
  constructor(id: string, currentStatus: string, requiredStatus: string) {
    super(
      'Invalid package template status for operation',
      ErrorCode.PACKAGE_TEMPLATE_INVALID_STATUS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        currentStatus,
        requiredStatus,
        message: `Package template status must be ${requiredStatus} but is ${currentStatus}`,
      },
    );
  }
}

export class PackageTemplateOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} package template: ${reason}`,
      ErrorCode.PACKAGE_TEMPLATE_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class PackageTemplateUpdateFailedException extends AppException {
  constructor(id: string, reason?: string) {
    super(
      `Failed to update package template${reason ? `: ${reason}` : ''}`,
      ErrorCode.PACKAGE_TEMPLATE_UPDATE_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      {
        id,
        reason,
      },
    );
  }
}

export class PackageTemplateCreationFailedException extends AppException {
  constructor(reason?: string) {
    super(
      `Failed to create package template${reason ? `: ${reason}` : ''}`,
      ErrorCode.PACKAGE_TEMPLATE_CREATION_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      {
        reason,
      },
    );
  }
}

export class PackageTemplateRestoreFailedException extends AppException {
  constructor(id: string, reason?: string) {
    super(
      `Failed to restore package template${reason ? `: ${reason}` : ''}`,
      ErrorCode.PACKAGE_TEMPLATE_RESTORE_FAILED,
      HttpStatus.INTERNAL_SERVER_ERROR,
      {
        id,
        reason,
      },
    );
  }
}

export class PackageTemplateTenantMismatchException extends AppException {
  constructor(packageTemplateId: string, tenantId: string) {
    super(
      'Package template does not belong to specified tenant',
      ErrorCode.PACKAGE_TEMPLATE_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        packageTemplateId,
        tenantId,
        errorType: 'TENANT_MISMATCH',
      },
    );
  }
}
