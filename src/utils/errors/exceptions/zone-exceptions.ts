import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '../error-codes';
import { HttpStatus } from '@nestjs/common';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class ZoneNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple zones not found',
        ErrorCode.ZONE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested zones were not found',
        ErrorCode.ZONE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super('Zone not found', ErrorCode.ZONE_NOT_FOUND, HttpStatus.NOT_FOUND, {
      id,
    });
  }
}

export class ZoneAlreadyExistsException extends AppException {
  constructor(name: string) {
    super(
      'Zone with this name already exists',
      ErrorCode.ZONE_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        name,
        constraint: 'unique_zone_name',
      },
    );
  }
}

export class ZonePostalCodeExistsException extends AppException {
  constructor(postalCode: string, existingZoneId: string) {
    super(
      'Postal code already assigned to another zone',
      ErrorCode.ZONE_POSTAL_CODE_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        postalCode,
        existingZoneId,
        constraint: 'unique_postal_code',
      },
    );
  }
}

export class ZonePostalCodeNotFoundException extends AppException {
  constructor(postalCode: string) {
    super(
      'No zone found for the given postal code',
      ErrorCode.ZONE_POSTAL_CODE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { postalCode },
    );
  }
}

export class ZoneAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Zone is already deleted',
      ErrorCode.ZONE_DELETED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class ZoneOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} zone: ${reason}`,
      ErrorCode.ZONE_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

// Zone Table Exceptions
export class ZoneTableNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple zone tables not found',
        ErrorCode.ZONE_TABLE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested zone tables were not found',
        ErrorCode.ZONE_TABLE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Zone table not found',
      ErrorCode.ZONE_TABLE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class ZoneTableAlreadyExistsException extends AppException {
  constructor(name: string) {
    super(
      'Zone table with this name already exists',
      ErrorCode.ZONE_TABLE_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        name,
        constraint: 'unique_zone_table_name',
      },
    );
  }
}

export class ZoneTableAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Zone table is already deleted',
      ErrorCode.ZONE_TABLE_DELETED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class ZoneTableOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} zone table: ${reason}`,
      ErrorCode.ZONE_TABLE_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class ZoneTableValueInvalidException extends AppException {
  constructor(details: Record<string, any>) {
    super(
      'Invalid zone table value',
      ErrorCode.ZONE_TABLE_VALUE_INVALID,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        ...details,
        errorType: 'INVALID_VALUE',
      },
    );
  }
}

export class ZoneTableValueDuplicateException extends AppException {
  constructor(originZoneId: string, destinationZoneId: string) {
    super(
      'Duplicate zone pair in table',
      ErrorCode.ZONE_TABLE_VALUE_DUPLICATE,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        originZoneId,
        destinationZoneId,
        constraint: 'unique_zone_pair',
      },
    );
  }
}

export class ZoneTableZoneNotFoundException extends AppException {
  constructor() {
    super(
      'One or more zones in the table not found',
      ErrorCode.ZONE_TABLE_ZONE_NOT_FOUND,
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}
