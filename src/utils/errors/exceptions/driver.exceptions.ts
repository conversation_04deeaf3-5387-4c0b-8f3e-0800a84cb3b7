import { AppException } from '../app.exception';
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '../error-codes';

export class DriverNotFoundException extends AppException {
  constructor(driverId: string) {
    super(
      `Driver with ID ${driverId} not found`,
      ErrorCode.DRIVER_NOT_FOUND,
      HttpStatus.NOT_FOUND,
    );
  }
}

export class DriverLoginException extends AppException {
  constructor() {
    super(
      `Invalid email or password`,
      ErrorCode.DRIVER_AUTH_INVALID_CREDENTIALS,
      HttpStatus.UNAUTHORIZED,
    );
  }
}

export class DriverInactiveException extends AppException {
  constructor(email: string) {
    super(
      `Driver with email ${email} is inactive`,
      ErrorCode.DRIVER_INACTIVE,
      HttpStatus.FORBIDDEN,
    );
  }
}

export class DriverAlreadyExistsException extends AppException {
  constructor(email: string) {
    super(
      'Driver with this email already exists',
      ErrorCode.DRIVER_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        email,
        constraint: 'unique_email',
      },
    );
  }
}
