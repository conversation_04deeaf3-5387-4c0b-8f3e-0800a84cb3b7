import { AppException } from '@utils/errors/app.exception';
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@utils/errors/error-codes';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class PriceSetNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    if (Array.isArray(id)) {
      super(
        'Multiple price sets not found',
        ErrorCode.PRICE_SET_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }
    if (context && typeof context === 'object') {
      super(
        'Some requested price sets were not found',
        ErrorCode.PRICE_SET_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }
    super(
      'Price Set not found',
      ErrorCode.PRICE_SET_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class PriceSetOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} address: ${reason}`,
      ErrorCode.PRICE_SET_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class PriceSetNotAvailableException extends AppException {
  constructor(priceSetId: string, requestedDate: Date) {
    super(
      'Price Set is not available for the requested date/time',
      ErrorCode.PRICE_SET_NOT_AVAILABLE,
      HttpStatus.BAD_REQUEST,
      {
        priceSetId,
        requestedDate: requestedDate.toISOString(),
        errorType: 'AVAILABILITY_ERROR',
      },
    );
  }
}
