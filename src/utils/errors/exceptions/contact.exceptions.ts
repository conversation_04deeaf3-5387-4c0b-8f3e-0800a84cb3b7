import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class ContactNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    // Handle batch operations
    if (Array.isArray(id)) {
      super(
        'Multiple contacts not found',
        ErrorCode.CONTACT_NOT_FOUND, // 403101
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }

    // Handle single ID with batch context
    if (context && typeof context === 'object') {
      super(
        'Some requested contacts were not found',
        ErrorCode.CONTACT_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }

    // Simple single contact not found
    super(
      'Contact not found',
      ErrorCode.CONTACT_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      {
        id,
      },
    );
  }
}

export class ContactAlreadyExistsException extends AppException {
  constructor(email: string, userId: string) {
    super(
      'Contact with this email already exists',
      ErrorCode.CONTACT_ALREADY_EXISTS, // 403102
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        email,
        userId,
        constraint: 'unique_email_per_tenant',
      },
    );
  }
}

export class ContactInactiveException extends AppException {
  constructor(id: string) {
    super(
      'Contact is inactive',
      ErrorCode.CONTACT_INACTIVE, // 403103
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class ContactAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Contact is already deleted',
      ErrorCode.CONTACT_DELETED, // 403104
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class ContactInvalidStatusException extends AppException {
  constructor(id: string, currentStatus: string, requiredStatus: string) {
    super(
      'Invalid contact status for operation',
      ErrorCode.CONTACT_INVALID_STATUS, // 403105
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        currentStatus,
        requiredStatus,
        message: `Contact status must be ${requiredStatus} but is ${currentStatus}`,
      },
    );
  }
}

export class ContactOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} contact: ${reason}`,
      ErrorCode.CONTACT_OPERATION_NOT_ALLOWED, // 403106
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class ContactBatchOperationException extends AppException {
  constructor(failedIds: string[], action: string) {
    super(
      `Failed to ${action} one or more contacts`,
      ErrorCode.CONTACT_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        failedIds,
        action,
        count: failedIds.length,
      },
    );
  }
}

export class ContactDuplicateException extends AppException {
  constructor(details: {
    email?: string;
    phone?: string;
    customerId?: string;
  }) {
    super(
      'Duplicate contact information detected',
      ErrorCode.CONTACT_DUPLICATE, // 403107
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        details,
        errorType: 'DUPLICATE_CONTACT',
      },
    );
  }
}

export class ContactInvalidDataException extends AppException {
  constructor(fieldErrors: Record<string, string>) {
    super(
      'Invalid contact data',
      ErrorCode.CONTACT_INVALID_DATA, // 403108
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        fieldErrors,
        errorType: 'VALIDATION_ERROR',
      },
    );
  }
}

export class ContactCustomerMismatchException extends AppException {
  constructor(contactId: string, customerId: string) {
    super(
      'Contact does not belong to specified customer',
      ErrorCode.CONTACT_CUSTOMER_MISMATCH, // 403109
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        contactId,
        customerId,
        errorType: 'CUSTOMER_MISMATCH',
      },
    );
  }
}
