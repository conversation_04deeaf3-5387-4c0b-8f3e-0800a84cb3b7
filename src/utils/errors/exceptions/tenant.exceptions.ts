import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class TenantNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    // Handle batch operations
    if (Array.isArray(id)) {
      super(
        'Multiple tenants not found',
        ErrorCode.TENANT_NOT_FOUND, // 401001
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }

    // Handle single ID with batch context
    if (context && typeof context === 'object') {
      super(
        'Some requested tenants were not found',
        ErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }

    // Simple single tenant not found
    super(
      'Tenant not found',
      ErrorCode.TENANT_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      { id },
    );
  }
}

export class TenantUniqueConstraintException extends AppException {
  constructor(violations: string[]) {
    const violationsMap = violations.reduce(
      (acc, field) => ({ ...acc, [field]: 'alreadyExists' }),
      {},
    );

    super(
      'Tenant with these fields already exists',
      ErrorCode.TENANT_ALREADY_EXISTS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        violations: violationsMap,
        fields: violations,
        count: violations.length,
      },
    );
  }
}

export class TenantAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Tenant is already deleted',
      ErrorCode.TENANT_DELETED, // 401004
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class TenantNotDeletedException extends AppException {
  constructor(id: string) {
    super(
      'Tenant is not deleted',
      ErrorCode.TENANT_INVALID_STATUS, // 401005
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id, currentStatus: 'active', requiredStatus: 'deleted' },
    );
  }
}

export class TenantInvalidActionException extends AppException {
  constructor(action: string, reason: string) {
    super(
      `Cannot ${action} tenant: ${reason}`,
      ErrorCode.TENANT_OPERATION_NOT_ALLOWED, // 401006
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        action,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

// Additional useful tenant exceptions that align with our error code system
export class TenantInactiveException extends AppException {
  constructor(id: string) {
    super(
      'Tenant is inactive',
      ErrorCode.TENANT_INACTIVE, // 401003
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

// For batch operations
export class TenantBatchOperationException extends AppException {
  constructor(failedIds: string[], action: string) {
    super(
      `Failed to ${action} one or more tenants`,
      ErrorCode.TENANT_OPERATION_NOT_ALLOWED, // 401006
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        failedIds,
        action,
        count: failedIds.length,
      },
    );
  }
}

export class TenantAlreadyActiveException extends AppException {
  constructor(id: string) {
    super(
      'This tenant is already active',
      ErrorCode.TENANT_INVALID_STATUS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        currentStatus: 'active',
        requestedOperation: 'restore',
      },
    );
  }
}
