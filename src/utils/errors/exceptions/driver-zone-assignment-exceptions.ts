import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

export class DriverZoneAssignmentNotFoundException extends AppException {
  constructor(assignmentId: string) {
    super(
      `Driver zone assignment with ID "${assignmentId}" not found`,
      ErrorCode.DRIVER_ZONE_ASSIGNMENT_NOT_FOUND,
      HttpStatus.NOT_FOUND,
    );
  }
}

export class ZoneAlreadyAssignedException extends AppException {
  constructor(zoneId: string) {
    super(
      `Zone with ID "${zoneId}" is already assigned to a driver`,
      ErrorCode.ZONE_ALREADY_ASSIGNED,
      HttpStatus.CONFLICT,
    );
  }
}

export class DriverNotFoundException extends AppException {
  constructor(driverId: string) {
    super(
      `Driver with ID "${driverId}" not found`,
      ErrorCode.DRIVER_NOT_FOUND,
      HttpStatus.NOT_FOUND,
    );
  }
}

export class ZoneNotFoundException extends AppException {
  constructor(zoneId: string) {
    super(
      `Zone with ID "${zoneId}" not found`,
      ErrorCode.ZONE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
    );
  }
}

export class DriverZoneAssignmentOperationNotAllowedException extends AppException {
  constructor(userId: string, operation: string, reason?: string) {
    super(
      `User with ID "${userId}" is not allowed to ${operation} driver zone assignments${
        reason ? ': ' + reason : ''
      }`,
      ErrorCode.DRIVER_ZONE_ASSIGNMENT_OPERATION_NOT_ALLOWED,
      HttpStatus.FORBIDDEN,
    );
  }
}
