import { HttpStatus } from '@nestjs/common';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';

interface BatchNotFoundContext {
  missingIds: string[];
  foundIds: string[];
  totalRequested: number;
  totalFound: number;
}

export class UserNotFoundException extends AppException {
  constructor(id: string | string[], context?: 'batch' | BatchNotFoundContext) {
    // Handle batch operations
    if (Array.isArray(id)) {
      super(
        'Multiple users not found',
        ErrorCode.USER_NOT_FOUND, // 402001
        HttpStatus.NOT_FOUND,
        { ids: id },
      );
      return;
    }

    // Handle single ID with batch context
    if (context && typeof context === 'object') {
      super(
        'Some requested users were not found',
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
        {
          triggerId: id,
          ...context,
        },
      );
      return;
    }

    // Simple single user not found
    super('User not found', ErrorCode.USER_NOT_FOUND, HttpStatus.NOT_FOUND, {
      id,
    });
  }
}

export class UserAlreadyExistsException extends AppException {
  constructor(email: string, tenantId: string) {
    super(
      'User with this email already exists',
      ErrorCode.USER_ALREADY_EXISTS, // 402002
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        email,
        tenantId,
        constraint: 'unique_email_per_tenant',
      },
    );
  }
}

export class UserInactiveException extends AppException {
  constructor(id: string) {
    super(
      'User is inactive',
      ErrorCode.USER_INACTIVE, // 402003
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class UserAlreadyDeletedException extends AppException {
  constructor(id: string) {
    super(
      'User is already deleted',
      ErrorCode.USER_DELETED, // 402004
      HttpStatus.UNPROCESSABLE_ENTITY,
      { id },
    );
  }
}

export class UserEmailExistsException extends AppException {
  constructor(email: string) {
    super(
      'Email already registered',
      ErrorCode.USER_EMAIL_EXISTS, // 402005
      HttpStatus.UNPROCESSABLE_ENTITY,
      { email },
    );
  }
}

export class UserInvalidStatusException extends AppException {
  constructor(id: string, currentStatus: string, requiredStatus: string) {
    super(
      'Invalid user status for operation',
      ErrorCode.USER_INVALID_STATUS, // 402006
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        currentStatus,
        requiredStatus,
        message: `User status must be ${requiredStatus} but is ${currentStatus}`,
      },
    );
  }
}

export class UserOperationNotAllowedException extends AppException {
  constructor(id: string, operation: string, reason: string) {
    super(
      `Cannot ${operation} user: ${reason}`,
      ErrorCode.USER_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        id,
        operation,
        reason,
        errorType: 'OPERATION_NOT_ALLOWED',
      },
    );
  }
}

export class UserBatchOperationException extends AppException {
  constructor(failedIds: string[], action: string) {
    super(
      `Failed to ${action} one or more users`,
      ErrorCode.USER_OPERATION_NOT_ALLOWED,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        failedIds,
        action,
        count: failedIds.length,
      },
    );
  }
}

export class UserTenantMismatchException extends AppException {
  constructor(userId: string, tenantId: string) {
    super(
      'User does not belong to specified tenant',
      ErrorCode.USER_INVALID_STATUS,
      HttpStatus.UNPROCESSABLE_ENTITY,
      {
        userId,
        tenantId,
        errorType: 'TENANT_MISMATCH',
      },
    );
  }
}
