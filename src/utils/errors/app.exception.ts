import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCode, ErrorCodeType } from './error-codes';

export interface IErrorResponse {
  statusCode: number;
  message: string;
  code: string;
  timestamp: string;
  path?: string;
  details?: Record<string, unknown>;
}

export class AppException extends HttpException {
  constructor(
    message: string,
    errorCode: ErrorCodeType,
    statusCode: HttpStatus,
    details?: Record<string, unknown>,
  ) {
    const response: IErrorResponse = {
      statusCode,
      message,
      code: ErrorCode.toString(Number(errorCode)),
      timestamp: new Date().toISOString(),
      details,
    };

    super(response, statusCode);

    // Add additional properties for debugging and logging
    Object.defineProperties(this, {
      category: {
        value: ErrorCode.getCategory(Number(errorCode)),
        enumerable: true,
      },
      subcategory: {
        value: ErrorCode.getSubcategory(Number(errorCode)),
        enumerable: true,
      },
      errorCode: {
        value: errorCode,
        enumerable: true,
      },
    });
  }

  public setPath(path: string): this {
    const response = this.getResponse() as IErrorResponse;
    response.path = path;
    return this;
  }
}
