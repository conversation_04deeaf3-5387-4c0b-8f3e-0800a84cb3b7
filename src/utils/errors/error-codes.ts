/**
 * Type definition for numeric error codes used throughout the application.
 * Each error code is a 6-digit number representing category, subcategory, and specific error.
 */
type ErrorCodeNumber = number;

/**
 * Base error categories for the application.
 * Each category represents a broad group of related errors.
 *
 * Category codes are two digits (10-69) and form the first two digits of the complete error code.
 *
 * @example
 * For error code 401001:
 * - 40 represents BusinessCategory
 * - 10 represents TenantSubcategory
 * - 01 represents specific error number
 */
enum ErrorCategory {
  /** System and generic errors (10xxxx) */
  SYSTEM = 10,
  /** Authentication and authorization errors (20xxxx) */
  AUTH = 20,
  /** Data validation and processing errors (30xxxx) */
  DATA = 30,
  /** Business logic and domain errors (40xxxx) */
  BUSINESS = 40,
  /** External service integration errors (50xxxx) */
  EXTERNAL = 50,
  /** Infrastructure and deployment errors (60xxxx) */
  INFRASTRUCTURE = 60,
}

/**
 * System-level error subcategories (10xxxx)
 * Used for general system operations, configuration, and validation
 */
enum SystemSubcategory {
  /** Generic system errors (1000xx) */
  GENERIC = 0,
  /** Input validation errors (1001xx) */
  VALIDATION = 1,
  /** System configuration errors (1002xx) */
  CONFIGURATION = 2,
}

/**
 * Authentication and authorization subcategories (20xxxx)
 * Used for access control, user authentication, and token management
 */
enum AuthSubcategory {
  /** Generic auth errors (2000xx) */
  GENERIC = 0,
  /** Authentication process errors (2001xx) */
  AUTHENTICATION = 1,
  /** Authorization and permission errors (2002xx) */
  AUTHORIZATION = 2,
  /** Token management errors (2003xx) */
  TOKEN = 3,
}

/**
 * Data operation subcategories (30xxxx)
 * Used for database operations, data validation, and constraints
 */
enum DataSubcategory {
  /** Generic data errors (3000xx) */
  GENERIC = 0,
  /** Resource not found errors (3001xx) */
  NOT_FOUND = 1,
  /** Duplicate entry errors (3002xx) */
  DUPLICATE = 2,
  /** Invalid data format errors (3003xx) */
  INVALID = 3,
  /** Data constraint violation errors (3004xx) */
  CONSTRAINT = 4,
}

/**
 * Business logic subcategories (40xxxx)
 * Used for domain-specific operations and business rules
 */
enum BusinessSubcategory {
  /** Generic business errors (4000xx) */
  GENERIC = 0,
  /** Tenant management errors (4010xx) */
  TENANT = 1,
  /** User management errors (4020xx) */
  USER = 2,
  /** Customer management errors (4030xx) */
  CUSTOMER = 3,
  /** Order management errors (4040xx) */
  ORDER = 4,
  /** Payment processing errors (4050xx) */
  PAYMENT = 5,
}

/**
 * External service subcategories (50xxxx)
 * Used for integration with external services and APIs
 */
enum ExternalSubcategory {
  /** Generic external service errors (5000xx) */
  GENERIC = 0,
  /** HTTP request errors (5001xx) */
  HTTP = 1,
  /** Database connection errors (5002xx) */
  DATABASE = 2,
  /** File system operation errors (5003xx) */
  FILE = 3,
  /** Email service errors (5004xx) */
  EMAIL = 4,
  /** SMS service errors (5005xx) */
  SMS = 5,
  /** Notification service errors (5006xx) */
  NOTIFICATION = 6,
}

/**
 * Infrastructure subcategories (60xxxx)
 * Used for system infrastructure and deployment
 */
enum InfrastructureSubcategory {
  /** Generic infrastructure errors (6000xx) */
  GENERIC = 0,
  /** Network communication errors (6001xx) */
  NETWORK = 1,
  /** Storage system errors (6002xx) */
  STORAGE = 2,
  /** Cache system errors (6003xx) */
  CACHE = 3,
  /** Message queue errors (6004xx) */
  QUEUE = 4,
}

/**
 * Error Code Structure: CCSSNN
 *
 * @description
 * Each error code is a 6-digit number with the following structure:
 * - CC: Category (10-69)
 * - SS: Subcategory (00-99)
 * - NN: Specific error number (00-99)
 *
 * @example
 * Error code 401001 breaks down as:
 * - 40: Business category
 * - 10: Tenant subcategory
 * - 01: Specific error (not found)
 *
 * Usage in exception:
 * ```typescript
 * throw new TenantNotFoundException(id);
 * // Results in error code: ERR401001
 * ```
 *
 * Frontend handling:
 * ```typescript
 * try {
 *   await api.operation();
 * } catch (error) {
 *   if (error.code === 'ERR401001') {
 *     // Handle tenant not found
 *   } else if (error.code.startsWith('ERR401')) {
 *     // Handle any tenant error
 *   }
 * }
 * ```
 */
export const ErrorCode = {
  //============================================================================
  // SYSTEM ERRORS (10xxxx)
  //============================================================================

  // System Generic Errors (1000xx)
  /** Generic unknown system error */
  UNKNOWN: 100001,

  // System Validation Errors (1001xx)
  /** Input validation error */
  VALIDATION_ERROR: 100101,

  // System Configuration Errors (1002xx)
  /** System configuration error */
  CONFIGURATION_ERROR: 100201,

  //============================================================================
  // AUTH ERRORS (20xxxx)
  //============================================================================

  // Auth Generic Errors (2000xx)

  // Authentication Errors (2001xx)
  /** Authentication required */
  UNAUTHORIZED: 200101,
  /** Invalid credentials provided */
  INVALID_CREDENTIALS: 200102,

  // Authorization Errors (2002xx)
  /** Insufficient permissions */
  FORBIDDEN: 200201,

  // Token Errors (2003xx)
  /** Authentication token has expired */
  TOKEN_EXPIRED: 200301,
  /** Invalid authentication token */
  TOKEN_INVALID: 200302,
  /** Missing authentication token */
  TOKEN_MISSING: 200303,

  //============================================================================
  // DATA ERRORS (30xxxx)
  //============================================================================

  // Data Generic Errors (3000xx)

  // Data Not Found Errors (3001xx)
  /** Resource not found */
  NOT_FOUND: 300101,

  // Data Duplicate Errors (3002xx)
  /** Duplicate entry */
  DUPLICATE_ENTRY: 300201,

  // Data Invalid Format Errors (3003xx)
  /** Invalid data format */
  INVALID_DATA: 300301,

  // Data Constraint Errors (3004xx)
  /** Data constraint violation */
  CONSTRAINT_VIOLATION: 300401,

  //============================================================================
  // BUSINESS ERRORS (40xxxx)
  //============================================================================

  //----------------------------------------------------------------------------
  // Tenant Related Errors (4010xx)
  //----------------------------------------------------------------------------
  /** Tenant not found */
  TENANT_NOT_FOUND: 401001,
  /** Tenant already exists */
  TENANT_ALREADY_EXISTS: 401002,
  /** Tenant is inactive */
  TENANT_INACTIVE: 401003,
  /** Tenant is deleted */
  TENANT_DELETED: 401004,
  /** Invalid tenant status */
  TENANT_INVALID_STATUS: 401005,
  /** Operation not allowed on tenant */
  TENANT_OPERATION_NOT_ALLOWED: 401006,
  /** Unique constraint violation for tenant */
  TENANT_UNIQUE_CONSTRAINT_VIOLATION: 401007,

  //----------------------------------------------------------------------------
  // User Related Errors (4020xx)
  //----------------------------------------------------------------------------
  /** User not found */
  USER_NOT_FOUND: 402001,
  /** User already exists */
  USER_ALREADY_EXISTS: 402002,
  /** User is inactive */
  USER_INACTIVE: 402003,
  /** User is deleted */
  USER_DELETED: 402004,
  /** Email already exists */
  USER_EMAIL_EXISTS: 402005,
  /** Invalid user status */
  USER_INVALID_STATUS: 402006,
  /** User operation not allowed */
  USER_OPERATION_NOT_ALLOWED: 402007,
  /** User credentials expired */
  USER_CREDENTIALS_EXPIRED: 402008,

  //----------------------------------------------------------------------------
  // Customer Related Errors (4030xx)
  //----------------------------------------------------------------------------

  // Vehicle Errors (4030xx)
  /** Vehicle not found */
  VEHICLE_NOT_FOUND: 403001,
  /** Vehicle already exists */
  VEHICLE_ALREADY_EXISTS: 403002,
  /** Vehicle is inactive */
  VEHICLE_INACTIVE: 403003,
  /** Vehicle is deleted */
  VEHICLE_DELETED: 403004,
  /** Invalid vehicle status */
  VEHICLE_INVALID_STATUS: 403005,
  /** Operation not allowed on vehicle */
  VEHICLE_OPERATION_NOT_ALLOWED: 403006,
  /** Vehicle maintenance is overdue */
  VEHICLE_MAINTENANCE_OVERDUE: 403007,

  // Contact Errors (4031xx)
  /** Contact not found */
  CONTACT_NOT_FOUND: 403101,
  /** Contact already exists */
  CONTACT_ALREADY_EXISTS: 403102,
  /** Contact is inactive */
  CONTACT_INACTIVE: 403103,
  /** Contact is deleted */
  CONTACT_DELETED: 403104,
  /** Invalid contact status */
  CONTACT_INVALID_STATUS: 403105,
  /** Operation not allowed on contact */
  CONTACT_OPERATION_NOT_ALLOWED: 403106,
  /** Duplicate contact information */
  CONTACT_DUPLICATE: 403107,
  /** Invalid contact data */
  CONTACT_INVALID_DATA: 403108,
  /** Contact does not belong to customer */
  CONTACT_CUSTOMER_MISMATCH: 403109,

  //----------------------------------------------------------------------------
  // Order Related Errors (4040xx)
  //----------------------------------------------------------------------------

  // Vehicle Type Errors (4040xx)
  /** Vehicle type not found */
  VEHICLE_TYPE_NOT_FOUND: 404001,
  /** Vehicle type already exists */
  VEHICLE_TYPE_ALREADY_EXISTS: 404002,
  /** Vehicle type is inactive */
  VEHICLE_TYPE_INACTIVE: 404003,
  /** Vehicle type is deleted */
  VEHICLE_TYPE_DELETED: 404004,
  /** Invalid vehicle type status */
  VEHICLE_TYPE_INVALID_STATUS: 404005,
  /** Operation not allowed on vehicle type */
  VEHICLE_TYPE_OPERATION_NOT_ALLOWED: 404006,
  /** Vehicle type is in use */
  VEHICLE_TYPE_IN_USE: 404007,

  //----------------------------------------------------------------------------
  // Time Clock Session Errors (4050xx)
  //----------------------------------------------------------------------------
  /** Time clock session not found */
  TIME_CLOCK_SESSION_NOT_FOUND: 405001,
  /** Time clock session already exists */
  TIME_CLOCK_SESSION_ALREADY_EXISTS: 405002,
  /** Invalid time clock session status */
  TIME_CLOCK_SESSION_INVALID_STATUS: 405003,
  /** Time clock session is deleted */
  TIME_CLOCK_SESSION_DELETED: 405004,
  /** Operation not allowed on time clock session */
  TIME_CLOCK_SESSION_OPERATION_NOT_ALLOWED: 405005,
  /** Time clock session overlap */
  TIME_CLOCK_SESSION_OVERLAP: 405006,
  /** Time clock session duration exceeded */
  TIME_CLOCK_SESSION_DURATION_EXCEEDED: 405007,

  //----------------------------------------------------------------------------
  // Zone Errors (4060xx)
  //----------------------------------------------------------------------------
  /** Zone not found */
  ZONE_NOT_FOUND: 406001,
  /** Zone already exists */
  ZONE_ALREADY_EXISTS: 406002,
  /** Zone is deleted */
  ZONE_DELETED: 406003,
  /** Operation not allowed on zone */
  ZONE_OPERATION_NOT_ALLOWED: 406004,
  /** Invalid zone status */
  ZONE_INVALID_STATUS: 406005,
  /** Postal code already exists in zone */
  ZONE_POSTAL_CODE_EXISTS: 406006,
  /** Postal code not found in zone */
  ZONE_POSTAL_CODE_NOT_FOUND: 406007,

  //----------------------------------------------------------------------------
  // Zone Table Errors (4070xx)
  //----------------------------------------------------------------------------
  /** Zone table not found */
  ZONE_TABLE_NOT_FOUND: 407001,
  /** Zone table already exists */
  ZONE_TABLE_ALREADY_EXISTS: 407002,
  /** Zone table is deleted */
  ZONE_TABLE_DELETED: 407003,
  /** Operation not allowed on zone table */
  ZONE_TABLE_OPERATION_NOT_ALLOWED: 407004,
  /** Invalid zone table status */
  ZONE_TABLE_INVALID_STATUS: 407005,
  /** Invalid zone table value */
  ZONE_TABLE_VALUE_INVALID: 407006,
  /** Duplicate zone table value */
  ZONE_TABLE_VALUE_DUPLICATE: 407007,
  /** Zone not found in zone table */
  ZONE_TABLE_ZONE_NOT_FOUND: 407008,

  //----------------------------------------------------------------------------
  // Address Errors (4080xx)
  //----------------------------------------------------------------------------
  /** Address not found */
  ADDRESS_NOT_FOUND: 408001,
  /** Address already exists */
  ADDRESS_ALREADY_EXISTS: 408002,
  /** Address is deleted */
  ADDRESS_DELETED: 408003,
  /** Operation not allowed on address */
  ADDRESS_OPERATION_NOT_ALLOWED: 408004,
  /** Invalid address status */
  ADDRESS_INVALID_STATUS: 408005,
  /** Address validation error */
  ADDRESS_VALIDATION_ERROR: 408006,
  /** Duplicate default address */
  ADDRESS_DUPLICATE_DEFAULT: 408007,
  /** Zone not found for address */
  ADDRESS_ZONE_NOT_FOUND: 408008,
  /** Invalid phone number in address */
  ADDRESS_INVALID_PHONE: 408009,
  /** Invalid email in address */
  ADDRESS_INVALID_EMAIL: 408010,

  //----------------------------------------------------------------------------
  // Permission Errors (4090xx)
  //----------------------------------------------------------------------------
  /** Permission not found */
  PERMISSION_NOT_FOUND: 409001,
  /** Permission already exists */
  PERMISSION_ALREADY_EXISTS: 409002,
  /** Permission is deleted */
  PERMISSION_DELETED: 409003,
  /** Operation not allowed on permission */
  PERMISSION_OPERATION_NOT_ALLOWED: 409004,
  /** Invalid permission status */
  PERMISSION_INVALID_STATUS: 409005,
  /** Invalid permission type */
  PERMISSION_INVALID_TYPE: 409006,
  /** Invalid permission module */
  PERMISSION_INVALID_MODULE: 409007,
  /** Duplicate permission in group */
  PERMISSION_DUPLICATE_IN_GROUP: 409008,
  /** System modification not allowed for permission */
  PERMISSION_SYSTEM_MODIFICATION: 409009,
  /** Permission Denied */
  PERMISSION_DENIED: 409010,

  // Permission Group Errors (4091xx)
  /** Permission group not found */
  PERMISSION_GROUP_NOT_FOUND: 409101,
  /** Permission group already exists */
  PERMISSION_GROUP_ALREADY_EXISTS: 409102,
  /** Permission group is deleted */
  PERMISSION_GROUP_DELETED: 409103,
  /** Operation not allowed on permission group */
  PERMISSION_GROUP_OPERATION_NOT_ALLOWED: 409104,

  //Role Errors(4092xx)
  /** Role not found */
  ROLE_NOT_FOUND: 409201,

  //Role Assignment Errors(4093xx)
  /** Role assignment not found */
  ROLE_ASSIGNMENT_NOT_FOUND: 409301,

  //----------------------------------------------------------------------------
  // Price Modifier Errors (4100xx) and Price Set Errors (4101xx)
  //----------------------------------------------------------------------------
  /** Price modifier not found */
  PRICE_MODIFIER_NOT_FOUND: 410001,
  /** Price modifier already exists */
  PRICE_MODIFIER_ALREADY_EXISTS: 410002,
  /** Operation not allowed on price modifier */
  PRICE_MODIFIER_OPERATION_NOT_ALLOWED: 410003,
  /** Invalid price modifier status */
  PRICE_MODIFIER_INVALID_STATUS: 410004,
  /** Price modifier group not found */
  PRICE_MODIFIER_GROUP_NOT_FOUND: 410005,
  /** Price modifier group already exists */
  PRICE_MODIFIER_GROUP_ALREADY_EXISTS: 410006,
  /** Operation not allowed on price modifier group */
  PRICE_MODIFIER_GROUP_OPERATION_NOT_ALLOWED: 410007,
  /** Tenant mismatch for price modifier */
  PRICE_MODIFIER_TENANT_MISMATCH: 410008,
  /** Tenant mismatch for price modifier group */
  PRICE_MODIFIER_GROUP_TENANT_MISMATCH: 410009,

  //----------------------------------------------------------------------------
  // Driver Errors (4110xx)
  //----------------------------------------------------------------------------
  /** Driver not found */
  DRIVER_NOT_FOUND: 411001,
  /** Driver is inactive */
  DRIVER_INACTIVE: 411002,
  /** Driver license expired */
  DRIVER_LICENSE_EXPIRED: 411003,
  /** Invalid driver authentication credentials */
  DRIVER_AUTH_INVALID_CREDENTIALS: 411004,
  /** Driver authentication token expired */
  DRIVER_AUTH_TOKEN_EXPIRED: 411005,
  /** Driver already exists */
  DRIVER_ALREADY_EXISTS: 411006,

  //----------------------------------------------------------------------------
  // Order Errors (4120xx)
  //----------------------------------------------------------------------------
  /** Order not found */
  ORDER_NOT_FOUND: 412001,
  /** Order not assigned to driver */
  ORDER_NOT_ASSIGNED_TO_DRIVER: 412002,
  /** Invalid order status transition */
  ORDER_INVALID_STATUS_TRANSITION: 412003,
  /** Order update failed */
  ORDER_UPDATE_FAILED: 412004,
  /** Order already assigned */
  ORDER_ALREADY_ASSIGNED: 412005,
  /** Order delivery location not found */
  ORDER_DELIVERY_LOCATION_NOT_FOUND: 412006,
  /** Order pickup location not found */
  ORDER_PICKUP_LOCATION_NOT_FOUND: 412007,
  /** Order requires customer signature */
  ORDER_CUSTOMER_SIGNATURE_REQUIRED: 412008,
  /** Order is locked */
  ORDER_LOCKED: 412009,
  /** Order creation failed */
  ORDER_CREATION_FAILED: 412010,
  /** Order item not found */
  ORDER_ITEM_NOT_FOUND: 412011,
  /** Order completion requirement */
  ORDER_COMPLETION_REQUIREMENT: 412012,
  /** Order delivery time invalid */
  ORDER_DELIVERY_TIME_INVALID: 412013,

  //----------------------------------------------------------------------------
  // TimeClock Errors (4130xx)
  //----------------------------------------------------------------------------
  /** User not clocked in */
  TIMECLOCK_NOT_CLOCKED_IN: 413001,
  /** User already clocked in */
  TIMECLOCK_ALREADY_CLOCKED_IN: 413002,
  /** User not on break */
  TIMECLOCK_NOT_ON_BREAK: 413003,
  /** User already on break */
  TIMECLOCK_ALREADY_ON_BREAK: 413004,
  /** Time clock entry not found */
  TIMECLOCK_ENTRY_NOT_FOUND: 413005,
  /** Vehicle required for time clock */
  TIMECLOCK_VEHICLE_REQUIRED: 413006,
  /** Invalid odometer reading for time clock */
  TIMECLOCK_INVALID_ODOMETER: 413007,

  /** Price set not found */
  PRICE_SET_NOT_FOUND: 410101,
  /** Operation not allowed on price set */
  PRICE_SET_OPERATION_NOT_ALLOWED: 410102,
  PRICE_SET_NOT_AVAILABLE: 410103,

  /** Setting Not Found */
  SETTING_NOT_FOUND: 414001,
  /** Setting already exists */
  SETTING_ALREADY_EXISTS: 414002,

  //----------------------------------------------------------------------------
  // Package Template Errors (4150xx)
  //----------------------------------------------------------------------------
  /** Package template not found */
  PACKAGE_TEMPLATE_NOT_FOUND: 415001,
  /** Package template already exists */
  PACKAGE_TEMPLATE_ALREADY_EXISTS: 415002,
  /** Package template is inactive */
  PACKAGE_TEMPLATE_INACTIVE: 415003,
  /** Package template is deleted */
  PACKAGE_TEMPLATE_DELETED: 415004,
  /** Invalid package template status */
  PACKAGE_TEMPLATE_INVALID_STATUS: 415005,
  /** Operation not allowed on package template */
  PACKAGE_TEMPLATE_OPERATION_NOT_ALLOWED: 415006,
  /** Package template update failed */
  PACKAGE_TEMPLATE_UPDATE_FAILED: 415007,
  /** Package template creation failed */
  PACKAGE_TEMPLATE_CREATION_FAILED: 415008,
  /** Package template restore failed */
  PACKAGE_TEMPLATE_RESTORE_FAILED: 415009,

  //----------------------------------------------------------------------------
  // Driver Zone Assignment Errors (4160xx)
  //----------------------------------------------------------------------------
  /** Driver zone assignment not found */
  DRIVER_ZONE_ASSIGNMENT_NOT_FOUND: 416001,
  /** Zone already assigned to a driver */
  ZONE_ALREADY_ASSIGNED: 416002,
  /** Operation not allowed on driver zone assignment */
  DRIVER_ZONE_ASSIGNMENT_OPERATION_NOT_ALLOWED: 416003,
  /** Driver zone assignment creation failed */
  DRIVER_ZONE_ASSIGNMENT_CREATION_FAILED: 416004,
  /** Driver zone assignment update failed */
  DRIVER_ZONE_ASSIGNMENT_UPDATE_FAILED: 416005,
  /** Driver zone assignment deletion failed */
  DRIVER_ZONE_ASSIGNMENT_DELETION_FAILED: 416006,

  //============================================================================
  // EXTERNAL SERVICE ERRORS (50xxxx)
  //============================================================================

  // External Generic Errors (5000xx)
  /** Generic external service error */
  EXTERNAL_SERVICE_ERROR: 500001,

  // HTTP Errors (5001xx)
  /** HTTP request failed */
  HTTP_REQUEST_FAILED: 500101,

  // Database Errors (5002xx)
  /** Database operation failed */
  DATABASE_ERROR: 500201,

  // File System Errors (5003xx)
  /** File upload failed */
  FILE_UPLOAD_FAILED: 500301,
  /** File not found */
  FILE_NOT_FOUND: 500302,

  // Email Errors (5004xx)
  /** Email sending failed */
  EMAIL_SEND_FAILED: 500401,

  // SMS Errors (5005xx)
  /** SMS sending failed */
  SMS_SEND_FAILED: 500501,
  /** SMS provider configuration error */
  SMS_PROVIDER_CONFIG_ERROR: 500502,
  /** SMS provider service error */
  SMS_PROVIDER_SERVICE_ERROR: 500503,

  // Notification Errors (5006xx)
  /** Notification template not found */
  NOTIFICATION_TEMPLATE_NOT_FOUND: 500601,
  /** Notification template rendering failed */
  NOTIFICATION_TEMPLATE_RENDERING_FAILED: 500602,
  /** Notification sending failed */
  NOTIFICATION_SEND_FAILED: 500603,
  /** Notification recipient email required */
  NOTIFICATION_RECIPIENT_EMAIL_REQUIRED: 500604,
  /** Notification recipient phone required */
  NOTIFICATION_RECIPIENT_PHONE_REQUIRED: 500605,
  /** Notification template creation failed */
  NOTIFICATION_TEMPLATE_CREATION_FAILED: 500606,

  //============================================================================
  // INFRASTRUCTURE ERRORS (60xxxx)
  //============================================================================

  // Infrastructure Generic Errors (6000xx)
  /** Generic infrastructure error */
  INFRASTRUCTURE_ERROR: 600001,

  // Network Errors (6001xx)
  /** Network communication error */
  NETWORK_ERROR: 600101,

  // Storage Errors (6002xx)
  /** Storage system error */
  STORAGE_ERROR: 600201,

  // Cache Errors (6003xx)
  /** Cache system error */
  CACHE_ERROR: 600301,

  // Queue Errors (6004xx)
  /** Message queue error */
  QUEUE_ERROR: 600401,

  /**
   * Get the category name for an error code
   *
   * @param code - The numeric error code
   * @returns The category name or 'UNKNOWN'
   *
   * @example
   * ```typescript
   * ErrorCode.getCategory(401001) // Returns "BUSINESS"
   * ```
   */
  getCategory(code: ErrorCodeNumber): string {
    const category = Math.floor(code / 10000);
    return ErrorCategory[ErrorCategory[category]] || 'UNKNOWN';
  },

  /**
   * Get the subcategory number from an error code
   *
   * @param code - The numeric error code
   * @returns The subcategory number
   *
   * @example
   * ```typescript
   * ErrorCode.getSubcategoryNumber(401001) // Returns 10
   * ```
   */
  getSubcategoryNumber(code: ErrorCodeNumber): number {
    return Math.floor((code % 10000) / 100);
  },

  /**
   * Get the subcategory name for an error code
   *
   * @param code - The numeric error code
   * @returns The subcategory name or 'UNKNOWN'
   *
   * @example
   * ```typescript
   * ErrorCode.getSubcategory(401001) // Returns "TENANT"
   * ```
   */
  getSubcategory(code: ErrorCodeNumber): string {
    const category = Math.floor(code / 10000);
    const subcategory = this.getSubcategoryNumber(code);

    switch (category) {
      case ErrorCategory.SYSTEM:
        return SystemSubcategory[SystemSubcategory[subcategory]] || 'UNKNOWN';
      case ErrorCategory.AUTH:
        return AuthSubcategory[AuthSubcategory[subcategory]] || 'UNKNOWN';
      case ErrorCategory.DATA:
        return DataSubcategory[DataSubcategory[subcategory]] || 'UNKNOWN';
      case ErrorCategory.BUSINESS:
        return (
          BusinessSubcategory[BusinessSubcategory[subcategory]] || 'UNKNOWN'
        );
      case ErrorCategory.EXTERNAL:
        return (
          ExternalSubcategory[ExternalSubcategory[subcategory]] || 'UNKNOWN'
        );
      case ErrorCategory.INFRASTRUCTURE:
        return (
          InfrastructureSubcategory[InfrastructureSubcategory[subcategory]] ||
          'UNKNOWN'
        );
      default:
        return 'UNKNOWN';
    }
  },

  /**
   * Check if an error code belongs to a specific category
   *
   * @param code - The numeric error code
   * @param category - The category to check against
   * @returns True if the code belongs to the category
   *
   * @example
   * ```typescript
   * ErrorCode.isCategory(401001, ErrorCategory.BUSINESS) // Returns true
   * ```
   */
  isCategory(code: ErrorCodeNumber, category: ErrorCategory): boolean {
    return Math.floor(code / 10000) === category;
  },

  /**
   * Format an error code as a string
   *
   * @param code - The numeric error code
   * @returns Formatted error code string (e.g., "ERR401001")
   *
   * @example
   * ```typescript
   * ErrorCode.toString(401001) // Returns "ERR401001"
   * ```
   */
  toString(code: ErrorCodeNumber): string {
    return code.toString();
  },

  /**
   * Get the specific error number from an error code
   *
   * @param code - The numeric error code
   * @returns The specific error number (last two digits)
   *
   * @example
   * ```typescript
   * ErrorCode.getErrorNumber(401001) // Returns 1
   * ```
   */
  getErrorNumber(code: ErrorCodeNumber): number {
    return code % 100;
  },
} as const;

/**
 * Type representing all possible error codes in the application
 *
 * @example
 * ```typescript
 * function handleError(code: ErrorCodeType) {
 *   console.log(ErrorCode.toString(code));
 * }
 * ```
 */
export type ErrorCodeType = (typeof ErrorCode)[keyof typeof ErrorCode];
