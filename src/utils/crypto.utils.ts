import * as crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;

export class CryptoUtils {
  private readonly key: Buffer;

  constructor(secretKey: string) {
    this.key = crypto.createHash('sha256').update(secretKey).digest();
  }

  encrypt(data: any): string {
    const iv = crypto.randomBytes(IV_LENGTH);

    const cipher = crypto.createCipheriv(ALGORITHM, this.key, iv);

    const text = typeof data === 'string' ? data : JSON.stringify(data);

    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');

    const authTag = cipher.getAuthTag();

    return (
      Buffer.from(iv).toString('base64') +
      ':' +
      encrypted +
      ':' +
      authTag.toString('base64')
    );
  }

  decrypt(encryptedData: string): any {
    const parts = encryptedData.split(':');
    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(parts[0], 'base64');
    const encrypted = parts[1];
    const authTag = Buffer.from(parts[2], 'base64');

    const decipher = crypto.createDecipheriv(ALGORITHM, this.key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'base64', 'utf8');
    decrypted += decipher.final('utf8');

    try {
      return JSON.parse(decrypted);
    } catch {
      return decrypted;
    }
  }
}
