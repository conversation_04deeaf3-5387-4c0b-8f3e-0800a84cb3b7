import { TransformFnParams } from 'class-transformer/types/interfaces';
import { MaybeType } from '../types/maybe.type';

export const lowerCaseTransformer = (
  params: TransformFnParams,
): MaybeType<string> => params.value?.toLowerCase().trim();

export const trimTransformer = (
  params: TransformFnParams,
): MaybeType<string | Array<string>> => {
  if (typeof params.value === 'string') {
    return params.value.trim();
  }
  if (Array.isArray(params.value)) {
    return params.value.map((item) => {
      if (typeof item === 'string') {
        return item.trim();
      }
      return item;
    });
  }
  return params.value;
};
