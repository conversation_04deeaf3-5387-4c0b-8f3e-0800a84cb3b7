import { trace } from '@opentelemetry/api';

export function Trace(name?: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      const tracer = trace.getTracer('app-tracer');
      const spanName = name || `${target.constructor.name}.${propertyKey}`;

      return tracer.startActiveSpan(spanName, async (span) => {
        try {
          return await originalMethod.apply(this, args);
        } catch (error) {
          span.recordException(error);
          throw error;
        } finally {
          span.end();
        }
      });
    };
    return descriptor;
  };
}
