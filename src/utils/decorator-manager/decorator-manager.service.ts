import { Injectable } from '@nestjs/common';
import { DecoratorConfig, DecoratorOptions } from './types';

@Injectable()
export class DecoratorManagerService {
  private decorators: Map<string, DecoratorMetadata>;

  constructor() {
    this.decorators = new Map();
  }

  register(config: DecoratorConfig, options?: DecoratorOptions): void {
    const metadata: DecoratorMetadata = {
      key: config.key,
      type: config.type,
      value: {
        metadata: config.metadata,
        options,
      },
    };

    this.decorators.set(config.key, metadata);
  }

  get(key: string): DecoratorMetadata | undefined {
    return this.decorators.get(key);
  }

  has(key: string): boolean {
    return this.decorators.has(key);
  }

  list(): DecoratorMetadata[] {
    return Array.from(this.decorators.values());
  }

  remove(key: string): boolean {
    return this.decorators.delete(key);
  }
}
