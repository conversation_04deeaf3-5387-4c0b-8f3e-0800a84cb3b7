import { DecoratorManagerService } from './decorator-manager.service';
import { DecoratorConfig, DecoratorOptions } from './types';

/**
 * Abstract base class for creating decorators with built-in management capabilities.
 *
 * @abstract
 * @class BaseDecorator
 *
 * @example
 * ```typescript
 * // Creating a custom decorator
 * class ApiDecorator extends BaseDecorator {
 *   constructor(manager: DecoratorManagerService) {
 *     super(
 *       manager,
 *       {
 *         key: 'api',
 *         type: 'class',
 *         metadata: { version: '1.0' }
 *       },
 *       {
 *         name: 'Api',
 *         description: 'API documentation decorator'
 *       }
 *     );
 *   }
 *
 *   build(config: ApiConfig): ClassDecorator {
 *     // Implement decorator logic
 *     return (target) => {
 *       // Decorator implementation
 *     };
 *   }
 * }
 *
 * // Using the decorator
 * @Api({
 *   path: '/users',
 *   version: '1'
 * })
 * class UserController {}
 * ```
 */
export abstract class BaseDecorator {
  /**
   * Creates an instance of BaseDecorator.
   *
   * @param {DecoratorManagerService} manager - Service to manage decorator registration and metadata
   * @param {DecoratorConfig} config - Configuration for the decorator including key, type and metadata
   * @param {DecoratorOptions} [options] - Optional settings like name, description, version
   *
   * @throws {Error} Throws if required configuration is missing
   */
  protected constructor(
    protected readonly manager: DecoratorManagerService,
    protected readonly config: DecoratorConfig,
    protected readonly options?: DecoratorOptions,
  ) {
    this.manager.register(config, options);
  }

  /**
   * Builds and returns the actual decorator function.
   * Must be implemented by decorator classes extending BaseDecorator.
   *
   * @abstract
   * @param {...any[]} args - Arguments passed to the decorator
   * @returns {Function} The decorator function
   *
   * @remarks
   * The returned function should be properly typed based on the decorator type:
   * - For method decorators: MethodDecorator
   * - For class decorators: ClassDecorator
   * - For property decorators: PropertyDecorator
   * - For parameter decorators: ParameterDecorator
   */
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  abstract build(...args: any[]): Function;
}
