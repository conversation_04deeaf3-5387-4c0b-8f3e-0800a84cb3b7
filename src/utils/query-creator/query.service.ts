import { Injectable } from '@nestjs/common';
import {
  DataSource,
  ObjectLiteral,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { BaseQueryParams, PaginatedResult } from './interfaces';

@Injectable()
export class QueryService {
  constructor(private readonly dataSource: DataSource) {}

  async executeQuery<TEntity extends ObjectLiteral, TDomain>(
    options: {
      repository: Repository<TEntity>;
      queryParams: BaseQueryParams;
      alias: string;
      mapToDomain: (entity: TEntity) => TDomain;
      baseConditions?: Record<string, any>;
      joins?: Array<{
        property: string;
        alias: string;
        condition?: string;
      }>;
    },
    customizeQuery?: (queryBuilder: SelectQueryBuilder<TEntity>) => void,
  ): Promise<PaginatedResult<TDomain>> {
    const {
      repository,
      queryParams,
      alias,
      mapToDomain,
      baseConditions = {},
      joins = [],
    } = options;

    const queryBuilder = repository.createQueryBuilder(alias);

    // Apply base conditions
    Object.entries(baseConditions).forEach(([key, value]) => {
      queryBuilder.andWhere(`${alias}.${key} = :${key}`, { [key]: value });
    });

    // Apply joins
    joins.forEach(({ property, alias: joinAlias, condition }) => {
      queryBuilder.innerJoin(`${alias}.${property}`, joinAlias);
      if (condition) {
        queryBuilder.andWhere(condition);
      }
    });

    // Apply custom query logic
    if (customizeQuery) {
      customizeQuery(queryBuilder);
    }

    // Apply search if provided
    if (queryParams.search && typeof queryParams.search === 'string') {
      // Implement in the custom query function
    }

    // Apply ordering
    const orderBy = queryParams.orderBy || 'createdAt';
    const order = queryParams.order || 'DESC';
    queryBuilder.orderBy(`${alias}.${orderBy}`, order);

    // Apply pagination
    const page = queryParams.page || 1;
    const limit = Math.min(queryParams.limit || 10, 50);
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    // Execute query
    const [entities, total] = await queryBuilder.getManyAndCount();
    const data = entities.map(mapToDomain);
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }
}
