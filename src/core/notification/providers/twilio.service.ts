import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SmsProvider, SmsProviderOptions } from './sms-provider.interface';
import { NotificationConfig } from '../config/notification-config.type';
import {
  SmsProviderConfigurationException,
  SmsProviderServiceException,
} from '../../../utils/errors/exceptions/notification-exceptions';
import * as twilio from 'twilio';

@Injectable()
export class TwilioService implements SmsProvider {
  private readonly logger = new Logger(TwilioService.name);
  private readonly twilioClient: twilio.Twilio; // Twilio client
  private readonly smsEnabled: boolean;
  private readonly mockMode: boolean;
  private readonly fromNumber: string;

  constructor(private configService: ConfigService) {
    const notificationConfig = this.configService.get<NotificationConfig>(
      'notification',
      { infer: true },
    );

    if (!notificationConfig) {
      throw new SmsProviderConfigurationException(
        'Notification configuration is missing',
      );
    }

    this.smsEnabled = notificationConfig.sms.enabled;
    this.mockMode = notificationConfig.sms.mockInDevelopment;
    this.fromNumber = notificationConfig.sms.twilioFromNumber || '';

    // Only initialize Twilio if SMS is enabled
    if (this.smsEnabled && !this.mockMode) {
      const twilioAccountSid = notificationConfig.sms.twilioAccountSid;
      const twilioAuthToken = notificationConfig.sms.twilioAuthToken;

      if (!twilioAccountSid || !twilioAuthToken) {
        throw new SmsProviderConfigurationException(
          'Twilio credentials are missing',
        );
      }

      try {
        this.twilioClient = twilio.default(twilioAccountSid, twilioAuthToken);
        this.logger.log('Twilio client initialized for production');
      } catch (error) {
        this.logger.error('Failed to initialize Twilio client', error);
        throw new SmsProviderConfigurationException(
          `Failed to initialize Twilio client: ${error.message}`,
        );
      }
    } else {
      this.logger.log(
        this.smsEnabled
          ? 'Using mock SMS provider for development'
          : 'SMS sending is disabled',
      );
    }
  }

  async sendSms({
    recipientPhone,
    message,
    from,
  }: SmsProviderOptions): Promise<boolean> {
    // If SMS is disabled, log and return success (pretend it worked)
    if (!this.smsEnabled) {
      this.logger.log(
        `[SMS DISABLED] Would send to: ${recipientPhone}, Message: ${message}`,
      );
      return true;
    }

    const fromNumber = from || this.fromNumber;

    if (!fromNumber && !this.mockMode) {
      throw new SmsProviderConfigurationException(
        'From number is required for sending SMS',
      );
    }

    if (this.mockMode) {
      // In development, just log the SMS
      this.logger.log(
        `[MOCK SMS] To: ${recipientPhone}, From: ${fromNumber || 'DEFAULT'}, Message: ${message}`,
      );
      return true;
    }

    try {
      // In production, use the Twilio client to send SMS
      const result = await this.twilioClient.messages.create({
        body: message,
        from: fromNumber,
        to: recipientPhone,
      });

      this.logger.log(`SMS sent to ${recipientPhone}, SID: ${result.sid}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send SMS to ${recipientPhone}`, error);
      throw new SmsProviderServiceException(
        `Failed to send SMS to ${recipientPhone}: ${error.message}`,
      );
    }
  }
}
