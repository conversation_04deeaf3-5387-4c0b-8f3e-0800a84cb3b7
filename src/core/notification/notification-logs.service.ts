import { Injectable } from '@nestjs/common';
import { NotificationLogRepository } from './infrastructure/repositories/notification-log.repository';
import { NotificationLogResponseDto } from './dto/notification-log-response.dto';
import { plainToClass } from 'class-transformer';
import { NotificationChannel } from './types/notification.types';

@Injectable()
export class NotificationLogsService {
  constructor(
    private readonly notificationLogRepository: NotificationLogRepository,
  ) {}

  /**
   * Find all notification logs with pagination
   */
  async findAll(
    limit = 100,
    offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    const [logs, count] = await this.notificationLogRepository.findByDateRange(
      new Date(0), // from beginning of time
      new Date(), // to now
      limit,
      offset,
    );

    return {
      items: logs.map((log) => plainToClass(NotificationLogResponseDto, log)),
      count,
    };
  }

  /**
   * Find notification logs by tenant id
   */
  async findByTenant(
    tenantId: string,
    limit = 100,
    offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    const [logs, count] = await this.notificationLogRepository.findByTenantId(
      tenantId,
      limit,
      offset,
    );

    return {
      items: logs.map((log) => plainToClass(NotificationLogResponseDto, log)),
      count,
    };
  }

  /**
   * Find notification logs by type and optionally channel
   */
  async findByType(
    type: string,
    channelStr?: string,
    limit = 100,
    offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    let channel: NotificationChannel | undefined = undefined;

    if (
      channelStr &&
      Object.values(NotificationChannel).includes(
        channelStr as NotificationChannel,
      )
    ) {
      channel = channelStr as NotificationChannel;
    }

    const [logs, count] =
      await this.notificationLogRepository.findByTypeAndChannel(
        type,
        channel,
        limit,
        offset,
      );

    return {
      items: logs.map((log) => plainToClass(NotificationLogResponseDto, log)),
      count,
    };
  }

  /**
   * Find notification logs by recipient (email or phone)
   */
  async findByRecipient(
    email?: string,
    phone?: string,
    limit = 100,
    offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    const [logs, count] = await this.notificationLogRepository.findByRecipient(
      email,
      phone,
      limit,
      offset,
    );

    return {
      items: logs.map((log) => plainToClass(NotificationLogResponseDto, log)),
      count,
    };
  }

  /**
   * Get statistics for a notification type within a date range
   */
  async getStatsByType(
    type: string,
    startDateStr?: string,
    endDateStr?: string,
  ): Promise<{
    total: number;
    successful: number;
    failed: number;
    successRate: number;
  }> {
    const startDate = startDateStr ? new Date(startDateStr) : new Date(0);
    const endDate = endDateStr ? new Date(endDateStr) : new Date();

    const total = await this.notificationLogRepository.countByTypeAndDateRange(
      type,
      startDate,
      endDate,
    );

    const successful =
      await this.notificationLogRepository.countSuccessfulByTypeAndDateRange(
        type,
        startDate,
        endDate,
      );

    const failed = total - successful;
    const successRate = total > 0 ? (successful / total) * 100 : 0;

    return {
      total,
      successful,
      failed,
      successRate,
    };
  }
}
