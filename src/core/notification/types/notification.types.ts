export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
}

export enum NotificationType {
  ORDER_CREATED = 'order_created',
  ORDER_ASSIGNED = 'order_assigned',
  ORDER_STARTED = 'order_started',
  ORDER_COMPLETED = 'order_completed',
  ORDER_STATUS_CHANGED = 'order_status_changed',
  DRIVER_ASSIGNED = 'driver_assigned',
  DELIVERY_REMINDER = 'delivery_reminder',
}

export enum NotificationTemplate {
  ORDER_CREATED_EMAIL = 'order-created-email',
  ORDER_CREATED_SMS = 'order-created-sms',
  ORDER_ASSIGNED_EMAIL = 'order-assigned-email',
  ORDER_ASSIGNED_SMS = 'order-assigned-sms',
  ORDER_STARTED_EMAIL = 'order-started-email',
  ORDER_STARTED_SMS = 'order-started-sms',
  ORDER_COMPLETED_EMAIL = 'order-completed-email',
  ORDER_COMPLETED_SMS = 'order-completed-sms',
  ORDER_STATUS_CHANGED_EMAIL = 'order-status-changed-email',
  ORDER_STATUS_CHANGED_SMS = 'order-status-changed-sms',
  DRIVER_ASSIGNED_EMAIL = 'driver-assigned-email',
  DRIVER_ASSIGNED_SMS = 'driver-assigned-sms',
  DELIVERY_REMINDER_EMAIL = 'delivery-reminder-email',
  DELIVERY_REMINDER_SMS = 'delivery-reminder-sms',
}

export interface NotificationPayload<T = any> {
  type: NotificationType;
  recipientEmail?: string;
  recipientPhone?: string;
  data: T;
  channel: NotificationChannel;
  tenantId: string;
}

export interface NotificationTemplateContext {
  [key: string]: any;
}
