import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotificationTemplateEntity } from '../entities/notification-template.entity';
import { NotificationChannel } from '../../types/notification.types';

@Injectable()
export class NotificationTemplateRepository {
  constructor(
    @InjectRepository(NotificationTemplateEntity)
    private notificationTemplateRepository: Repository<NotificationTemplateEntity>,
  ) {}

  async findAll(): Promise<NotificationTemplateEntity[]> {
    return this.notificationTemplateRepository.find();
  }

  async findAllActive(): Promise<NotificationTemplateEntity[]> {
    return this.notificationTemplateRepository.find({
      where: { isActive: true },
    });
  }

  async findById(id: string): Promise<NotificationTemplateEntity | null> {
    return this.notificationTemplateRepository.findOne({
      where: { id },
    });
  }

  async findByName(name: string): Promise<NotificationTemplateEntity | null> {
    return this.notificationTemplateRepository.findOne({
      where: { name },
    });
  }

  async findByTypeAndChannel(
    type: string,
    channel: NotificationChannel,
  ): Promise<NotificationTemplateEntity | null> {
    return this.notificationTemplateRepository.findOne({
      where: {
        type,
        channel,
        isActive: true,
      },
    });
  }

  async create(
    template: Partial<NotificationTemplateEntity>,
  ): Promise<NotificationTemplateEntity> {
    const newTemplate = this.notificationTemplateRepository.create(template);
    return this.notificationTemplateRepository.save(newTemplate);
  }

  async update(
    id: string,
    template: Partial<NotificationTemplateEntity>,
  ): Promise<NotificationTemplateEntity | null> {
    await this.notificationTemplateRepository.update(id, template);
    return this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.notificationTemplateRepository.delete(id);
    return result.affected ? result.affected > 0 : false;
  }

  async deactivate(id: string): Promise<NotificationTemplateEntity | null> {
    await this.notificationTemplateRepository.update(id, { isActive: false });
    return this.findById(id);
  }

  async activate(id: string): Promise<NotificationTemplateEntity | null> {
    await this.notificationTemplateRepository.update(id, { isActive: true });
    return this.findById(id);
  }
}
