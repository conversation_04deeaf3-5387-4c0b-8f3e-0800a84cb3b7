import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between } from 'typeorm';
import { NotificationLogEntity } from '../entities/notification-log.entity';
import { NotificationChannel } from '../../types/notification.types';

@Injectable()
export class NotificationLogRepository {
  constructor(
    @InjectRepository(NotificationLogEntity)
    private notificationLogRepository: Repository<NotificationLogEntity>,
  ) {}

  async create(
    log: Partial<NotificationLogEntity>,
  ): Promise<NotificationLogEntity> {
    const newLog = this.notificationLogRepository.create(log);
    return this.notificationLogRepository.save(newLog);
  }

  async findById(id: string): Promise<NotificationLogEntity | null> {
    return this.notificationLogRepository.findOne({
      where: { id },
    });
  }

  async findByTenantId(
    tenantId: string,
    limit = 100,
    offset = 0,
  ): Promise<[NotificationLogEntity[], number]> {
    return this.notificationLogRepository.findAndCount({
      where: { tenantId },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });
  }

  async findByTypeAndChannel(
    type: string,
    channel?: NotificationChannel,
    limit = 100,
    offset = 0,
  ): Promise<[NotificationLogEntity[], number]> {
    const where: FindOptionsWhere<NotificationLogEntity> = { type };

    if (channel) {
      where.channel = channel;
    }

    return this.notificationLogRepository.findAndCount({
      where,
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });
  }

  async findByRecipient(
    email?: string,
    phone?: string,
    limit = 100,
    offset = 0,
  ): Promise<[NotificationLogEntity[], number]> {
    const where: FindOptionsWhere<NotificationLogEntity> = {};

    if (email) {
      where.recipientEmail = email;
    }

    if (phone) {
      where.recipientPhone = phone;
    }

    if (!email && !phone) {
      return [[], 0];
    }

    return this.notificationLogRepository.findAndCount({
      where,
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });
  }

  async findByDateRange(
    startDate: Date,
    endDate: Date,
    limit = 100,
    offset = 0,
  ): Promise<[NotificationLogEntity[], number]> {
    return this.notificationLogRepository.findAndCount({
      where: {
        createdAt: Between(startDate, endDate),
      },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });
  }

  async countByTypeAndDateRange(
    type: string,
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    return this.notificationLogRepository.count({
      where: {
        type,
        createdAt: Between(startDate, endDate),
      },
    });
  }

  async countSuccessfulByTypeAndDateRange(
    type: string,
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    return this.notificationLogRepository.count({
      where: {
        type,
        success: true,
        createdAt: Between(startDate, endDate),
      },
    });
  }
}
