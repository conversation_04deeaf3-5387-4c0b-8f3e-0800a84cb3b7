import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationTemplateEntity } from './entities/notification-template.entity';
import { NotificationLogEntity } from './entities/notification-log.entity';
import { NotificationTemplateRepository } from './repositories/notification-template.repository';
import { NotificationLogRepository } from './repositories/notification-log.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      NotificationTemplateEntity,
      NotificationLogEntity,
    ]),
  ],
  providers: [NotificationTemplateRepository, NotificationLogRepository],
  exports: [NotificationTemplateRepository, NotificationLogRepository],
})
export class NotificationRelationalPersistenceModule {}
