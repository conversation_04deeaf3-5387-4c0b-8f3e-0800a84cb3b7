import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { NotificationChannel } from '../../types/notification.types';

@Entity('notification_logs')
export class NotificationLogEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  type: string;

  @Column({
    type: 'enum',
    enum: NotificationChannel,
    nullable: false,
  })
  channel: NotificationChannel;

  @Column({ type: 'varchar', length: 255, nullable: true })
  recipientEmail?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  recipientPhone?: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  templateId: string;

  @Column({ type: 'uuid', nullable: false })
  tenantId: string;

  @Column({ type: 'jsonb', nullable: true })
  payload?: Record<string, any>;

  @Column({ type: 'boolean', default: true })
  success: boolean;

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @CreateDateColumn()
  createdAt: Date;
}
