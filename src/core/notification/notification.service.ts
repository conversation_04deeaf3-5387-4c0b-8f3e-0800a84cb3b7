import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Handlebars from 'handlebars';
import { MailerService } from '../mailer/mailer.service';
import { TwilioService } from './providers/twilio.service';
import {
  NotificationChannel,
  NotificationPayload,
  NotificationTemplateContext,
} from './types/notification.types';
import {
  NotificationTemplateNotFoundException,
  NotificationTemplateRenderingFailedException,
  NotificationSendFailedException,
  NotificationRecipientEmailRequiredException,
  NotificationRecipientPhoneRequiredException,
} from '../../utils/errors/exceptions/notification-exceptions';
import { NotificationConfig } from './config/notification-config.type';
import { NotificationTemplateRepository } from './infrastructure/repositories/notification-template.repository';
import { NotificationLogRepository } from './infrastructure/repositories/notification-log.repository';

@Injectable()
export class NotificationService implements OnModuleInit {
  private readonly logger = new Logger(NotificationService.name);
  private readonly compiledTemplates: Map<string, HandlebarsTemplateDelegate> =
    new Map();

  constructor(
    private configService: ConfigService,
    private mailerService: MailerService,
    private twilioService: TwilioService,
    private notificationTemplateRepository: NotificationTemplateRepository,
    private notificationLogRepository: NotificationLogRepository,
  ) {}

  async onModuleInit() {
    this.registerHelpers();
    await this.precompileTemplates();
  }

  private registerHelpers(): void {
    // Register Handlebars helpers
    Handlebars.registerHelper('formatDate', (date) => {
      if (!date) return '';
      const formattedDate = new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      return formattedDate;
    });

    Handlebars.registerHelper('currentYear', () => {
      return new Date().getFullYear();
    });

    Handlebars.registerHelper('lowerCase', (str) => {
      return str ? str.toLowerCase() : '';
    });
  }

  private async precompileTemplates(): Promise<void> {
    try {
      // Fetch all active templates from the database
      const templates =
        await this.notificationTemplateRepository.findAllActive();

      for (const template of templates) {
        try {
          // Compile the template and store it in the map
          const compiledTemplate = Handlebars.compile(template.content);
          this.compiledTemplates.set(template.name, compiledTemplate);
          this.logger.log(`Template compiled: ${template.name}`);
        } catch (error) {
          this.logger.error(
            `Failed to compile template ${template.name}`,
            error,
          );
        }
      }

      this.logger.log(`Precompiled ${this.compiledTemplates.size} templates`);
    } catch (error) {
      this.logger.error('Failed to precompile templates', error);
    }
  }

  /**
   * Render a template with the provided context
   */
  private async renderTemplate(
    templateName: string,
    context: NotificationTemplateContext,
  ): Promise<string> {
    let compiledTemplate = this.compiledTemplates.get(templateName);

    if (!compiledTemplate) {
      // Try to load the template from the database if it's not in memory
      const template =
        await this.notificationTemplateRepository.findByName(templateName);

      if (!template) {
        throw new NotificationTemplateNotFoundException(templateName);
      }

      try {
        // Compile the template and cache it for future use
        compiledTemplate = Handlebars.compile(template.content);
        this.compiledTemplates.set(templateName, compiledTemplate);
      } catch (error) {
        this.logger.error(`Failed to compile template ${templateName}`, error);
        throw new NotificationTemplateRenderingFailedException(templateName);
      }
    }

    try {
      return compiledTemplate(context);
    } catch (error) {
      this.logger.error(`Failed to render template ${templateName}`, error);
      throw new NotificationTemplateRenderingFailedException(templateName);
    }
  }

  /**
   * Send a notification based on the provided payload
   */
  async sendNotification(payload: NotificationPayload): Promise<boolean> {
    const { type, channel, data, recipientEmail, recipientPhone, tenantId } =
      payload;

    // Get notification configuration
    const notificationConfig = this.configService.get<NotificationConfig>(
      'notification',
      { infer: true },
    );

    // Check if this channel is enabled
    const channelEnabled =
      channel === NotificationChannel.EMAIL
        ? notificationConfig.email.enabled
        : notificationConfig.sms.enabled;

    // If channel is disabled, log and return success without sending
    if (!channelEnabled) {
      this.logger.log(
        `Notification channel ${channel} is disabled. Skipping notification of type ${type}`,
      );

      // Create a notification log entry for skipped notification
      await this.notificationLogRepository.create({
        type,
        channel,
        recipientEmail,
        recipientPhone,
        templateId: 'channel-disabled',
        tenantId,
        payload: data,
        success: false,
        errorMessage: `Notification channel ${channel} is disabled`,
      });

      return true; // Return success as this is an expected behavior
    }

    if (channel === NotificationChannel.EMAIL && !recipientEmail) {
      throw new NotificationRecipientEmailRequiredException();
    }

    if (channel === NotificationChannel.SMS && !recipientPhone) {
      throw new NotificationRecipientPhoneRequiredException();
    }

    try {
      // Determine the template name based on notification type and channel
      const templateName = `${type.replace(/_/g, '-')}-${channel}`;

      // Get the template from the database
      const template =
        await this.notificationTemplateRepository.findByTypeAndChannel(
          type,
          channel,
        );

      if (!template) {
        throw new NotificationTemplateNotFoundException(templateName);
      }

      // Render the template with the provided data
      const renderedContent = await this.renderTemplate(templateName, data);

      // Create a notification log entry
      const logEntry = {
        type,
        channel,
        recipientEmail,
        recipientPhone,
        templateId: template.id,
        tenantId,
        payload: data,
        success: true,
      };

      // Send the notification through the appropriate channel
      if (channel === NotificationChannel.EMAIL && recipientEmail) {
        await this.mailerService.sendMail({
          to: recipientEmail,
          subject: template.subject || this.getSubjectForNotificationType(type),
          html: renderedContent,
          templatePath: '', // Using our own rendered HTML
          context: {},
        });

        this.logger.log(
          `Email notification sent to ${recipientEmail} (${type})`,
        );
      } else if (channel === NotificationChannel.SMS && recipientPhone) {
        await this.twilioService.sendSms({
          recipientPhone,
          message: renderedContent,
        });

        this.logger.log(`SMS notification sent to ${recipientPhone} (${type})`);
      }

      // Save the successful notification log
      await this.notificationLogRepository.create(logEntry);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send ${channel} notification (${type})`,
        error,
      );

      // Log the failed notification
      if (tenantId) {
        try {
          await this.notificationLogRepository.create({
            type,
            channel,
            recipientEmail,
            recipientPhone,
            templateId: '',
            tenantId,
            payload: data,
            success: false,
            errorMessage: error.message,
          });
        } catch (logError) {
          this.logger.error(
            'Failed to create notification log entry',
            logError,
          );
        }
      }

      if (
        error instanceof NotificationTemplateNotFoundException ||
        error instanceof NotificationTemplateRenderingFailedException ||
        error instanceof NotificationRecipientEmailRequiredException ||
        error instanceof NotificationRecipientPhoneRequiredException
      ) {
        throw error;
      }

      throw new NotificationSendFailedException(error.message);
    }
  }

  /**
   * Get a subject line for a notification type
   */
  private getSubjectForNotificationType(type: string): string {
    const subjectMap = {
      order_created: 'Your Order Confirmation',
      order_assigned: 'Your Order Has Been Assigned',
      order_started: 'Your Order Is In Progress',
      order_completed: 'Your Order Has Been Completed',
      order_status_changed: 'Order Status Update',
      driver_assigned: 'Driver Assignment Notification',
      delivery_reminder: 'Upcoming Delivery Reminder',
    };

    return subjectMap[type] || 'Notification from Transport App';
  }

  /**
   * Reload all templates from the database
   * This can be called when templates are updated via the admin API
   */
  async reloadTemplates(): Promise<void> {
    this.compiledTemplates.clear();
    await this.precompileTemplates();
  }
}
