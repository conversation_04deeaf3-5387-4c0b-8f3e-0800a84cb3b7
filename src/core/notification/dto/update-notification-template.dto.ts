import {
  IsBoolean,
  IsEnum,
  Is<PERSON><PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { NotificationChannel } from '../types/notification.types';

export class UpdateNotificationTemplateDto {
  @IsString()
  @IsOptional()
  @MaxLength(100)
  name?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  type?: string;

  @IsEnum(NotificationChannel)
  @IsOptional()
  channel?: NotificationChannel;

  @IsString()
  @IsOptional()
  content?: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  subject?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsString()
  @IsOptional()
  description?: string;
}
