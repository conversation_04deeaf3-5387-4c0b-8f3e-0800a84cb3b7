import { registerAs } from '@nestjs/config';
import { NotificationConfig } from './notification-config.type';

export default registerAs(
  'notification',
  (): NotificationConfig => ({
    sms: {
      enabled: process.env.SMS_NOTIFICATIONS_ENABLED === 'true',
      twilioAccountSid: process.env.TWILIO_ACCOUNT_SID || '',
      twilioAuthToken: process.env.TWILIO_AUTH_TOKEN || '',
      twilioFromNumber: process.env.TWILIO_FROM_NUMBER || '',
      mockInDevelopment: process.env.NODE_ENV !== 'production',
    },
    email: {
      enabled: process.env.EMAIL_NOTIFICATIONS_ENABLED !== 'false', // Enable by default unless explicitly disabled
      fromEmail: process.env.MAIL_DEFAULT_EMAIL || '<EMAIL>',
      fromName: process.env.MAIL_DEFAULT_NAME || 'Transport App',
    },
    defaultChannel:
      (process.env.DEFAULT_NOTIFICATION_CHANNEL as 'email' | 'sms' | 'both') ||
      'email',
  }),
);
