import { Injectable, NotFoundException } from '@nestjs/common';
import { NotificationTemplateRepository } from './infrastructure/repositories/notification-template.repository';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { UpdateNotificationTemplateDto } from './dto/update-notification-template.dto';
import { NotificationTemplateResponseDto } from './dto/notification-template-response.dto';
import { NotificationService } from './notification.service';
import { plainToClass } from 'class-transformer';

@Injectable()
export class NotificationTemplatesService {
  constructor(
    private readonly notificationTemplateRepository: NotificationTemplateRepository,
    private readonly notificationService: NotificationService,
  ) {}

  /**
   * Create a new notification template
   */
  async create(
    createTemplateDto: CreateNotificationTemplateDto,
  ): Promise<NotificationTemplateResponseDto> {
    const template =
      await this.notificationTemplateRepository.create(createTemplateDto);
    return plainToClass(NotificationTemplateResponseDto, template);
  }

  /**
   * Find all notification templates
   */
  async findAll(): Promise<NotificationTemplateResponseDto[]> {
    const templates = await this.notificationTemplateRepository.findAll();
    return templates.map((template) =>
      plainToClass(NotificationTemplateResponseDto, template),
    );
  }

  /**
   * Find a notification template by id
   */
  async findOne(id: string): Promise<NotificationTemplateResponseDto> {
    const template = await this.notificationTemplateRepository.findById(id);

    if (!template) {
      throw new NotFoundException(
        `Notification template with ID ${id} not found`,
      );
    }

    return plainToClass(NotificationTemplateResponseDto, template);
  }

  /**
   * Update a notification template
   */
  async update(
    id: string,
    updateTemplateDto: UpdateNotificationTemplateDto,
  ): Promise<NotificationTemplateResponseDto> {
    const template = await this.notificationTemplateRepository.findById(id);

    if (!template) {
      throw new NotFoundException(
        `Notification template with ID ${id} not found`,
      );
    }

    const updatedTemplate = await this.notificationTemplateRepository.update(
      id,
      updateTemplateDto,
    );

    if (!updatedTemplate) {
      throw new NotFoundException(`Failed to update template with ID ${id}`);
    }

    // Reload templates in memory after update
    await this.notificationService.reloadTemplates();

    return plainToClass(NotificationTemplateResponseDto, updatedTemplate);
  }

  /**
   * Remove a notification template
   */
  async remove(id: string): Promise<boolean> {
    const template = await this.notificationTemplateRepository.findById(id);

    if (!template) {
      throw new NotFoundException(
        `Notification template with ID ${id} not found`,
      );
    }

    return this.notificationTemplateRepository.delete(id);
  }

  /**
   * Activate a notification template
   */
  async activate(id: string): Promise<NotificationTemplateResponseDto> {
    const template = await this.notificationTemplateRepository.findById(id);

    if (!template) {
      throw new NotFoundException(
        `Notification template with ID ${id} not found`,
      );
    }

    const activatedTemplate =
      await this.notificationTemplateRepository.activate(id);

    if (!activatedTemplate) {
      throw new NotFoundException(`Failed to activate template with ID ${id}`);
    }

    // Reload templates in memory after activation
    await this.notificationService.reloadTemplates();

    return plainToClass(NotificationTemplateResponseDto, activatedTemplate);
  }

  /**
   * Deactivate a notification template
   */
  async deactivate(id: string): Promise<NotificationTemplateResponseDto> {
    const template = await this.notificationTemplateRepository.findById(id);

    if (!template) {
      throw new NotFoundException(
        `Notification template with ID ${id} not found`,
      );
    }

    const deactivatedTemplate =
      await this.notificationTemplateRepository.deactivate(id);

    if (!deactivatedTemplate) {
      throw new NotFoundException(
        `Failed to deactivate template with ID ${id}`,
      );
    }

    // Reload templates in memory after deactivation
    await this.notificationService.reloadTemplates();

    return plainToClass(NotificationTemplateResponseDto, deactivatedTemplate);
  }

  /**
   * Reload all templates in memory
   */
  async reloadTemplates(): Promise<boolean> {
    await this.notificationService.reloadTemplates();
    return true;
  }
}
