import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { NotificationService } from '../notification.service';
import {
  NotificationChannel,
  NotificationType,
} from '../types/notification.types';

@Injectable()
export class OrderNotificationListener {
  private readonly logger = new Logger(OrderNotificationListener.name);

  constructor(private readonly notificationService: NotificationService) {}

  @OnEvent('order.created')
  async handleOrderCreatedEvent(payload: any) {
    this.logger.log(
      `Received order.created event for order ${payload.order.id}`,
    );

    try {
      // Get customer info from order
      const customerEmail = this.getCustomerEmail(payload);
      const customerPhone = this.getCustomerPhone(payload);

      // Send email notification if customer has email
      if (customerEmail) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_CREATED,
          channel: NotificationChannel.EMAIL,
          recipientEmail: customerEmail,
          data: { order: payload.order },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order created email notification to ${customerEmail}`,
        );
      }

      // Send SMS notification if customer has phone
      if (customerPhone) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_CREATED,
          channel: NotificationChannel.SMS,
          recipientPhone: customerPhone,
          data: { order: payload.order },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order created SMS notification to ${customerPhone}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to send order created notification: ${error.message}`,
        error.stack,
      );
    }
  }

  @OnEvent('order.status.changed')
  async handleOrderStatusChangedEvent(payload: any) {
    this.logger.log(
      `Received order.status.changed event for order ${payload.order.id}`,
    );

    try {
      // Get customer info from order
      const customerEmail = this.getCustomerEmail(payload);
      const customerPhone = this.getCustomerPhone(payload);

      // Send email notification if customer has email
      if (customerEmail) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_STATUS_CHANGED,
          channel: NotificationChannel.EMAIL,
          recipientEmail: customerEmail,
          data: {
            order: payload.order,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order status changed email notification to ${customerEmail}`,
        );
      }

      // Send SMS notification if customer has phone
      if (customerPhone) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_STATUS_CHANGED,
          channel: NotificationChannel.SMS,
          recipientPhone: customerPhone,
          data: {
            order: payload.order,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order status changed SMS notification to ${customerPhone}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to send order status changed notification: ${error.message}`,
        error.stack,
      );
    }
  }

  @OnEvent('order.assigned')
  async handleOrderAssignedEvent(payload: any) {
    this.logger.log(
      `Received order.assigned event for order ${payload.order.id}`,
    );

    try {
      // Get customer info from order
      const customerEmail = this.getCustomerEmail(payload);
      const customerPhone = this.getCustomerPhone(payload);

      // Send email notification if customer has email
      if (customerEmail) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_ASSIGNED,
          channel: NotificationChannel.EMAIL,
          recipientEmail: customerEmail,
          data: {
            order: payload.order,
            driver: payload.driver,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order assigned email notification to ${customerEmail}`,
        );
      }

      // Send SMS notification if customer has phone
      if (customerPhone) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_ASSIGNED,
          channel: NotificationChannel.SMS,
          recipientPhone: customerPhone,
          data: {
            order: payload.order,
            driver: payload.driver,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order assigned SMS notification to ${customerPhone}`,
        );
      }

      // Notify the driver if they have an email
      if (payload.driver?.email) {
        await this.notificationService.sendNotification({
          type: NotificationType.DRIVER_ASSIGNED,
          channel: NotificationChannel.EMAIL,
          recipientEmail: payload.driver.email,
          data: {
            order: payload.order,
            driver: payload.driver,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent driver assigned email notification to ${payload.driver.email}`,
        );
      }

      // Send SMS to the driver if they have a phone
      if (payload.driver?.phone) {
        await this.notificationService.sendNotification({
          type: NotificationType.DRIVER_ASSIGNED,
          channel: NotificationChannel.SMS,
          recipientPhone: payload.driver.phone,
          data: {
            order: payload.order,
            driver: payload.driver,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent driver assigned SMS notification to ${payload.driver.phone}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to send order assigned notification: ${error.message}`,
        error.stack,
      );
    }
  }

  @OnEvent('order.started')
  async handleOrderStartedEvent(payload: any) {
    this.logger.log(
      `Received order.started event for order ${payload.order.id}`,
    );

    try {
      // Get customer info from order
      const customerEmail = this.getCustomerEmail(payload);
      const customerPhone = this.getCustomerPhone(payload);

      // Send email notification if customer has email
      if (customerEmail) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_STARTED,
          channel: NotificationChannel.EMAIL,
          recipientEmail: customerEmail,
          data: {
            order: payload.order,
            driver: payload.driver,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order started email notification to ${customerEmail}`,
        );
      }

      // Send SMS notification if customer has phone
      if (customerPhone) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_STARTED,
          channel: NotificationChannel.SMS,
          recipientPhone: customerPhone,
          data: {
            order: payload.order,
            driver: payload.driver,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order started SMS notification to ${customerPhone}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to send order started notification: ${error.message}`,
        error.stack,
      );
    }
  }

  @OnEvent('order.completed')
  async handleOrderCompletedEvent(payload: any) {
    this.logger.log(
      `Received order.completed event for order ${payload.order.id}`,
    );

    try {
      // Get customer info from order
      const customerEmail = this.getCustomerEmail(payload);
      const customerPhone = this.getCustomerPhone(payload);

      // Send email notification if customer has email
      if (customerEmail) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_COMPLETED,
          channel: NotificationChannel.EMAIL,
          recipientEmail: customerEmail,
          data: {
            order: payload.order,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order completed email notification to ${customerEmail}`,
        );
      }

      // Send SMS notification if customer has phone
      if (customerPhone) {
        await this.notificationService.sendNotification({
          type: NotificationType.ORDER_COMPLETED,
          channel: NotificationChannel.SMS,
          recipientPhone: customerPhone,
          data: {
            order: payload.order,
          },
          tenantId: payload.tenantId,
        });
        this.logger.log(
          `Sent order completed SMS notification to ${customerPhone}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to send order completed notification: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Helper method to extract customer email from various payload structures
   */
  private getCustomerEmail(payload: any): string | undefined {
    // Try different paths to find customer email
    return (
      payload.order?.customer?.email ||
      payload.customer?.email ||
      payload.order?.customerEmail ||
      undefined
    );
  }

  /**
   * Helper method to extract customer phone from various payload structures
   */
  private getCustomerPhone(payload: any): string | undefined {
    // Try different paths to find customer phone
    return (
      payload.order?.customer?.phone ||
      payload.customer?.phone ||
      payload.order?.customerPhone ||
      undefined
    );
  }
}
