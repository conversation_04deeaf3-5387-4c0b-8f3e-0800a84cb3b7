import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { NotificationTemplatesService } from './notification-templates.service';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { UpdateNotificationTemplateDto } from './dto/update-notification-template.dto';
import { NotificationTemplateResponseDto } from './dto/notification-template-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserType } from '../../business/user/users/domain/user.types';

@Controller('notification-templates')
@UseGuards(JwtAuthGuard)
@Roles(UserType.Admin)
export class NotificationTemplatesController {
  constructor(
    private readonly notificationTemplatesService: NotificationTemplatesService,
  ) {}

  @Post()
  async create(
    @Body() createTemplateDto: CreateNotificationTemplateDto,
  ): Promise<NotificationTemplateResponseDto> {
    return this.notificationTemplatesService.create(createTemplateDto);
  }

  @Get()
  async findAll(): Promise<NotificationTemplateResponseDto[]> {
    return this.notificationTemplatesService.findAll();
  }

  @Get(':id')
  async findOne(
    @Param('id') id: string,
  ): Promise<NotificationTemplateResponseDto> {
    return this.notificationTemplatesService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateNotificationTemplateDto,
  ): Promise<NotificationTemplateResponseDto> {
    return this.notificationTemplatesService.update(id, updateTemplateDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<boolean> {
    return this.notificationTemplatesService.remove(id);
  }

  @Post(':id/activate')
  async activate(
    @Param('id') id: string,
  ): Promise<NotificationTemplateResponseDto> {
    return this.notificationTemplatesService.activate(id);
  }

  @Post(':id/deactivate')
  async deactivate(
    @Param('id') id: string,
  ): Promise<NotificationTemplateResponseDto> {
    return this.notificationTemplatesService.deactivate(id);
  }

  @Post('reload')
  async reloadTemplates(): Promise<boolean> {
    return this.notificationTemplatesService.reloadTemplates();
  }
}
