import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { NotificationLogsService } from './notification-logs.service';
import { NotificationLogResponseDto } from './dto/notification-log-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserType } from '../../business/user/users/domain/user.types';

@Controller('notification-logs')
@UseGuards(JwtAuthGuard)
@Roles(UserType.Admin)
export class NotificationLogsController {
  constructor(
    private readonly notificationLogsService: NotificationLogsService,
  ) {}

  @Get()
  async findAll(
    @Query('limit') limit = 100,
    @Query('offset') offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    return this.notificationLogsService.findAll(limit, offset);
  }

  @Get('tenant/:tenantId')
  async findByTenant(
    @Param('tenantId') tenantId: string,
    @Query('limit') limit = 100,
    @Query('offset') offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    return this.notificationLogsService.findByTenant(tenantId, limit, offset);
  }

  @Get('type/:type')
  async findByType(
    @Param('type') type: string,
    @Query('channel') channel?: string,
    @Query('limit') limit = 100,
    @Query('offset') offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    return this.notificationLogsService.findByType(
      type,
      channel,
      limit,
      offset,
    );
  }

  @Get('recipient')
  async findByRecipient(
    @Query('email') email?: string,
    @Query('phone') phone?: string,
    @Query('limit') limit = 100,
    @Query('offset') offset = 0,
  ): Promise<{ items: NotificationLogResponseDto[]; count: number }> {
    return this.notificationLogsService.findByRecipient(
      email,
      phone,
      limit,
      offset,
    );
  }

  @Get('stats/type/:type')
  async getStatsByType(
    @Param('type') type: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<{
    total: number;
    successful: number;
    failed: number;
    successRate: number;
  }> {
    return this.notificationLogsService.getStatsByType(
      type,
      startDate,
      endDate,
    );
  }
}
