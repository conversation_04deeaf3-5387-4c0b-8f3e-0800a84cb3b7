import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { NotificationService } from './notification.service';
import { TwilioService } from './providers/twilio.service';
import { MailModule } from '../mail/mail.module';
import { MailerModule } from '../mailer/mailer.module';
import notificationConfig from './config/notification.config';
import { OrderNotificationListener } from './listeners/order-notification.listener';
import { NotificationRelationalPersistenceModule } from './infrastructure/relational-persistence.module';
import { NotificationTemplatesService } from './notification-templates.service';
import { NotificationTemplatesController } from './notification-templates.controller';
import { NotificationLogsService } from './notification-logs.service';
import { NotificationLogsController } from './notification-logs.controller';

@Module({
  imports: [
    ConfigModule.forFeature(notificationConfig),
    MailModule,
    MailerModule,
    NotificationRelationalPersistenceModule,
  ],
  controllers: [NotificationTemplatesController, NotificationLogsController],
  providers: [
    NotificationService,
    TwilioService,
    OrderNotificationListener,
    NotificationTemplatesService,
    NotificationLogsService,
  ],
  exports: [NotificationService, TwilioService],
})
export class NotificationModule {}
