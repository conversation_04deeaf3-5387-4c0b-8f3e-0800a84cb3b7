import { Injectable } from '@nestjs/common';

@Injectable()
export class FileStorageService {
  async uploadBase64Image(
    base64Image: string,
    fileName: string,
    folder: string,
  ): Promise<string> {
    // Mock implementation for testing
    return `https://storage.example.com/${folder}/${fileName}`;
  }

  async uploadFile(
    file: Express.Multer.File,
    fileName: string,
    folder: string,
  ): Promise<string> {
    // Mock implementation for testing
    return `https://storage.example.com/${folder}/${fileName}`;
  }

  async deleteFile(fileUrl: string): Promise<boolean> {
    // Mock implementation for testing
    return true;
  }
}
