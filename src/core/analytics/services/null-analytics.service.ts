import { Injectable, Logger } from '@nestjs/common';
import { AnalyticsProvider, IAnalyticsService } from '../analytics.interface';

/**
 * Null implementation of the analytics service that does nothing
 * Used when analytics is disabled or when no provider is configured
 */
@Injectable()
export class NullAnalyticsService implements IAnalyticsService {
  private readonly logger = new Logger(NullAnalyticsService.name);

  constructor() {
    this.logger.log('Using NullAnalyticsService - no events will be tracked');
  }

  async trackEvent(
    distinctId: string,
    event: string,
    properties?: Record<string, any>,
  ): Promise<void> {
    // Do nothing
    return Promise.resolve();
  }

  async identifyUser(
    distinctId: string,
    properties?: Record<string, any>,
  ): Promise<void> {
    // Do nothing
    return Promise.resolve();
  }

  isEnabled(): boolean {
    return false;
  }

  getProviderName(): string {
    return AnalyticsProvider.NONE;
  }
}
