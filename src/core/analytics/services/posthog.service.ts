import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PostHog } from 'posthog-node';
import { AnalyticsProvider, IAnalyticsService } from '../analytics.interface';
import { AllConfigType } from '@config/config.type';

@Injectable()
export class PosthogService implements IAnalyticsService {
  private client: PostHog | null = null;
  private readonly logger = new Logger(PosthogService.name);
  private enabled = false;

  constructor(private configService: ConfigService<AllConfigType>) {
    const posthogConfig = this.configService.get('analytics.posthog', {
      infer: true,
    });

    if (!posthogConfig) {
      this.logger.warn('PostHog configuration not found');
      return;
    }

    const { apiKey, host, enabled } = posthogConfig;
    this.enabled = enabled || false;

    if (this.enabled && apiKey) {
      try {
        this.client = new PostHog(api<PERSON><PERSON>, {
          host: host || 'https://app.posthog.com',
        });
        this.logger.log('PostHog analytics initialized');
      } catch (error) {
        this.logger.error('Failed to initialize PostHog client', error);
        this.enabled = false;
      }
    } else if (!apiKey) {
      this.logger.warn('PostHog API key not provided, analytics disabled');
      this.enabled = false;
    } else if (!this.enabled) {
      this.logger.log('PostHog analytics disabled by configuration');
    }
  }

  async trackEvent(
    distinctId: string,
    event: string,
    properties?: Record<string, any>,
  ): Promise<void> {
    if (!this.enabled || !this.client) {
      return;
    }

    try {
      await this.client.capture({
        distinctId,
        event,
        properties,
      });
      this.logger.debug(`Tracked event: ${event} for user: ${distinctId}`);
    } catch (error) {
      this.logger.error(`Failed to track event: ${event}`, error);
    }
  }

  async identifyUser(
    distinctId: string,
    properties?: Record<string, any>,
  ): Promise<void> {
    if (!this.enabled || !this.client) {
      return;
    }

    try {
      await this.client.identify({
        distinctId,
        properties,
      });
      this.logger.debug(`Identified user: ${distinctId}`);
    } catch (error) {
      this.logger.error(`Failed to identify user: ${distinctId}`, error);
    }
  }

  isEnabled(): boolean {
    return this.enabled;
  }

  getProviderName(): string {
    return AnalyticsProvider.POSTHOG;
  }
}
