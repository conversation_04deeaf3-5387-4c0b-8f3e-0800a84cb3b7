import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ANALYTICS_SERVICE } from './analytics.interface';
import analyticsConfig from './config/analytics.config';
import { AnalyticsInterceptorProvider } from './providers/analytics-interceptor.provider';
import { AnalyticsFactoryService } from './services/analytics-factory.service';

@Global()
@Module({
  imports: [ConfigModule.forFeature(analyticsConfig)],
  providers: [
    AnalyticsFactoryService,
    {
      provide: ANALYTICS_SERVICE,
      useFactory: (factoryService: AnalyticsFactoryService) => {
        return factoryService.createAnalyticsService();
      },
      inject: [AnalyticsFactoryService],
    },
    AnalyticsInterceptorProvider,
  ],
  exports: [ANALYTICS_SERVICE],
})
export class AnalyticsModule {}
