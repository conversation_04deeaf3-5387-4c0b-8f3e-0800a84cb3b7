import { AnalyticsProvider } from '../analytics.interface';

export type PosthogConfig = {
  apiKey: string;
  host: string;
  enabled: boolean;
};

export type SegmentConfig = {
  writeKey: string;
  enabled: boolean;
};

export type GoogleAnalyticsConfig = {
  measurementId: string;
  enabled: boolean;
};

export type MixpanelConfig = {
  token: string;
  enabled: boolean;
};

export type AmplitudeConfig = {
  apiKey: string;
  enabled: boolean;
};

export type AnalyticsConfig = {
  provider: AnalyticsProvider;
  enabled: boolean;
  posthog?: PosthogConfig;
  segment?: SegmentConfig;
  googleAnalytics?: GoogleAnalyticsConfig;
  mixpanel?: MixpanelConfig;
  amplitude?: AmplitudeConfig;
};
