import { InjectionToken } from '@nestjs/common';

/**
 * Represents an analytics event to be tracked
 */
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
}

/**
 * Core analytics service interface that all provider implementations must implement
 */
export interface IAnalyticsService {
  /**
   * Track an event for a specific user
   * @param distinctId Unique identifier for the user
   * @param event Name of the event to track
   * @param properties Additional properties to include with the event
   */
  trackEvent(
    distinctId: string,
    event: string,
    properties?: Record<string, any>,
  ): Promise<void>;

  /**
   * Identify a user with specific properties
   * @param distinctId Unique identifier for the user
   * @param properties User properties to associate with this identity
   */
  identifyUser(
    distinctId: string,
    properties?: Record<string, any>,
  ): Promise<void>;

  /**
   * Check if analytics tracking is enabled
   */
  isEnabled(): boolean;

  /**
   * Get the name of the analytics provider
   */
  getProviderName(): string;
}

/**
 * Token for injecting the analytics service
 */
export const ANALYTICS_SERVICE = Symbol('ANALYTICS_SERVICE') as InjectionToken;

/**
 * Available analytics providers
 */
export enum AnalyticsProvider {
  POSTHOG = 'posthog',
  SEGMENT = 'segment',
  GOOGLE_ANALYTICS = 'google_analytics',
  MIXPANEL = 'mixpanel',
  AMPLITUDE = 'amplitude',
  CUSTOM = 'custom',
  NONE = 'none',
}
