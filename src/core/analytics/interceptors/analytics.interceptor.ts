import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Inject,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, tap } from 'rxjs';
import { ANALYTICS_SERVICE, IAnalyticsService } from '../analytics.interface';
import { Request } from 'express';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';

interface RequestWithUser extends Request {
  user?: UserEntity;
}

@Injectable()
export class AnalyticsInterceptor implements NestInterceptor {
  constructor(
    @Inject(ANALYTICS_SERVICE)
    private readonly analyticsService: IAnalyticsService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (!this.analyticsService.isEnabled()) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest<RequestWithUser>();
    const { method, path, user } = request;
    const distinctId = user?.id || 'anonymous';
    const startTime = Date.now();

    return next.handle().pipe(
      tap({
        next: () => {
          // Track successful requests
          const duration = Date.now() - startTime;
          const eventName = this.getEventNameFromPath(method, path);

          if (eventName) {
            void this.analyticsService.trackEvent(distinctId, eventName, {
              method,
              path,
              duration,
              status: 'success',
              timestamp: new Date().toISOString(),
            });
          }
        },
        error: (error) => {
          // Track failed requests
          const duration = Date.now() - startTime;
          const eventName = this.getEventNameFromPath(method, path, true);

          if (eventName) {
            void this.analyticsService.trackEvent(distinctId, eventName, {
              method,
              path,
              duration,
              status: 'error',
              errorMessage: error.message,
              errorCode: error.code || error.status,
              timestamp: new Date().toISOString(),
            });
          }
        },
      }),
    );
  }

  private getEventNameFromPath(
    method: string,
    path: string,
    isError = false,
  ): string | null {
    // Map paths to event names based on the user's requirements
    if (path.includes('/auth/login') && method === 'POST') {
      return isError ? null : 'user_signed_in';
    }

    if (path.includes('/auth/logout') && method === 'POST') {
      return isError ? null : 'user_signed_out';
    }

    if (path.includes('/address') && method === 'POST') {
      return isError ? 'address_creation_failed' : 'address_created';
    }

    if (path.includes('/orders') && method === 'POST') {
      return isError ? 'order_creation_failed' : 'order_created';
    }

    if (path.includes('/orders') && method === 'PUT') {
      return isError ? 'order_update_failed' : 'order_updated';
    }

    if (path.includes('/orders') && method === 'GET') {
      return isError ? 'order_fetch_failed' : 'order_fetch';
    }

    if (path.includes('/prices') && method === 'GET') {
      return isError ? 'prices_fetch_failed' : 'prices_fetched';
    }

    return null;
  }
}
