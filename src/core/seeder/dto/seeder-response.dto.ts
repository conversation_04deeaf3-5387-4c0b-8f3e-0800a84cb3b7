import { ApiProperty } from '@nestjs/swagger';

export class SeederResponseDto {
  @ApiProperty({
    description: 'Number of zones created',
    example: 50,
  })
  zonesCreated: number;

  @ApiProperty({
    description: 'Number of zone tables created',
    example: 10,
  })
  zoneTablesCreated: number;

  @ApiProperty({
    description: 'Number of price modifiers created',
    example: 20,
  })
  priceModifiersCreated: number;

  @ApiProperty({
    description: 'Number of price modifier groups created',
    example: 5,
  })
  priceModifierGroupsCreated: number;

  @ApiProperty({
    description: 'Number of price sets created',
    example: 8,
  })
  priceSetsCreated: number;

  @ApiProperty({
    description: 'Time taken to seed data (in seconds)',
    example: 5.2,
  })
  timeTakenSeconds: number;
}
