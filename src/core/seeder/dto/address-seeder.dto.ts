import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class AddressSeederDto {
  @ApiProperty({
    description: 'Tenant ID to seed addresses for',
    example: '66fedab8-7820-4cca-b8ca-cf59ade1aada',
  })
  @IsNotEmpty()
  @IsUUID()
  tenantId: string;

  @ApiProperty({
    description: 'Customer ID to associate addresses with',
    example: '66fedab8-7820-4cca-b8ca-cf59ade1bbbb',
  })
  @IsNotEmpty()
  @IsUUID()
  customerId: string;
}
