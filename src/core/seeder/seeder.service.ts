import { Injectable, Logger } from '@nestjs/common';
import { ZonesService } from '../../business/zone/zones/zones.service';
import { ZoneTableService } from '../../business/zone/zone-tables/zone-table.service';
import { PriceModifiersService } from '../../business/pricing/price-modifiers/price-modifiers.service';
import { PriceSetsService } from '../../business/pricing/price-sets/price-sets.service';
import { AddressService } from '../../business/address/addresses/address.service';

import { ZoneDomain } from '../../business/zone/zones/domain/zone';
import {
  ZoneTableDomain,
  ZoneTableValueDomain,
} from '../../business/zone/zone-tables/domain/zone-table';
import { PriceModifierDomain } from '../../business/pricing/price-modifiers/domain/price-modifier';
import { PriceModifierGroupDomain } from '../../business/pricing/price-modifiers/domain/price-modifier-group';
import { PriceSetDomain } from '../../business/pricing/price-sets/domain/price-set';
import { AddressDomain } from '../../business/address/addresses/domain/address';
import { SeederResponseDto } from './dto/seeder-response.dto';
import { AddressSeederResponseDto } from './dto/address-seeder-response.dto';
import {
  ECalculationType,
  EModifierGroupBehavior,
  ECalculationField,
  ERangeFromOperator,
  ERangeToOperator,
} from '../../business/pricing/price-modifiers/domain/price-modifier.types';
import {
  AvailabilityType,
  OffsetType,
  PriceSetPaymentOption,
  ConfigurationType,
} from '../../business/pricing/price-sets/domain/price-set.types';
import {
  CreateScheduleDto,
  OffsetData,
  Schedule,
} from '../../business/pricing/price-sets/dto/create-schedule.dto';
import { AssignModifiersDto } from '../../business/pricing/price-sets/dto/assign-modifiers.dto';
import { CreateZoneTableDto } from '../../business/zone/zone-tables/dto/create-zone-table.dto';
import { ModifierMemberDto } from '../../business/pricing/price-modifiers/dto/create-price-modifier-group.dto';

@Injectable()
export class SeederService {
  private readonly logger = new Logger(SeederService.name);

  constructor(
    private readonly zonesService: ZonesService,
    private readonly zoneTableService: ZoneTableService,
    private readonly priceModifiersService: PriceModifiersService,
    private readonly priceSetsService: PriceSetsService,
    private readonly addressService: AddressService,
  ) {}

  /**
   * Seed data for a tenant
   * @param tenantId The tenant ID to seed data for
   * @returns Statistics about the seeded data
   */
  async seedData(tenantId: string): Promise<SeederResponseDto> {
    this.logger.log(`Starting data seeding for tenant: ${tenantId}`);
    const startTime = Date.now();

    // Validate tenant exists
    this.validateTenant(tenantId);

    // Create zones
    const zones = await this.createZones(tenantId);
    this.logger.log(`Created ${zones.length} zones`);

    // Create zone tables
    const zoneTables = await this.createZoneTables(tenantId, zones);
    this.logger.log(`Created ${zoneTables.length} zone tables`);

    // Create price modifiers
    const { modifiers, groups } = await this.createPriceModifiers(tenantId);
    this.logger.log(
      `Created ${modifiers.length} price modifiers and ${groups.length} groups`,
    );

    // Create price sets
    const priceSets = await this.createPriceSets(tenantId);
    this.logger.log(`Created ${priceSets.length} price sets`);

    // Assign zone tables to price sets
    await this.assignZoneTablesToSets(tenantId, priceSets, zoneTables);
    this.logger.log('Assigned zone tables to price sets');

    // Assign modifiers to price sets
    await this.assignModifiersToSets(priceSets, modifiers, groups);
    this.logger.log('Assigned modifiers to price sets');

    const endTime = Date.now();
    const timeTakenSeconds = (endTime - startTime) / 1000;

    return {
      zonesCreated: zones.length,
      zoneTablesCreated: zoneTables.length,
      priceModifiersCreated: modifiers.length,
      priceModifierGroupsCreated: groups.length,
      priceSetsCreated: priceSets.length,
      timeTakenSeconds,
    };
  }

  /**
   * Seed addresses for a customer
   * @param tenantId The tenant ID
   * @param customerId The customer ID to associate addresses with
   * @returns Statistics about the seeded addresses
   */
  async seedAddresses(
    tenantId: string,
    customerId: string,
  ): Promise<AddressSeederResponseDto> {
    this.logger.log(
      `Starting address seeding for tenant: ${tenantId}, customer: ${customerId}`,
    );
    const startTime = Date.now();

    // Validate tenant exists
    this.validateTenant(tenantId);

    // Get all zones to use their postal codes
    const zones = await this.zonesService.getAllZonesByTenant(tenantId);

    if (!zones || zones.length === 0) {
      this.logger.warn(
        'No zones found. Please run the main seeder first to create zones.',
      );
      return {
        addressesCreated: 0,
        timeTakenSeconds: (Date.now() - startTime) / 1000,
      };
    }

    this.logger.log(
      `Found ${zones.length} existing zones to use for addresses`,
    );

    // Extract all postal codes from zones
    const allPostalCodes = zones.flatMap((zone) => zone.postalCodes || []);
    this.logger.log(
      `Extracted ${allPostalCodes.length} postal codes from zones`,
    );

    // Create addresses
    const addresses = await this.createAddresses(
      tenantId,
      customerId,
      allPostalCodes,
    );
    this.logger.log(`Created ${addresses.length} addresses`);

    const endTime = Date.now();
    const timeTakenSeconds = (endTime - startTime) / 1000;

    return {
      addressesCreated: addresses.length,
      timeTakenSeconds,
    };
  }

  /**
   * Create addresses for a customer
   * @param tenantId The tenant ID
   * @param customerId The customer ID
   * @param postalCodes Array of postal codes to use
   * @returns Array of created addresses
   */
  private async createAddresses(
    tenantId: string,
    customerId: string,
    postalCodes: string[],
  ): Promise<AddressDomain[]> {
    const addresses: AddressDomain[] = [];
    const targetCount = 120; // Create 120 addresses

    // Company name prefixes and suffixes for variety
    const companyPrefixes = [
      'Global',
      'Premier',
      'Elite',
      'Advanced',
      'Superior',
      'Innovative',
      'Strategic',
      'Dynamic',
      'Reliable',
      'Precision',
    ];
    const companySuffixes = [
      'Logistics',
      'Transport',
      'Shipping',
      'Delivery',
      'Freight',
      'Supply Chain',
      'Distribution',
      'Carriers',
      'Express',
      'Cargo',
    ];

    // Street name components
    const streetTypes = [
      'St',
      'Ave',
      'Blvd',
      'Dr',
      'Ln',
      'Rd',
      'Way',
      'Pl',
      'Ct',
      'Terrace',
    ];
    const streetNames = [
      'Main',
      'Oak',
      'Pine',
      'Maple',
      'Cedar',
      'Elm',
      'Washington',
      'Park',
      'Lake',
      'River',
      'Highland',
      'Sunset',
      'Willow',
      'Meadow',
      'Forest',
      'Mountain',
      'Valley',
      'Spring',
    ];

    // Cities based on zone types
    const cities = {
      '1': ['New York', 'Chicago', 'Boston', 'Philadelphia', 'San Francisco'], // Downtown (10xxx)
      '2': ['Brooklyn', 'Queens', 'Bronx', 'Staten Island', 'Jersey City'], // Urban (20xxx)
      '3': ['White Plains', 'Yonkers', 'New Rochelle', 'Stamford', 'Greenwich'], // Suburban (30xxx)
      '4': ['Newark', 'Elizabeth', 'Bayonne', 'Secaucus', 'Kearny'], // Industrial (40xxx)
      '5': ['Paramus', 'Hackensack', 'Fort Lee', 'Englewood', 'Teaneck'], // Commercial (50xxx)
    };

    // Provinces/States
    const provinces = ['NY', 'NJ', 'CT', 'PA', 'MA'];

    // Countries
    const countries = ['United States'];

    // Create addresses
    for (let i = 0; i < targetCount; i++) {
      try {
        // Select a postal code
        const postalCode = postalCodes[i % postalCodes.length];

        // Determine zone type from postal code prefix
        const zoneType = postalCode.charAt(0);

        // Generate a random address
        const streetNumber = Math.floor(Math.random() * 9000) + 1000;
        const streetName =
          streetNames[Math.floor(Math.random() * streetNames.length)];
        const streetType =
          streetTypes[Math.floor(Math.random() * streetTypes.length)];
        const addressLine1 = `${streetNumber} ${streetName} ${streetType}`;

        // Maybe add a suite/apt number (30% chance)
        const addressLine2 =
          Math.random() < 0.3
            ? `Suite ${Math.floor(Math.random() * 500) + 100}`
            : null;

        // Select city based on zone type
        const cityOptions = cities[zoneType] || cities['1'];
        const city =
          cityOptions[Math.floor(Math.random() * cityOptions.length)];

        // Select province
        const province =
          provinces[Math.floor(Math.random() * provinces.length)];

        // Select country
        const country = countries[Math.floor(Math.random() * countries.length)];

        // Generate company name
        const companyPrefix =
          companyPrefixes[Math.floor(Math.random() * companyPrefixes.length)];
        const companySuffix =
          companySuffixes[Math.floor(Math.random() * companySuffixes.length)];
        const companyName = `${companyPrefix} ${companySuffix}`;

        // Generate contact name
        const contactName = `Location ${i + 1}`;

        // Generate email
        const email = `location${i + 1}@example.com`;

        // Generate phone
        const countryCode = '+1';
        const phoneNumber = `${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`;
        const phoneExtension =
          Math.random() < 0.5 ? Math.floor(Math.random() * 900) + 100 : 0;

        // Generate notes
        const notes = Math.random() < 0.3 ? `Test address ${i + 1}` : null;

        // Generate latitude and longitude based on postal code
        // This creates a realistic cluster of coordinates for each postal code area
        // Base coordinates for each zone type (roughly NYC area)
        const baseCoords = {
          '1': { lat: 40.7128, lng: -74.006 }, // Manhattan
          '2': { lat: 40.6782, lng: -73.9442 }, // Brooklyn
          '3': { lat: 40.9115, lng: -73.7826 }, // Westchester
          '4': { lat: 40.7357, lng: -74.1724 }, // Newark
          '5': { lat: 40.9579, lng: -74.0748 }, // Bergen County
        };

        // Get base coordinates for this zone type
        const baseCoord = baseCoords[zoneType] || baseCoords['1'];

        // Add small random offset (within about 5 miles)
        const latOffset = (Math.random() - 0.5) * 0.05;
        const lngOffset = (Math.random() - 0.5) * 0.05;
        const latitude = baseCoord.lat + latOffset;
        const longitude = baseCoord.lng + lngOffset;

        // Set favorite and default flags
        // Make first address default for pickup and delivery
        const isDefaultForPickup = i === 0;
        const isDefaultForDelivery = i === 0;

        // Make some addresses favorites (about 10%)
        const isFavoriteForPickup = Math.random() < 0.1;
        const isFavoriteForDelivery = Math.random() < 0.1;

        // Create address domain
        const addressDomain = new AddressDomain();
        addressDomain.tenantId = tenantId;
        addressDomain.customerId = customerId;
        addressDomain.name = contactName;
        addressDomain.companyName = companyName;
        addressDomain.email = email;
        addressDomain.countryCode = countryCode;
        addressDomain.phoneNumber = phoneNumber;
        addressDomain.phoneExtension = phoneExtension;
        addressDomain.addressLine1 = addressLine1;
        addressDomain.addressLine2 = addressLine2 || '';
        addressDomain.city = city;
        addressDomain.province = province;
        addressDomain.postalCode = postalCode;
        addressDomain.country = country;
        addressDomain.notes = notes || '';
        addressDomain.latitude = latitude;
        addressDomain.longitude = longitude;
        addressDomain.isFavoriteForPickup = isFavoriteForPickup;
        addressDomain.isFavoriteForDelivery = isFavoriteForDelivery;
        addressDomain.isDefaultForPickup = isDefaultForPickup;
        addressDomain.isDefaultForDelivery = isDefaultForDelivery;

        // Create the address
        const createdAddress = await this.addressService.create(addressDomain);
        addresses.push(createdAddress);

        this.logger.debug(
          `Created address: ${createdAddress.addressLine1}, ${createdAddress.city}`,
        );
      } catch (error) {
        this.logger.error(`Failed to create address: ${error.message}`);
        // Continue with other addresses even if one fails
      }
    }

    return addresses;
  }

  /**
   * Validate that the tenant exists
   * Note: This is a simplified implementation for seeding purposes
   * @param tenantId The tenant ID to validate
   */
  private validateTenant(tenantId: string): void {
    // For seeding purposes, we'll just log the tenant ID
    // In a real implementation, we would validate the tenant exists
    this.logger.log(`Seeding data for tenant: ${tenantId}`);
  }

  /**
   * Create zones for the tenant
   */
  private async createZones(tenantId: string): Promise<ZoneDomain[]> {
    const zones: ZoneDomain[] = [];

    // Define zone data with realistic postal codes
    const zoneData = [
      // Downtown zones
      {
        name: 'Downtown Core',
        postalCodes: ['10001', '10002', '10003', '10004', '10005'],
        notes: 'Central business district with high-rise buildings',
      },
      {
        name: 'Financial District',
        postalCodes: ['10006', '10007', '10008', '10009', '10010'],
        notes: 'Banking and financial services area',
      },
      {
        name: 'Tech Hub',
        postalCodes: ['10011', '10012', '10013', '10014', '10015'],
        notes: 'Technology companies and startups',
      },
      {
        name: 'Arts District',
        postalCodes: ['10016', '10017', '10018', '10019', '10020'],
        notes: 'Galleries, theaters, and creative spaces',
      },
      {
        name: 'Government Center',
        postalCodes: ['10021', '10022', '10023', '10024', '10025'],
        notes: 'City hall, courthouses, and government offices',
      },

      // Urban residential zones
      {
        name: 'North End',
        postalCodes: ['20001', '20002', '20003', '20004', '20005'],
        notes: 'Dense urban residential area with apartments',
      },
      {
        name: 'West Side',
        postalCodes: ['20006', '20007', '20008', '20009', '20010'],
        notes: 'Mixed residential and commercial area',
      },
      {
        name: 'East Side',
        postalCodes: ['20011', '20012', '20013', '20014', '20015'],
        notes: 'Residential area with waterfront properties',
      },
      {
        name: 'South End',
        postalCodes: ['20016', '20017', '20018', '20019', '20020'],
        notes: 'Trendy neighborhood with restaurants and shops',
      },
      {
        name: 'University District',
        postalCodes: ['20021', '20022', '20023', '20024', '20025'],
        notes: 'Educational institutions and student housing',
      },

      // Suburban zones
      {
        name: 'North Suburbs',
        postalCodes: ['30001', '30002', '30003', '30004', '30005'],
        notes: 'Residential suburbs with single-family homes',
      },
      {
        name: 'West Suburbs',
        postalCodes: ['30006', '30007', '30008', '30009', '30010'],
        notes: 'Affluent suburbs with larger properties',
      },
      {
        name: 'East Suburbs',
        postalCodes: ['30011', '30012', '30013', '30014', '30015'],
        notes: 'Suburban area with good schools',
      },
      {
        name: 'South Suburbs',
        postalCodes: ['30016', '30017', '30018', '30019', '30020'],
        notes: 'Growing suburban area with new developments',
      },
      {
        name: 'Outer Ring',
        postalCodes: ['30021', '30022', '30023', '30024', '30025'],
        notes: 'Outer suburban area with larger lots',
      },

      // Industrial zones
      {
        name: 'Port District',
        postalCodes: ['40001', '40002', '40003', '40004', '40005'],
        notes: 'Shipping and logistics hub',
      },
      {
        name: 'Manufacturing Zone',
        postalCodes: ['40006', '40007', '40008', '40009', '40010'],
        notes: 'Heavy industry and manufacturing',
      },
      {
        name: 'Warehouse District',
        postalCodes: ['40011', '40012', '40013', '40014', '40015'],
        notes: 'Warehousing and distribution centers',
      },
      {
        name: 'Industrial Park',
        postalCodes: ['40016', '40017', '40018', '40019', '40020'],
        notes: 'Modern industrial park with mixed businesses',
      },
      {
        name: 'Airport Zone',
        postalCodes: ['40021', '40022', '40023', '40024', '40025'],
        notes: 'Airport and related logistics businesses',
      },

      // Commercial zones
      {
        name: 'Shopping District',
        postalCodes: ['50001', '50002', '50003', '50004', '50005'],
        notes: 'Retail stores and shopping centers',
      },
      {
        name: 'Restaurant Row',
        postalCodes: ['50006', '50007', '50008', '50009', '50010'],
        notes: 'Concentration of restaurants and cafes',
      },
      {
        name: 'Entertainment District',
        postalCodes: ['50011', '50012', '50013', '50014', '50015'],
        notes: 'Theaters, clubs, and entertainment venues',
      },
      {
        name: 'Office Park',
        postalCodes: ['50016', '50017', '50018', '50019', '50020'],
        notes: 'Suburban office buildings and corporate campuses',
      },
      {
        name: 'Medical Center',
        postalCodes: ['50021', '50022', '50023', '50024', '50025'],
        notes: 'Hospitals, clinics, and medical offices',
      },
    ];

    // Create zones in batches to avoid overwhelming the database
    for (const data of zoneData) {
      try {
        // Create a zone domain object
        const zoneDomain = new ZoneDomain();
        zoneDomain.tenantId = tenantId;
        zoneDomain.name = data.name;
        zoneDomain.postalCodes = data.postalCodes;
        zoneDomain.notes = data.notes;

        const createdZone = await this.zonesService.create(zoneDomain);
        zones.push(createdZone);

        this.logger.debug(`Created zone: ${createdZone.name}`);
      } catch (error) {
        this.logger.error(
          `Failed to create zone ${data.name}: ${error.message}`,
        );
        // Continue with other zones even if one fails
      }
    }

    return zones;
  }

  /**
   * Create zone tables for the tenant
   */
  private async createZoneTables(
    tenantId: string,
    zones: ZoneDomain[],
  ): Promise<ZoneTableDomain[]> {
    const zoneTables: ZoneTableDomain[] = [];

    if (zones.length === 0) {
      this.logger.warn('No zones available to create zone tables');
      return zoneTables;
    }

    // Group zones by type for easier reference
    const downtownZones = zones.filter(
      (z) =>
        z.name.includes('Downtown') ||
        z.name.includes('Financial') ||
        z.name.includes('Tech') ||
        z.name.includes('Arts') ||
        z.name.includes('Government'),
    );
    const suburbanZones = zones.filter(
      (z) => z.name.includes('Suburbs') || z.name.includes('Outer Ring'),
    );
    const industrialZones = zones.filter(
      (z) =>
        z.name.includes('Port') ||
        z.name.includes('Manufacturing') ||
        z.name.includes('Warehouse') ||
        z.name.includes('Industrial') ||
        z.name.includes('Airport'),
    );

    // Define zone table configurations
    const zoneTableConfigs = [
      {
        name: 'Standard Delivery Rates',
        description: 'Base rates for standard delivery service',
      },
      {
        name: 'Express Delivery Rates',
        description: 'Premium rates for expedited delivery service',
      },
      {
        name: 'Economy Delivery Rates',
        description: 'Cost-effective rates for non-urgent deliveries',
      },
    ];

    // Create zone tables
    for (const config of zoneTableConfigs) {
      try {
        // Create zone table domain
        const zoneTableDomain = new ZoneTableDomain();
        zoneTableDomain.tenantId = tenantId;
        zoneTableDomain.name = config.name;

        // Create zone table values
        const zoneTableValues: {
          originZoneId: string;
          destinationZoneId: string;
          value: number;
        }[] = [];

        // Generate values for all zone combinations
        for (const originZone of zones) {
          for (const destinationZone of zones) {
            // Skip if origin and destination are the same for some zone types
            if (
              originZone.id === destinationZone.id &&
              (originZone.name.includes('Downtown') ||
                originZone.name.includes('Industrial'))
            ) {
              continue;
            }

            // Calculate base price based on zone types
            let basePrice = 10; // Default base price

            // Adjust price based on origin zone type
            if (downtownZones.some((z) => z.id === originZone.id)) {
              basePrice += 5;
            } else if (suburbanZones.some((z) => z.id === originZone.id)) {
              basePrice += 15;
            } else if (industrialZones.some((z) => z.id === originZone.id)) {
              basePrice += 20;
            }

            // Adjust price based on destination zone type
            if (downtownZones.some((z) => z.id === destinationZone.id)) {
              basePrice += 5;
            } else if (suburbanZones.some((z) => z.id === destinationZone.id)) {
              basePrice += 15;
            } else if (
              industrialZones.some((z) => z.id === destinationZone.id)
            ) {
              basePrice += 20;
            }

            // Apply multiplier based on service type
            let multiplier = 1.0;
            if (config.name.includes('Express')) {
              multiplier = 1.5;
            } else if (config.name.includes('Economy')) {
              multiplier = 0.8;
            }

            // Calculate final price
            const finalPrice = Math.round(basePrice * multiplier * 100) / 100;

            // Create zone table value
            zoneTableValues.push({
              originZoneId: originZone.id,
              destinationZoneId: destinationZone.id,
              value: finalPrice,
            });
          }
        }

        // Map zone table values to domain objects
        const zoneTableValueDomains = zoneTableValues.map((value) => {
          const valueDomain = new ZoneTableValueDomain();
          valueDomain.originZoneId = value.originZoneId;
          valueDomain.destinationZoneId = value.destinationZoneId;
          valueDomain.value = value.value;
          return valueDomain;
        });

        // Assign values to domain
        zoneTableDomain.zoneTableValues = zoneTableValueDomains;

        // Create the zone table
        const createdZoneTable =
          await this.zoneTableService.create(zoneTableDomain);

        zoneTables.push(createdZoneTable);
        this.logger.debug(
          `Created zone table: ${createdZoneTable.name} with ${zoneTableValues.length} values`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to create zone table ${config.name}: ${error.message}`,
        );
        // Continue with other zone tables even if one fails
      }
    }

    return zoneTables;
  }

  /**
   * Create price modifiers for the tenant
   */
  private async createPriceModifiers(tenantId: string): Promise<{
    modifiers: PriceModifierDomain[];
    groups: any[];
  }> {
    const modifiers: PriceModifierDomain[] = [];
    const groups: any[] = [];

    // Define price modifier data
    const modifierData = [
      // Flat Amount Modifiers
      {
        name: 'Standard Fuel Surcharge',
        calculationType: ECalculationType.FlatAmount,
        calculationField: ECalculationField.BasePrice,
        amount: 5.0,
        description: 'Standard fuel surcharge applied to all deliveries',
      },
      {
        name: 'Handling Fee',
        calculationType: ECalculationType.FlatAmount,
        calculationField: ECalculationField.BasePrice,
        amount: 2.5,
        description: 'Fee for handling packages',
      },
      {
        name: 'Documentation Fee',
        calculationType: ECalculationType.FlatAmount,
        calculationField: ECalculationField.BasePrice,
        amount: 3.0,
        description: 'Fee for processing delivery documentation',
      },
      {
        name: 'Residential Delivery Fee',
        calculationType: ECalculationType.FlatAmount,
        calculationField: ECalculationField.BasePrice,
        amount: 4.5,
        description: 'Additional fee for residential deliveries',
      },

      // Flat Percentage Modifiers
      {
        name: 'Insurance Fee',
        calculationType: ECalculationType.FlatPercentage,
        calculationField: ECalculationField.DeclaredPrice,
        amount: 0.02, // 2%
        description: 'Insurance fee based on declared value',
      },
      {
        name: 'Peak Season Surcharge',
        calculationType: ECalculationType.FlatPercentage,
        calculationField: ECalculationField.BasePrice,
        amount: 0.1, // 10%
        description: 'Additional fee during peak season',
      },
      {
        name: 'Expedited Service Fee',
        calculationType: ECalculationType.FlatPercentage,
        calculationField: ECalculationField.BasePrice,
        amount: 0.15, // 15%
        description: 'Fee for expedited delivery service',
      },
      {
        name: 'Discount',
        calculationType: ECalculationType.FlatPercentage,
        calculationField: ECalculationField.BasePrice,
        amount: -0.05, // -5%
        description: 'Standard discount for regular customers',
      },

      // Overage Amount Modifiers
      {
        name: 'Heavy Package Fee',
        calculationType: ECalculationType.FlatOverageAmount,
        calculationField: ECalculationField.Weight,
        amount: 10.0,
        calculationStartAfter: 20, // kg
        description: 'Fee for packages over 20kg',
      },
      {
        name: 'Long Distance Fee',
        calculationType: ECalculationType.FlatOverageAmount,
        calculationField: ECalculationField.Distance,
        amount: 15.0,
        calculationStartAfter: 50, // km
        description: 'Fee for deliveries over 50km',
      },
      {
        name: 'Oversized Package Fee',
        calculationType: ECalculationType.FlatOverageAmount,
        calculationField: ECalculationField.CubicDimensions,
        amount: 12.0,
        calculationStartAfter: 125000, // 50x50x50 cm
        description: 'Fee for packages exceeding 125,000 cubic cm',
      },
      {
        name: 'Bulk Order Fee',
        calculationType: ECalculationType.FlatOverageAmount,
        calculationField: ECalculationField.Quantity,
        amount: 8.0,
        calculationStartAfter: 10, // items
        description: 'Fee for orders with more than 10 items',
      },

      // Overage Percentage Modifiers
      {
        name: 'High Value Surcharge',
        calculationType: ECalculationType.FlatOveragePercentage,
        calculationField: ECalculationField.DeclaredPrice,
        amount: 0.05, // 5%
        calculationStartAfter: 1000, // $
        description: 'Surcharge for high-value items over $1000',
      },
      {
        name: 'Premium Distance Surcharge',
        calculationType: ECalculationType.FlatOveragePercentage,
        calculationField: ECalculationField.Distance,
        amount: 0.08, // 8%
        calculationStartAfter: 100, // km
        description: 'Percentage surcharge for distances over 100km',
      },
      {
        name: 'Large Order Discount',
        calculationType: ECalculationType.FlatOveragePercentage,
        calculationField: ECalculationField.Quantity,
        amount: -0.03, // -3%
        calculationStartAfter: 20, // items
        description: 'Discount for orders with more than 20 items',
      },

      // Incremental Overage Amount Modifiers
      {
        name: 'Weight Increment Fee',
        calculationType: ECalculationType.IncrementalOverageAmount,
        calculationField: ECalculationField.Weight,
        amount: 2.0,
        increment: 5, // per 5kg
        calculationStartAfter: 10, // kg
        description: 'Additional fee of $2 per 5kg over 10kg',
      },
      {
        name: 'Distance Increment Fee',
        calculationType: ECalculationType.IncrementalOverageAmount,
        calculationField: ECalculationField.Distance,
        amount: 1.5,
        increment: 10, // per 10km
        calculationStartAfter: 30, // km
        description: 'Additional fee of $1.50 per 10km over 30km',
      },
      {
        name: 'Wait Time Fee',
        calculationType: ECalculationType.IncrementalOverageAmount,
        calculationField: ECalculationField.DeliveryWaitTime,
        amount: 5.0,
        increment: 15, // per 15 minutes
        calculationStartAfter: 15, // minutes
        description: 'Fee of $5 per 15 minutes of wait time over 15 minutes',
      },
      {
        name: 'Collection Wait Time Fee',
        calculationType: ECalculationType.IncrementalOverageAmount,
        calculationField: ECalculationField.CollectionWaitTime,
        amount: 4.0,
        increment: 15, // per 15 minutes
        calculationStartAfter: 10, // minutes
        description:
          'Fee of $4 per 15 minutes of collection wait time over 10 minutes',
      },

      // Incremental Overage Percentage Modifiers
      {
        name: 'Incremental Value Surcharge',
        calculationType: ECalculationType.IncrementalOveragePercentage,
        calculationField: ECalculationField.DeclaredPrice,
        amount: 0.01, // 1%
        increment: 1000, // per $1000
        calculationStartAfter: 2000, // $
        description: 'Additional 1% fee per $1000 of declared value over $2000',
      },
      {
        name: 'Incremental Quantity Discount',
        calculationType: ECalculationType.IncrementalOveragePercentage,
        calculationField: ECalculationField.Quantity,
        amount: -0.005, // -0.5%
        increment: 5, // per 5 items
        calculationStartAfter: 10, // items
        description: 'Additional 0.5% discount per 5 items over 10 items',
      },

      // Tiered Fixed Overage Amount Modifiers
      {
        name: 'Tiered Weight Fee',
        calculationType: ECalculationType.TieredFixedOverageAmount,
        calculationField: ECalculationField.Weight,
        tieredRanges: [
          { from: 0, to: 5, value: 0 },
          { from: 5, to: 10, value: 5 },
          { from: 10, to: 20, value: 10 },
          { from: 20, to: 50, value: 20 },
          { from: 50, to: null, value: 40 },
        ],
        description: 'Fee based on weight tiers',
      },
      {
        name: 'Tiered Distance Fee',
        calculationType: ECalculationType.TieredFixedOverageAmount,
        calculationField: ECalculationField.Distance,
        tieredRanges: [
          { from: 0, to: 10, value: 0 },
          { from: 10, to: 25, value: 5 },
          { from: 25, to: 50, value: 10 },
          { from: 50, to: 100, value: 20 },
          { from: 100, to: null, value: 35 },
        ],
        description: 'Fee based on distance tiers',
      },
      {
        name: 'Tiered Dimension Fee',
        calculationType: ECalculationType.TieredFixedOverageAmount,
        calculationField: ECalculationField.CubicDimensions,
        tieredRanges: [
          { from: 0, to: 27000, value: 0 }, // Up to 30x30x30 cm
          { from: 27000, to: 64000, value: 8 }, // Up to 40x40x40 cm
          { from: 64000, to: 125000, value: 15 }, // Up to 50x50x50 cm
          { from: 125000, to: 216000, value: 25 }, // Up to 60x60x60 cm
          { from: 216000, to: null, value: 40 }, // Larger than 60x60x60 cm
        ],
        description: 'Fee based on package dimension tiers',
      },

      // Tiered Fixed Overage Percentage Modifiers
      {
        name: 'Tiered Value Insurance',
        calculationType: ECalculationType.TieredFixedOveragePercentage,
        calculationField: ECalculationField.DeclaredPrice,
        tieredRanges: [
          { from: 0, to: 100, value: 0 },
          { from: 100, to: 500, value: 0.01 }, // 1%
          { from: 500, to: 1000, value: 0.015 }, // 1.5%
          { from: 1000, to: 5000, value: 0.02 }, // 2%
          { from: 5000, to: null, value: 0.025 }, // 2.5%
        ],
        description: 'Insurance percentage based on declared value tiers',
      },
      {
        name: 'Tiered Quantity Discount',
        calculationType: ECalculationType.TieredFixedOveragePercentage,
        calculationField: ECalculationField.Quantity,
        tieredRanges: [
          { from: 0, to: 5, value: 0 },
          { from: 5, to: 10, value: -0.02 }, // -2%
          { from: 10, to: 20, value: -0.05 }, // -5%
          { from: 20, to: 50, value: -0.08 }, // -8%
          { from: 50, to: null, value: -0.12 }, // -12%
        ],
        description: 'Discount percentage based on order quantity tiers',
      },

      // Tiered Incremental Overage Amount Modifiers
      {
        name: 'Tiered Incremental Weight Fee',
        calculationType: ECalculationType.TieredIncrementalOverageAmount,
        calculationField: ECalculationField.Weight,
        tieredRanges: [
          { from: 0, to: 10, value: 0 },
          { from: 10, to: 20, value: 1.5 }, // $1.5 per kg over 10kg
          { from: 20, to: 50, value: 2.0 }, // $2.0 per kg over 20kg
          { from: 50, to: 100, value: 2.5 }, // $2.5 per kg over 50kg
          { from: 100, to: null, value: 3.0 }, // $3.0 per kg over 100kg
        ],
        description: 'Incremental fee per kg based on weight tiers',
      },
      {
        name: 'Tiered Incremental Distance Fee',
        calculationType: ECalculationType.TieredIncrementalOverageAmount,
        calculationField: ECalculationField.Distance,
        tieredRanges: [
          { from: 0, to: 25, value: 0 },
          { from: 25, to: 50, value: 0.5 }, // $0.5 per km over 25km
          { from: 50, to: 100, value: 0.75 }, // $0.75 per km over 50km
          { from: 100, to: 200, value: 1.0 }, // $1.0 per km over 100km
          { from: 200, to: null, value: 1.25 }, // $1.25 per km over 200km
        ],
        description: 'Incremental fee per km based on distance tiers',
      },

      // Tiered Incremental Overage Percentage Modifiers
      {
        name: 'Tiered Incremental Value Surcharge',
        calculationType: ECalculationType.TieredIncrementalOveragePercentage,
        calculationField: ECalculationField.DeclaredPrice,
        tieredRanges: [
          { from: 0, to: 1000, value: 0 },
          { from: 1000, to: 5000, value: 0.002 }, // 0.2% per $100 over $1000
          { from: 5000, to: 10000, value: 0.003 }, // 0.3% per $100 over $5000
          { from: 10000, to: 50000, value: 0.004 }, // 0.4% per $100 over $10000
          { from: 50000, to: null, value: 0.005 }, // 0.5% per $100 over $50000
        ],
        description: 'Incremental percentage per $100 based on value tiers',
      },
      {
        name: 'Tiered Incremental Quantity Discount',
        calculationType: ECalculationType.TieredIncrementalOveragePercentage,
        calculationField: ECalculationField.Quantity,
        tieredRanges: [
          { from: 0, to: 10, value: 0 },
          { from: 10, to: 25, value: -0.001 }, // -0.1% per item over 10 items
          { from: 25, to: 50, value: -0.002 }, // -0.2% per item over 25 items
          { from: 50, to: 100, value: -0.003 }, // -0.3% per item over 50 items
          { from: 100, to: null, value: -0.004 }, // -0.4% per item over 100 items
        ],
        description:
          'Incremental discount percentage per item based on quantity tiers',
      },

      // Dimension-based modifiers
      {
        name: 'Height Surcharge',
        calculationType: ECalculationType.FlatOverageAmount,
        calculationField: ECalculationField.Height,
        amount: 7.5,
        calculationStartAfter: 100, // cm
        description: 'Surcharge for packages taller than 100cm',
      },
      {
        name: 'Width Surcharge',
        calculationType: ECalculationType.FlatOverageAmount,
        calculationField: ECalculationField.Width,
        amount: 7.5,
        calculationStartAfter: 100, // cm
        description: 'Surcharge for packages wider than 100cm',
      },
      {
        name: 'Length Surcharge',
        calculationType: ECalculationType.FlatOverageAmount,
        calculationField: ECalculationField.Length,
        amount: 10.0,
        calculationStartAfter: 150, // cm
        description: 'Surcharge for packages longer than 150cm',
      },

      // Custom amount modifier
      {
        name: 'Custom Fee',
        calculationType: ECalculationType.FlatPercentage,
        calculationField: ECalculationField.CustomAmount,
        amount: 0.05, // 5%
        description: 'Percentage fee based on custom amount',
      },
    ];

    // Create price modifiers
    for (const data of modifierData) {
      try {
        const modifierDomain = new PriceModifierDomain();
        modifierDomain.tenantId = tenantId;
        modifierDomain.name = data.name;
        modifierDomain.calculationType = data.calculationType;
        modifierDomain.fieldName = data.calculationField;
        modifierDomain.isActive = true;

        // Set specific properties based on calculation type
        if (data.amount !== undefined) {
          modifierDomain.amount = data.amount;
        }

        if (data.calculationStartAfter !== undefined) {
          modifierDomain.calculationBase = data.calculationStartAfter;
        }

        if (data.increment !== undefined) {
          modifierDomain.increment = data.increment;
        }

        if (data.tieredRanges !== undefined) {
          // Convert simple tiered ranges to the required format
          const formattedRanges = data.tieredRanges.map((range) => {
            const tieredRange = {
              fromValue: range.from,
              fromOperator: ERangeFromOperator.GreaterThanOrEqual,
              toValue: range.to !== null ? range.to : 999999, // Use a large number for "infinity"
              toOperator: ERangeToOperator.LessThanOrEqual,
              value: range.value,
            };
            return tieredRange;
          });

          modifierDomain.tieredRanges = formattedRanges;
        }

        // Add metadata for description
        if (data.description) {
          modifierDomain.metadata = {
            description: data.description,
          };
        }

        const createdModifier =
          await this.priceModifiersService.createPriceModifier(modifierDomain);
        modifiers.push(createdModifier);

        this.logger.debug(`Created price modifier: ${createdModifier.name}`);
      } catch (error) {
        this.logger.error(
          `Failed to create price modifier ${data.name}: ${error.message}`,
        );
        // Continue with other modifiers even if one fails
      }
    }

    // Create modifier groups
    const groupData = [
      {
        name: 'Standard Fees',
        description: 'Standard fees applied to most deliveries',
        behavior: EModifierGroupBehavior.UseSum,
        memberNames: [
          'Standard Fuel Surcharge',
          'Handling Fee',
          'Documentation Fee',
        ],
      },
      {
        name: 'Weight-Based Fees',
        description: 'Fees based on package weight',
        behavior: EModifierGroupBehavior.UseHighest,
        memberNames: [
          'Heavy Package Fee',
          'Weight Increment Fee',
          'Tiered Weight Fee',
          'Tiered Incremental Weight Fee',
        ],
      },
      {
        name: 'Distance-Based Fees',
        description: 'Fees based on delivery distance',
        behavior: EModifierGroupBehavior.UseHighest,
        memberNames: [
          'Long Distance Fee',
          'Distance Increment Fee',
          'Premium Distance Surcharge',
          'Tiered Distance Fee',
          'Tiered Incremental Distance Fee',
        ],
      },
      {
        name: 'Dimension-Based Fees',
        description: 'Fees based on package dimensions',
        behavior: EModifierGroupBehavior.UseSum,
        memberNames: [
          'Oversized Package Fee',
          'Height Surcharge',
          'Width Surcharge',
          'Length Surcharge',
          'Tiered Dimension Fee',
        ],
      },
      {
        name: 'Value-Based Fees',
        description: 'Fees based on declared value',
        behavior: EModifierGroupBehavior.UseHighest,
        memberNames: [
          'Insurance Fee',
          'High Value Surcharge',
          'Incremental Value Surcharge',
          'Tiered Value Insurance',
          'Tiered Incremental Value Surcharge',
        ],
      },
      {
        name: 'Quantity-Based Discounts',
        description: 'Discounts based on order quantity',
        behavior: EModifierGroupBehavior.UseLowest,
        memberNames: [
          'Large Order Discount',
          'Incremental Quantity Discount',
          'Tiered Quantity Discount',
          'Tiered Incremental Quantity Discount',
        ],
      },
      {
        name: 'Wait Time Fees',
        description: 'Fees based on wait times',
        behavior: EModifierGroupBehavior.UseSum,
        memberNames: ['Wait Time Fee', 'Collection Wait Time Fee'],
      },
      {
        name: 'Premium Services',
        description: 'Fees for premium delivery services',
        behavior: EModifierGroupBehavior.UseSum,
        memberNames: [
          'Expedited Service Fee',
          'Peak Season Surcharge',
          'Residential Delivery Fee',
        ],
      },
    ];

    // Create modifier groups
    for (const data of groupData) {
      try {
        // Find member modifiers by name
        const memberModifiers = modifiers.filter((m) =>
          data.memberNames.includes(m.name),
        );

        if (memberModifiers.length === 0) {
          this.logger.warn(`No members found for group ${data.name}, skipping`);
          continue;
        }

        // Create group domain
        const groupDomain = new PriceModifierGroupDomain();
        groupDomain.tenantId = tenantId;
        groupDomain.name = data.name;
        groupDomain.behavior = data.behavior;

        // Set members
        groupDomain.members = memberModifiers.map((m) => ({
          id: m.id,
          isGroup: false,
        }));

        // Set description
        if (data.description) {
          groupDomain.description = data.description;
        }

        // Create the group
        const group =
          await this.priceModifiersService.createPriceModifierGroup(
            groupDomain,
          );

        groups.push(group);

        this.logger.debug(
          `Created modifier group: ${group.name} with ${memberModifiers.length} members`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to create modifier group ${data.name}: ${error.message}`,
        );
        // Continue with other groups even if one fails
      }
    }

    return { modifiers, groups };
  }

  /**
   * Create price sets for the tenant
   * @param tenantId The tenant ID
   * @returns Array of created price sets
   */
  private async createPriceSets(tenantId: string): Promise<PriceSetDomain[]> {
    const priceSets: PriceSetDomain[] = [];

    // Define price set configurations
    const priceSetConfigs = [
      {
        name: 'Standard Delivery',
        internalName: 'standard_delivery',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Standard delivery service with regular pricing',
        notes: 'Available during business hours',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.By,
          offsetData: {
            time: '14:00',
            daysOut: 1,
            includeWeekends: false,
          },
          schedule: [
            {
              days: 'Monday,Tuesday,Wednesday,Thursday,Friday',
              startTime: '08:00',
              endTime: '17:00',
            },
          ],
        },
      },
      {
        name: 'Express Delivery',
        internalName: 'express_delivery',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Premium expedited delivery service',
        notes: 'Available 24/7 with premium pricing',
        isActive: true,
        availabilityType: AvailabilityType.Always,
        scheduleData: {
          offsetType: OffsetType.By,
          offsetData: {
            time: '12:00',
            daysOut: 0,
            includeWeekends: true,
          },
        },
      },
      {
        name: 'Economy Delivery',
        internalName: 'economy_delivery',
        paymentOption: PriceSetPaymentOption.Partial,
        description: 'Cost-effective delivery option for non-urgent shipments',
        notes: 'Longer delivery times but lower cost',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.By,
          offsetData: {
            time: '17:00',
            daysOut: 3,
            includeWeekends: false,
          },
          schedule: [
            {
              days: 'Monday,Wednesday,Friday',
              startTime: '09:00',
              endTime: '16:00',
            },
          ],
        },
      },
      {
        name: 'Weekend Delivery',
        internalName: 'weekend_delivery',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Special weekend delivery service',
        notes: 'Available only on weekends',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.To,
          offsetData: {
            hours: 12,
            minutes: 0,
          },
          schedule: [
            {
              days: 'Saturday,Sunday',
              startTime: '10:00',
              endTime: '16:00',
            },
          ],
        },
      },
      {
        name: 'Business Hours Delivery',
        internalName: 'business_hours_delivery',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Delivery during standard business hours',
        notes: 'Reliable delivery for business customers',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.By,
          offsetData: {
            time: '16:00',
            daysOut: 1,
            includeWeekends: false,
          },
          schedule: [
            {
              days: 'Monday,Tuesday,Wednesday,Thursday,Friday',
              startTime: '09:00',
              endTime: '17:00',
            },
          ],
        },
      },
      {
        name: 'Same-Day Business Critical',
        internalName: 'sameday_critical',
        paymentOption: PriceSetPaymentOption.Full,
        description:
          'Urgent same-day delivery within 4 business hours of booking.',
        notes: 'Book by 2 PM for same-day. Weekdays only.',
        isActive: true,
        availabilityType: AvailabilityType.Weekly, // Or Always if cutoff is handled by booking logic
        scheduleData: {
          offsetType: OffsetType.To, // "deliver within X hours"
          offsetData: {
            hours: 4,
            minutes: 0,
          },
          schedule: [
            // Operating hours for this service
            {
              days: 'Monday,Tuesday,Wednesday,Thursday,Friday',
              startTime: '08:00',
              endTime: '18:00', // Last delivery completion time
            },
          ],
        },
      },
      {
        name: 'Next-Day AM Guaranteed',
        internalName: 'nextday_am_guaranteed',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Guaranteed delivery by 12:00 PM next business day.',
        notes: 'Book by 5 PM previous day.',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.By, // "deliver by this time"
          offsetData: {
            time: '12:00',
            daysOut: 1,
            includeWeekends: false,
          },
          schedule: [
            // When bookings are accepted leading to this
            {
              days: 'Monday,Tuesday,Wednesday,Thursday,Friday',
              startTime: '00:00', // Effectively any time previous day until cutoff
              endTime: '17:00', // Cutoff time
            },
          ],
        },
      },
      {
        name: 'Scheduled Window Delivery (2hr)',
        internalName: 'scheduled_2hr_window',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Customer chooses a 2-hour delivery window.',
        notes: 'Available on weekdays. Requires advance booking.',
        isActive: true,
        availabilityType: AvailabilityType.Weekly, // The available windows
        scheduleData: {
          offsetType: OffsetType.To, // Or 'By' if you mean "book by X for window Y"
          offsetData: {
            // This might be more about booking options than strict offset
            hours: 2, // The window duration
            minutes: 0,
          },
          schedule: [
            {
              days: 'Monday,Tuesday,Wednesday,Thursday,Friday',
              startTime: '09:00', // Start of first possible window
              endTime: '17:00', // End of last possible window
            },
          ],
        },
      },
      {
        name: 'After-Hours Delivery',
        internalName: 'afterhours_delivery',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Delivery service between 6 PM and 10 PM on weekdays.',
        notes:
          'Ideal for businesses receiving shipments outside standard hours.',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.By, // Deliver by 10 PM if booked by X
          offsetData: {
            time: '22:00',
            daysOut: 0, // Same day if booked early enough
            includeWeekends: false,
          },
          schedule: [
            {
              days: 'Monday,Tuesday,Wednesday,Thursday,Friday',
              startTime: '18:00',
              endTime: '22:00',
            },
          ],
        },
      },
      {
        name: 'Weekend Express (Saturday)',
        internalName: 'weekend_express_sat',
        paymentOption: PriceSetPaymentOption.Full,
        description: 'Express delivery service on Saturdays.',
        notes: 'Limited availability, premium rates.',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.By,
          offsetData: {
            time: '16:00', // Deliver by 4 PM on Saturday
            daysOut: 0, // If booked on Saturday, or based on booking day
            includeWeekends: true,
          },
          schedule: [
            {
              days: 'Saturday',
              startTime: '09:00',
              endTime: '16:00',
            },
          ],
        },
      },
      {
        name: 'Economy Bulk Freight (3-5 Days)',
        internalName: 'economy_bulk_3_5_days',
        paymentOption: PriceSetPaymentOption.Partial, // Or Full, depending on B2B model
        description: 'Cost-effective solution for non-urgent bulk shipments.',
        notes: 'Delivery within 3-5 business days.',
        isActive: true,
        availabilityType: AvailabilityType.Weekly,
        scheduleData: {
          offsetType: OffsetType.By,
          offsetData: {
            time: '17:00', // By end of day
            daysOut: 5, // Max days out
            includeWeekends: false,
          },
          schedule: [
            // When this service can be booked/operates
            {
              days: 'Monday,Tuesday,Wednesday,Thursday,Friday',
              startTime: '09:00',
              endTime: '17:00',
            },
          ],
        },
      },
    ];

    // Create price sets
    for (const config of priceSetConfigs) {
      try {
        // Create price set domain
        const priceSetDomain = new PriceSetDomain();
        priceSetDomain.tenantId = tenantId;
        priceSetDomain.name = config.name;
        priceSetDomain.internalName = config.internalName;
        priceSetDomain.paymentOption = config.paymentOption;
        priceSetDomain.description = config.description;
        priceSetDomain.notes = config.notes;
        priceSetDomain.isActive = config.isActive;

        // Create the price set
        const createdPriceSet =
          await this.priceSetsService.create(priceSetDomain);
        priceSets.push(createdPriceSet);

        // Create schedule for the price set
        if (config.scheduleData) {
          const scheduleDto = new CreateScheduleDto();
          scheduleDto.availabilityType = config.availabilityType;
          scheduleDto.offsetType = config.scheduleData.offsetType;

          // Create proper OffsetData object
          const offsetData = new OffsetData();
          if (config.scheduleData.offsetType === OffsetType.To) {
            offsetData.hours = config.scheduleData.offsetData.hours || null;
            offsetData.minutes = config.scheduleData.offsetData.minutes || null;
            offsetData.time = null;
            offsetData.daysOut = null;
            offsetData.includeWeekends = null;
          } else {
            offsetData.hours = null;
            offsetData.minutes = null;
            offsetData.time = config.scheduleData.offsetData.time || null;
            offsetData.daysOut = config.scheduleData.offsetData.daysOut || null;
            offsetData.includeWeekends =
              config.scheduleData.offsetData.includeWeekends || null;
          }
          scheduleDto.offsetData = offsetData;

          // Create proper Schedule array if needed
          if (
            config.scheduleData.schedule &&
            config.availabilityType === AvailabilityType.Weekly
          ) {
            scheduleDto.schedule = config.scheduleData.schedule.map((s) => {
              const schedule = new Schedule();
              schedule.days = s.days;
              schedule.startTime = s.startTime;
              schedule.endTime = s.endTime;
              return schedule;
            });
          } else {
            scheduleDto.schedule = null;
          }

          await this.priceSetsService.editSchedule(
            createdPriceSet.id,
            scheduleDto,
          );
        }

        this.logger.debug(`Created price set: ${createdPriceSet.name}`);
      } catch (error) {
        this.logger.error(
          `Failed to create price set ${config.name}: ${error.message}`,
        );
        // Continue with other price sets even if one fails
      }
    }

    return priceSets;
  }

  /**
   * Assign zone tables to price sets
   * @param tenantId The tenant ID
   * @param priceSets The price sets to assign to
   * @param zoneTables The zone tables to assign
   */
  private async assignZoneTablesToSets(
    tenantId: string,
    priceSets: PriceSetDomain[],
    zoneTables: ZoneTableDomain[],
  ): Promise<void> {
    if (priceSets.length === 0 || zoneTables.length === 0) {
      this.logger.warn('No price sets or zone tables available for assignment');
      return;
    }

    this.logger.log(`Assigning zone tables to ${priceSets.length} price sets`);

    // Map price sets to zone tables based on their names
    const assignments = [
      {
        priceSetName: 'Standard Delivery',
        zoneTableName: 'Standard Delivery Rates',
      },
      {
        priceSetName: 'Express Delivery',
        zoneTableName: 'Express Delivery Rates',
      },
      {
        priceSetName: 'Economy Delivery',
        zoneTableName: 'Economy Delivery Rates',
      },
      {
        priceSetName: 'Weekend Delivery',
        zoneTableName: 'Standard Delivery Rates',
      },
      {
        priceSetName: 'Business Hours Delivery',
        zoneTableName: 'Standard Delivery Rates',
      },
    ];

    // Assign zone tables to price sets
    for (const assignment of assignments) {
      try {
        // Find the price set and zone table
        const priceSet = priceSets.find(
          (ps) => ps.name === assignment.priceSetName,
        );
        const zoneTable = zoneTables.find(
          (zt) => zt.name === assignment.zoneTableName,
        );

        if (!priceSet || !zoneTable) {
          this.logger.warn(
            `Could not find price set "${assignment.priceSetName}" or zone table "${assignment.zoneTableName}"`,
          );
          continue;
        }

        // Create zone table DTO
        const zoneTableDto = new CreateZoneTableDto();
        zoneTableDto.name = zoneTable.name;
        zoneTableDto.zoneTableValues = zoneTable.zoneTableValues.map(
          (value) => ({
            originZoneId: value.originZoneId,
            destinationZoneId: value.destinationZoneId,
            value: value.value,
          }),
        );

        // Assign zone table to price set
        await this.priceSetsService.basePriceByZone(
          tenantId,
          priceSet.id,
          zoneTableDto,
        );

        this.logger.debug(
          `Assigned zone table "${zoneTable.name}" to price set "${priceSet.name}"`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to assign zone table to price set: ${error.message}`,
        );
        // Continue with other assignments even if one fails
      }
    }
  }

  private async assignModifiersToSets(
    priceSets: PriceSetDomain[],
    modifiers: PriceModifierDomain[],
    groups: PriceModifierGroupDomain[],
  ): Promise<void> {
    if (
      priceSets.length === 0 ||
      (modifiers.length === 0 && groups.length === 0)
    ) {
      this.logger.warn('No price sets or modifiers available for assignment');
      return;
    }
    this.logger.log(`Assigning modifiers to ${priceSets.length} price sets`);

    const assignments: {
      priceSetName: string;
      modifiers: {
        name: string;
        isGroup: boolean;
        configuration: ConfigurationType;
      }[];
    }[] = [
      // Original 5
      {
        priceSetName: 'Standard Delivery',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Weight-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Dimension-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Wait Time Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Express Delivery',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Weight-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Distance-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Dimension-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Wait Time Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Economy Delivery',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Weight-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Quantity-Based Discounts',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Discount',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Weekend Delivery',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Weight-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Dimension-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Residential Delivery Fee',
            isGroup: false,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Weekend Premium Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Business Hours Delivery',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Distance-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Dimension-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Wait Time Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      // New B2B Price Sets
      {
        priceSetName: 'Same-Day Business Critical (4hr)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Weight-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Expedited Service Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Next-Day AM Guaranteed (by 12 PM)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Documentation Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Peak Season Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Scheduled Window Delivery (2hr slot)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Timed Delivery Slot Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Wait Time Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'After-Hours Delivery (6 PM - 10 PM)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'After Hours Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Weekend Express (Saturday by 4 PM)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Weekend Premium Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Economy Bulk Freight (3-5 Days)',
        modifiers: [
          {
            name: 'Standard Fuel Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Weight-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Dimension-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Handling Fee',
            isGroup: false,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Quantity-Based Discounts',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Early Bird Delivery (by 8 AM Weekdays)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Expedited Service Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Overnight Express (Next Day by 9 AM)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Retail Chain Regular Restock',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Wait Time Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Quantity-Based Discounts',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Inter-Office Shuttle (Daily Fixed)',
        modifiers: [
          {
            name: 'Standard Fuel Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Discount',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
        ],
      }, // Minimal, contract pricing
      {
        priceSetName: 'Pharma Temp-Controlled Urgent',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Temperature Control Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Legal Document Courier (2hr Metro)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Expedited Service Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Documentation Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
        ],
      },
      {
        priceSetName: 'Dedicated Vehicle - Half Day',
        modifiers: [
          {
            name: 'Standard Fuel Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Expedited Service Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
        ],
      }, // Base + vehicle cost likely separate
      {
        priceSetName: 'Standard Pallet - Next Day EOD',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Weight-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Dimension-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Handling Fee',
            isGroup: false,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Small Parcel Network - 2 Day Economy',
        modifiers: [
          {
            name: 'Standard Fuel Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Selected,
          },
          {
            name: 'Discount',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Quantity-Based Discounts',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Sunday Premium Delivery (Limited)',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Weekend Premium Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Value-Based Fees',
            isGroup: true,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
      {
        priceSetName: 'Event & Expo - Timed Setup Delivery',
        modifiers: [
          {
            name: 'Standard Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Premium Services',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Timed Delivery Slot Fee',
            isGroup: false,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'Wait Time Fees',
            isGroup: true,
            configuration: ConfigurationType.Required,
          },
          {
            name: 'After Hours Surcharge',
            isGroup: false,
            configuration: ConfigurationType.Selected,
          },
        ],
      },
    ];

    for (const assignment of assignments) {
      try {
        const priceSet = priceSets.find(
          (ps) => ps.name === assignment.priceSetName,
        );
        if (!priceSet) {
          this.logger.warn(
            `Could not find price set "${assignment.priceSetName}" for modifier assignment.`,
          );
          continue;
        }
        const membersToAssign: ModifierMemberDto[] = [];
        for (const modifierConfig of assignment.modifiers) {
          let modifierId: string | undefined;
          let foundItemName: string | undefined;
          if (modifierConfig.isGroup) {
            const group = groups.find((g) => g.name === modifierConfig.name);
            if (group) {
              modifierId = group.id;
              foundItemName = group.name;
            }
          } else {
            const modifier = modifiers.find(
              (m) => m.name === modifierConfig.name,
            );
            if (modifier) {
              modifierId = modifier.id;
              foundItemName = modifier.name;
            }
          }
          if (modifierId && foundItemName) {
            membersToAssign.push({
              id: modifierId,
              isGroup: modifierConfig.isGroup,
            });
            this.logger.debug(
              `Prepared ${modifierConfig.isGroup ? 'group' : 'modifier'} "${foundItemName}" for price set "${priceSet.name}"`,
            );
          } else {
            this.logger.warn(
              `Could not find ${modifierConfig.isGroup ? 'group' : 'modifier'} with name "${modifierConfig.name}" for price set "${priceSet.name}".`,
            );
          }
        }

        if (membersToAssign.length > 0) {
          const assignModifiersDto = new AssignModifiersDto();
          assignModifiersDto.members = membersToAssign;
          await this.priceSetsService.assignPriceModifiers(
            priceSet.id,
            assignModifiersDto,
          );
          this.logger.log(
            `Assigned ${membersToAssign.length} initial modifiers/groups to price set "${priceSet.name}"`,
          );

          const assignedModifiersForConfig =
            await this.priceSetsService.getPriceSetModifiers(priceSet.id);
          for (const memberToConfig of membersToAssign) {
            const originalConfig = assignment.modifiers.find((modConfig) => {
              const item = memberToConfig.isGroup
                ? groups.find((g) => g.id === memberToConfig.id)
                : modifiers.find((m) => m.id === memberToConfig.id);
              return (
                item &&
                item.name === modConfig.name &&
                modConfig.isGroup === memberToConfig.isGroup
              );
            });
            if (!originalConfig) {
              this.logger.warn(
                `Could not find original config for member ID ${memberToConfig.id} in price set "${priceSet.name}" for configuration.`,
              );
              continue;
            }
            const actualAssignedItem = assignedModifiersForConfig.find(
              (am) =>
                am.memberId === memberToConfig.id &&
                am.isGroup === memberToConfig.isGroup,
            );
            if (actualAssignedItem) {
              try {
                await this.priceSetsService.editConfiguration(
                  actualAssignedItem.id,
                  originalConfig.configuration,
                );
                this.logger.debug(
                  `Set configuration "${originalConfig.configuration}" for ${originalConfig.isGroup ? 'group' : 'modifier'} "${originalConfig.name}" on price set "${priceSet.name}"`,
                );
              } catch (configError) {
                this.logger.error(
                  `Failed to set configuration for ${originalConfig.name} on price set ${priceSet.name}: ${configError.message}`,
                );
              }
            } else {
              this.logger.warn(
                `Could not find assigned item for member ID ${memberToConfig.id} (${originalConfig.name}) in price set "${priceSet.name}" to set configuration.`,
              );
            }
          }
        } else {
          this.logger.log(
            `No valid modifiers/groups found to assign to price set "${priceSet.name}".`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to process modifier assignment for price set "${assignment.priceSetName}": ${error.message}`,
        );
      }
    }
  }
}
