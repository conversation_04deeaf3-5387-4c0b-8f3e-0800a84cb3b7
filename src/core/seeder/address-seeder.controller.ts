import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SeederService } from './seeder.service';
import { AddressSeederDto } from './dto/address-seeder.dto';
import { AddressSeederResponseDto } from './dto/address-seeder-response.dto';

@ApiTags('Address Seeder')
@Controller({
  path: 'address-seeder',
  version: '1',
})
export class AddressSeederController {
  constructor(private readonly seederService: SeederService) {}

  @Post()
  @ApiOperation({ summary: 'Seed addresses for a customer' })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Addresses seeded successfully',
    type: AddressSeederResponseDto,
  })
  async seedAddresses(
    @Body() addressSeederDto: AddressSeederDto,
  ): Promise<AddressSeederResponseDto> {
    return this.seederService.seedAddresses(
      addressSeederDto.tenantId,
      addressSeederDto.customerId,
    );
  }
}
