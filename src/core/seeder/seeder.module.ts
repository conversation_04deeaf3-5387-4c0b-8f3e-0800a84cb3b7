import { Module } from '@nestjs/common';
import { SeederService } from './seeder.service';
import { SeederController } from './seeder.controller';
import { AddressSeederController } from './address-seeder.controller';
import { ZonesModule } from '../../business/zone/zones/zones.module';
import { ZoneTableModule } from '../../business/zone/zone-tables/zone-table.module';
import { PriceModifiersModule } from '../../business/pricing/price-modifiers/price-modifiers.module';
import { PriceSetsModule } from '../../business/pricing/price-sets/price-sets.module';
import { TenantsModule } from '../../business/user/tenants/tenants.module';
import { AddressModule } from '../../business/address/addresses/address.module';

@Module({
  imports: [
    ZonesModule,
    ZoneTableModule,
    PriceModifiersModule,
    PriceSetsModule,
    TenantsModule,
    AddressModule,
  ],
  controllers: [Seeder<PERSON>ontroller, AddressSeederController],
  providers: [SeederService],
  exports: [SeederService],
})
export class SeederModule {}
