import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SeederService } from './seeder.service';
import { SeederDto } from './dto/seeder.dto';
import { SeederResponseDto } from './dto/seeder-response.dto';

@ApiTags('Core - Seeder')
@Controller({
  path: 'seeder',
  version: '1',
})
export class SeederController {
  constructor(private readonly seederService: SeederService) {}

  @Post()
  @ApiOperation({ summary: 'Seed data for a tenant' })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Data seeded successfully',
    type: SeederResponseDto,
  })
  async seedData(@Body() seederDto: SeederDto): Promise<SeederResponseDto> {
    return this.seederService.seedData(seederDto.tenantId);
  }
}
