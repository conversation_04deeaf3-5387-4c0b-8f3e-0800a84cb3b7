import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { SettingsController } from './settings.controller';
import { SettingsService } from './settings.service';
import { RelationalSettingPersistenceModule } from './infrastructure/relational-persistence.module';
import { TenantsModule } from '../../business/user/tenants/tenants.module';
import { RelationalUserPersistenceModule } from '../../business/user/users/infrastructure/relational-persistence.module';
import { ContactsModule } from '../../business/user/contacts/contacts.module';
import { ContactSettingsController } from './contactSettings.controller';

@Module({
  imports: [
    RelationalSettingPersistenceModule,
    RelationalUserPersistenceModule,
    ContactsModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    TenantsModule,
  ],
  controllers: [SettingsController, ContactSettingsController],
  providers: [SettingsService],
})
export class SettingModule {}
