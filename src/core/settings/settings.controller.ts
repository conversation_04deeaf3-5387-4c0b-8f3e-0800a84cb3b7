import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import {
  CreateSettingsDto,
  UpdateSettingsDto,
} from './dto/create-settings.dto';
import { SettingsService } from './settings.service';
import { TenantValidationService } from '../../business/user/tenants/tenant-validation.service';
import { UserType } from './domain/settings.type';

@ApiTags('Core - Settings')
@Controller({
  path: 'settings',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class SettingsController {
  constructor(
    private readonly settingsService: SettingsService,
    private readonly tenantValidationService: TenantValidationService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Create new settings',
    description: `
    Creates a new setting for the current tenant. This endpoint supports:
    - Creating system-wide settings (global scope)
    - Creating user-specific settings
    
    Key Features:
    - User Id is Optional here.
    - Scope can be system or user.
    - Key should always be unique for the user.
    `,
  })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createSettingsDto: CreateSettingsDto,
  ) {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);
    try {
      const settings = await this.settingsService.create(
        createSettingsDto,
        tenant.id,
        UserType.User,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }

  @Get(':key')
  @ApiOperation({ summary: 'Get global settings for tenant by key' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK' })
  async getGlobalSettings(
    @Req() request: RequestWithUser,
    @Param('key') key: string,
  ) {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);
    try {
      const settings = await this.settingsService.getGlobalSettings(
        tenant.id,
        key,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }

  @Get('/user/:userId/:key')
  @ApiOperation({ summary: 'Get user settings by key' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK' })
  async getUserSettings(
    @Req() request: RequestWithUser,
    @Param('userId') userId: string,
    @Param('key') key: string,
  ) {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);
    try {
      const settings = await this.settingsService.getUserSettings(
        tenant.id,
        userId,
        key,
        UserType.User,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }

  @Put(':settingId')
  @ApiOperation({
    summary: 'Update settings by setting id',
    description:
      'Updates the value of an existing setting by its unique identifier. The Body will just contain the value field which is in json.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiCreatedResponse({ description: 'No Content' })
  async update(
    @Param('settingId') settingId: string,
    @Body() updateSettingsDto: UpdateSettingsDto,
  ) {
    try {
      const settings = await this.settingsService.update(
        settingId,
        updateSettingsDto,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }
}
