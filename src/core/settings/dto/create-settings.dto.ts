import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsUUID,
  IsEnum,
  IsString,
  IsOptional,
} from 'class-validator';
import { Scope } from '../domain/settings.type';

export class CreateSettingsDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsUUID(4)
  @IsOptional()
  userId: string;

  @AutoMap()
  @ApiProperty({ enum: Scope })
  @IsEnum(Scope)
  @IsNotEmpty()
  scope: string;

  @AutoMap()
  @ApiProperty({ example: 'theme' })
  @IsString()
  @IsNotEmpty()
  key: string;

  @AutoMap()
  @ApiProperty()
  @IsNotEmpty()
  value: Record<string, any>;
}

export class UpdateSettingsDto {
  @AutoMap()
  @ApiProperty()
  @IsNotEmpty()
  value: Record<string, any>;
}
