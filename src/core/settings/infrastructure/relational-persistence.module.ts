import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SettingRepository } from './repositories/settings.repository';
import { SettingEntity } from './entities/settings.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SettingEntity])],
  providers: [SettingRepository],
  exports: [SettingRepository],
})
export class RelationalSettingPersistenceModule {}
