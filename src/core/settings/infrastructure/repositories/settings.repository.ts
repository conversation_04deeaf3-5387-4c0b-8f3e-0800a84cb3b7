import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, Repository } from 'typeorm';

import { SettingEntity } from '../entities/settings.entity';
import { CreateSettingsDto } from '../../dto/create-settings.dto';
import { EntityCondition } from '../../../../utils/types/entity-condition.type';

@Injectable()
export class SettingRepository {
  constructor(
    @InjectRepository(SettingEntity)
    private readonly settingRepository: Repository<SettingEntity>,
  ) {}

  async create(createSettingsDto: CreateSettingsDto, tenantId: string) {
    const setting = await this.settingRepository.save(
      this.settingRepository.create({ ...createSettingsDto, tenantId }),
    );
    return setting;
  }

  async findOne(fields: EntityCondition<SettingEntity>) {
    const setting = await this.settingRepository.findOne({
      where: {
        ...(fields as FindOptionsWhere<SettingEntity>),
      },
    });
    return setting;
  }

  async update(setting: SettingEntity) {
    const updatedSetting = await this.settingRepository.save(setting);
    return updatedSetting;
  }
}
