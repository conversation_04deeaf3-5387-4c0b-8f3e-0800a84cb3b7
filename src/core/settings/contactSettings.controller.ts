import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import {
  CreateSettingsDto,
  UpdateSettingsDto,
} from './dto/create-settings.dto';
import { SettingsService } from './settings.service';
import { UserType } from './domain/settings.type';
import { JwtContactAuthGuard } from '../auth/guards/jwt-contact-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { JwtPayload } from '../auth/domain/auth.types';
import { ContactsService } from '../../business/user/contacts/contacts.service';

@ApiTags('Core - Contact Settings')
@Controller({
  path: 'settings/contact',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtContactAuthGuard, TenantAuthGuard)
export class ContactSettingsController {
  constructor(
    private readonly settingsService: SettingsService,
    private readonly contactsService: ContactsService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Create new settings',
    description: `
    Creates a new setting for the current tenant. This endpoint supports:
    - Creating system-wide settings (global scope)
    - Creating user-specific settings
    
    Key Features:
    - User Id is Optional here.
    - Scope can be system or user.
    - Key should always be unique for the user.
    `,
  })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @CurrentUser() contactData: JwtPayload,
    @Body() createSettingsDto: CreateSettingsDto,
  ) {
    const contact = await this.contactsService.findById(contactData.sub);

    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }
    try {
      const settings = await this.settingsService.create(
        createSettingsDto,
        contact.tenantId,
        UserType.Contact,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }

  @Get(':key')
  @ApiOperation({ summary: 'Get global settings for tenant by key' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK' })
  async getGlobalSettings(
    @CurrentUser() contactData: JwtPayload,
    @Param('key') key: string,
  ) {
    const contact = await this.contactsService.findById(contactData.sub);

    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }
    try {
      const settings = await this.settingsService.getGlobalSettings(
        contact.tenantId,
        key,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }

  @Get(':userId/:key')
  @ApiOperation({ summary: 'Get user settings by key' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK' })
  async getUserSettings(
    @CurrentUser() contactData: JwtPayload,
    @Param('userId') userId: string,
    @Param('key') key: string,
  ) {
    const contact = await this.contactsService.findById(contactData.sub);

    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    try {
      const settings = await this.settingsService.getUserSettings(
        contact?.tenantId,
        userId,
        key,
        UserType.Contact,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }

  @Put(':settingId')
  @ApiOperation({
    summary: 'Update settings by setting id',
    description:
      'Updates the value of an existing setting by its unique identifier. The Body will just contain the value field which is in json.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiCreatedResponse({ description: 'No Content' })
  async update(
    @Param('settingId') settingId: string,
    @Body() updateSettingsDto: UpdateSettingsDto,
  ) {
    try {
      const settings = await this.settingsService.update(
        settingId,
        updateSettingsDto,
      );
      return settings;
    } catch (error) {
      throw error;
    }
  }
}
