import { Injectable } from '@nestjs/common';
import {
  CreateSettingsDto,
  UpdateSettingsDto,
} from './dto/create-settings.dto';
import { SettingRepository } from './infrastructure/repositories/settings.repository';
import {
  SettingAlreadyExistsException,
  SettingNotFoundException,
} from '../../utils/errors/exceptions/settings-exception';
import { UsersRepository } from '../../business/user/users/infrastructure/repositories/user.repository';
import { UserNotFoundException } from '../../utils/errors/exceptions/user.exceptions';
import { Scope, UserType } from './domain/settings.type';
import { ContactNotFoundException } from '../../utils/errors/exceptions/contact.exceptions';
import { ContactsService } from '../../business/user/contacts/contacts.service';

@Injectable()
export class SettingsService {
  constructor(
    private readonly settingsRepository: SettingRepository,
    private readonly userRepository: UsersRepository,
    private readonly contactService: ContactsService,
  ) {}

  async create(
    createSettingsDto: CreateSettingsDto,
    tenantId: string,
    type: string,
  ) {
    const setting = await this.settingsRepository.findOne({
      tenantId,
      userId: createSettingsDto.userId,
      key: createSettingsDto.key,
    });
    if (setting) {
      throw new SettingAlreadyExistsException(createSettingsDto.key);
    }

    if (type === UserType.User) {
      const customer = await this.userRepository.findById(
        createSettingsDto.userId,
      );
      if (!customer) {
        throw new UserNotFoundException(createSettingsDto.userId);
      }
    } else if (type === UserType.Contact) {
      const contact = await this.contactService.findById(
        createSettingsDto.userId,
      );
      if (!contact) {
        throw new ContactNotFoundException(createSettingsDto.userId);
      }
    }

    const newSetting = await this.settingsRepository.create(
      createSettingsDto,
      tenantId,
    );
    return newSetting;
  }

  async getGlobalSettings(tenantId: string, key: string) {
    const setting = await this.settingsRepository.findOne({
      tenantId,
      scope: Scope.System,
      key,
    });

    if (!setting) {
      throw new SettingNotFoundException(tenantId);
    }
    return setting;
  }

  async getUserSettings(
    tenantId: string,
    userId: string,
    key: string,
    type: string,
  ) {
    if (type === UserType.User) {
      const customer = await this.userRepository.findById(userId);
      if (!customer) {
        throw new UserNotFoundException(userId);
      }
    } else if (type === UserType.Contact) {
      const contact = await this.contactService.findById(userId);
      if (!contact) {
        throw new ContactNotFoundException(userId);
      }
    }

    const setting = await this.settingsRepository.findOne({
      tenantId,
      userId,
      key,
    });

    if (!setting) {
      throw new SettingNotFoundException(userId);
    }
    return setting;
  }

  async update(settingId: string, updateSettingsDto: UpdateSettingsDto) {
    const setting = await this.settingsRepository.findOne({
      id: settingId,
    });
    if (!setting) {
      throw new SettingNotFoundException(settingId);
    }

    setting.value = updateSettingsDto.value;

    const updatedSetting = await this.settingsRepository.update(setting);
    return updatedSetting;
  }
}
