import {
  ErrorConfig,
  ErrorQueryOptions,
  TrackedError,
} from './types/error-query.types';
import { ErrorSeverity } from '../logger/types/severity.types';

export const ERROR_TRACKING_SERVICE = Symbol('ERROR_TRACKING_SERVICE');

export interface IErrorTrackingService {
  /**
   * Tracks and processes a system error with configurable severity and metadata
   */
  trackError(error: Error, config: ErrorConfig): Promise<string>;

  /**
   * Retrieves errors matching specified criteria
   */
  queryErrors(options?: ErrorQueryOptions): Promise<TrackedError[]>;

  /**
   * Retrieves error statistics for analysis
   */
  getErrorStats(timeRange?: number): Promise<Record<ErrorSeverity, number>>;

  /**
   * Subscribes to error events by severity
   */
  onError(
    severity: ErrorSeverity,
    handler: (error: TrackedError) => void,
  ): () => void;
}
