import { ErrorSeverity } from '@core/infrastructure/logger/types/severity.types';

export interface ErrorQueryOptions {
  severity?: ErrorSeverity;
  fromDate?: Date;
  toDate?: Date;
  tags?: Record<string, string>;
  limit?: number;
  offset?: number;
}

export interface TrackedError extends Error {
  id: string;
  timestamp: Date;
  severity: ErrorSeverity;
  statusCode?: number;
  code: string;
  details?: Record<string, any>;
  cause?: Error;
  tags?: Record<string, string>;
  stack?: string;
  correlationId: string;
  traceId: string;
  spanId: string;
  tenantId?: string;
  userId?: string;
  environment: string;
  service: string;
}

export interface ErrorConfig {
  severity: ErrorSeverity;
  context?: Record<string, any>;
  tags?: Record<string, string>;
}
