import {
  Inject,
  Injectable,
  OnApplicationShutdown,
  forwardRef,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CorrelationService } from '../correlation/correlation.service';
import { ILoggerService, LOGGER_SERVICE } from '../logger/logger.interface';
import {
  ErrorQueryOptions,
  TrackedError,
  ErrorConfig,
} from './types/error-query.types';
import { ERROR_TRACKING_CONSTANTS } from './error-tracking.constants';
import { LogContext } from '../logger/types/logger.types';
import { ErrorSeverity } from '../logger/types/severity.types';
import { IErrorTrackingService } from './error-tracking.interface';

@Injectable()
export class ErrorTrackingService
  implements IErrorTrackingService, OnApplicationShutdown
{
  private readonly errorBuffer: TrackedError[] = [];
  private readonly MAX_BUFFER_SIZE = ERROR_TRACKING_CONSTANTS.MAX_BUFFER_SIZE;
  private isShuttingDown = false;
  private readonly ERROR_EVENTS = ERROR_TRACKING_CONSTANTS.ERROR_EVENTS;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    @Inject(LOGGER_SERVICE) private readonly logger: ILoggerService,
    @Inject(forwardRef(() => CorrelationService))
    private readonly correlation: CorrelationService,
  ) {
    this.setupErrorBufferMonitoring();
  }

  async trackError(error: Error, config: ErrorConfig): Promise<string> {
    this.logger.startOperation('TRACK_ERROR');

    try {
      const trackedError = await this.createTrackedError(error, config);

      if (!this.isShuttingDown) {
        await this.processError(trackedError);
      }

      this.logger.info(
        'Error tracked successfully',
        this.createErrorContext(trackedError),
      );

      return trackedError.id;
    } catch (err) {
      this.logger.error('Failed to track error', err as Error, {
        metadata: {
          originalError: {
            name: error.name,
            message: error.message,
          },
          config,
        },
      });
      throw err;
    } finally {
      this.logger.endOperation();
    }
  }

  async queryErrors(options: ErrorQueryOptions = {}): Promise<TrackedError[]> {
    return await this.logger.time(
      'Querying errors',
      () => {
        let errors = [...this.errorBuffer];

        if (options.severity) {
          errors = errors.filter((e) => e.severity === options.severity);
        }

        if (options.fromDate) {
          errors = errors.filter((e) => e.timestamp >= options.fromDate!);
        }

        if (options.toDate) {
          errors = errors.filter((e) => e.timestamp <= options.toDate!);
        }

        if (options.tags) {
          errors = errors.filter((e) =>
            Object.entries(options.tags!).every(
              ([key, value]) => e.tags?.[key] === value,
            ),
          );
        }

        if (options.offset) {
          errors = errors.slice(options.offset);
        }

        if (options.limit) {
          errors = errors.slice(0, options.limit);
        }

        return errors;
      },
      {
        metadata: { ...options },
        metrics: { bufferSize: this.errorBuffer.length },
      },
    );
  }

  async getErrorStats(
    timeRange: number = 3600000,
  ): Promise<Record<ErrorSeverity, number>> {
    return this.logger.time(
      'Calculating error statistics',
      async () => {
        const fromDate = new Date(Date.now() - timeRange);
        const errors = await this.queryErrors({ fromDate });

        const stats = errors.reduce(
          (acc, error) => {
            acc[error.severity] = (acc[error.severity] || 0) + 1;
            return acc;
          },
          {} as Record<ErrorSeverity, number>,
        );

        this.logger.debug('Error statistics calculated', {
          metadata: { stats },
          metrics: { timeRangeMs: timeRange },
        });

        return stats;
      },
      { metrics: { timeRangeMs: timeRange } },
    );
  }

  onError(
    severity: ErrorSeverity,
    handler: (error: TrackedError) => void,
  ): () => void {
    const listener = (error: TrackedError) => {
      if (error.severity === severity) {
        this.logger.debug('Error event handler triggered', {
          resources: { errorId: error.id },
          metadata: { severity },
        });
        handler(error);
      }
    };

    this.eventEmitter.on(this.ERROR_EVENTS.ERROR_TRACKED, listener);

    return () => {
      this.eventEmitter.off(this.ERROR_EVENTS.ERROR_TRACKED, listener);
      this.logger.debug('Error event handler removed', {
        metadata: { severity },
      });
    };
  }

  // ... private helper methods remain the same ...

  onApplicationShutdown(): void {
    this.isShuttingDown = true;
    const pendingErrors = this.errorBuffer.length;

    if (pendingErrors > 0) {
      this.logger.warn('Shutdown initiated with pending errors', {
        metrics: {
          pendingErrors,
          oldestError: Date.now() - this.errorBuffer[0].timestamp.getTime(),
          newestError:
            Date.now() -
            this.errorBuffer[pendingErrors - 1].timestamp.getTime(),
        },
        tags: ['shutdown'],
      });
    }
  }

  private async createTrackedError(
    error: Error,
    config: ErrorConfig,
  ): Promise<TrackedError> {
    const correlationContext = this.correlation.getCurrentContext();

    return this.logger.time<TrackedError>(
      'Creating tracked error',
      () => {
        const trackedError: TrackedError = {
          id: this.generateErrorId(),
          name: error.name,
          message: error.message,
          timestamp: new Date(),
          severity: config.severity,
          code: (error as any).code || 'UNKNOWN_ERROR',
          stack: error.stack,
          statusCode: (error as any).statusCode,
          cause: error,
          correlationId: correlationContext.correlationId,
          traceId: correlationContext.traceId,
          spanId: correlationContext.spanId,
          tenantId: correlationContext.tenantId,
          userId: correlationContext.userId,
          environment: correlationContext.environment,
          service: correlationContext.service,
          tags: {
            ...config.tags,
            environment: correlationContext.environment,
            service: correlationContext.service,
          },
          details: {
            ...config.context,
            correlationContext,
            originalError: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
          },
        };

        this.logger.debug(
          'Created tracked error',
          this.createErrorContext(trackedError),
        );

        return trackedError;
      },
      { metadata: { errorName: error.name } },
    );
  }

  private createErrorContext(error: TrackedError): LogContext {
    return {
      resources: {
        errorId: error.id,
      },
      metadata: {
        errorName: error.name,
        severity: error.severity,
        statusCode: error.statusCode,
        code: error.code,
      },
      tags: [
        error.severity.toLowerCase(),
        ...(error.tags ? Object.keys(error.tags) : []),
      ],
    };
  }

  private generateErrorId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).slice(2, 8);
    return `err_${timestamp}_${random}`;
  }

  private setupErrorBufferMonitoring(): void {
    this.eventEmitter.on(
      this.ERROR_EVENTS.ERROR_BUFFER_FULL,
      ({ droppedError, bufferSize }) => {
        this.logger.warn('Error buffer capacity reached', {
          resources: { droppedErrorId: droppedError.id },
          metrics: {
            bufferSize,
            oldestErrorAge: Date.now() - droppedError.timestamp.getTime(),
          },
          tags: ['buffer-full', 'monitoring'],
        });
      },
    );

    this.logger.info('Error buffer monitoring initialized', {
      metrics: { maxBufferSize: this.MAX_BUFFER_SIZE },
      tags: ['startup'],
    });
  }

  private async processError(error: TrackedError): Promise<void> {
    await this.logger.time(
      'Processing error',
      async () => {
        this.addToBuffer(error);
        await this.emitErrorEvents(error);
        this.logError(error);
      },
      this.createErrorContext(error),
    );
  }

  private addToBuffer(error: TrackedError): void {
    this.errorBuffer.push(error);

    if (this.errorBuffer.length >= this.MAX_BUFFER_SIZE) {
      const droppedError = this.errorBuffer.shift()!;
      this.eventEmitter.emit(this.ERROR_EVENTS.ERROR_BUFFER_FULL, {
        droppedError,
        bufferSize: this.MAX_BUFFER_SIZE,
      });

      this.logger.warn('Error buffer full, dropped oldest error', {
        resources: {
          droppedErrorId: droppedError.id,
          newErrorId: error.id,
        },
        metrics: {
          bufferSize: this.MAX_BUFFER_SIZE,
          bufferAge: Date.now() - droppedError.timestamp.getTime(),
        },
        tags: ['buffer-full'],
      });
    }
  }

  private emitErrorEvents(error: TrackedError): void {
    this.eventEmitter.emit(this.ERROR_EVENTS.ERROR_TRACKED, error);

    if (error.severity === ErrorSeverity.CRITICAL) {
      this.logger.warn('Critical error detected', {
        ...this.createErrorContext(error),
        tags: ['critical-alert'],
      });
      this.eventEmitter.emit(this.ERROR_EVENTS.ERROR_CRITICAL, error);
    }

    if (error.severity === ErrorSeverity.HIGH) {
      this.eventEmitter.emit(this.ERROR_EVENTS.ERROR_HIGH, error);
    }
  }

  private logError(error: TrackedError): void {
    const context = this.createErrorContext(error);

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        this.logger.error(`Critical error: ${error.message}`, error, {
          ...context,
          tags: [...(context.tags || []), 'critical'],
        });
        break;
      case ErrorSeverity.HIGH:
        this.logger.error(`High severity error: ${error.message}`, error, {
          ...context,
          tags: [...(context.tags || []), 'high'],
        });
        break;
      case ErrorSeverity.MEDIUM:
        this.logger.warn(error.message, {
          ...context,
          tags: [...(context.tags || []), 'medium'],
        });
        break;
      case ErrorSeverity.LOW:
        this.logger.debug(error.message, {
          ...context,
          tags: [...(context.tags || []), 'low'],
        });
        break;
    }
  }
}
