import { Global, Module, forwardRef } from '@nestjs/common';
import { ErrorTrackingService } from './error-tracking.service';
import { LoggerModule } from '../logger/logger.module';
import { CorrelationModule } from '../correlation/correlation.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ERROR_TRACKING_SERVICE } from './error-tracking.interface';

@Global()
@Module({
  imports: [
    forwardRef(() => LoggerModule),
    forwardRef(() => CorrelationModule),
    EventEmitterModule.forRoot(),
  ],
  providers: [
    {
      provide: ERROR_TRACKING_SERVICE,
      useClass: ErrorTrackingService,
    },
  ],
  exports: [ERROR_TRACKING_SERVICE],
})
export class ErrorTrackingModule {}
