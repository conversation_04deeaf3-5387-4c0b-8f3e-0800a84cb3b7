export enum FilterOperator {
  EQ = 'eq', // Equal to
  NEQ = 'neq', // Not equal to
  GT = 'gt', // Greater than
  GTE = 'gte', // Greater than or equal to
  LT = 'lt', // Less than
  LTE = 'lte', // Less than or equal to
  LIKE = 'like', // Contains (case-sensitive)
  ILIKE = 'ilike', // Contains (case-insensitive)
  IN = 'in', // In a list of values (comma-separated)
  NOT_IN = 'notIn', // Not in a list of values (comma-separated)
  IS_NULL = 'isNull', // Is null
  IS_NOT_NULL = 'isNotNull', // Is not null
  BETWEEN = 'between', // Between two values (inclusive, comma-separated)
  STARTS_WITH = 'startsWith', // Starts with
  ENDS_WITH = 'endsWith', // Ends with
  CONTAINS = 'contains', // Contains (similar to ILIKE but more intuitive)
  NOT_CONTAINS = 'notContains', // Does not contain
}

export type FilterValue = string | number | boolean | Date | Array<any>;

export interface FilterCondition {
  [field: string]: {
    [operator in FilterOperator]?: FilterValue;
  };
}

export interface LogicalOperator {
  AND?: (FilterCondition | LogicalOperator)[];
  OR?: (FilterCondition | LogicalOperator)[];
}

/**
 * Type guard to check if an object is a LogicalOperator
 */
export const isLogicalOperator = (obj: any): obj is LogicalOperator => {
  return obj && (Array.isArray(obj.AND) || Array.isArray(obj.OR));
};

/**
 * Map URL friendly operator strings to FilterOperator enum
 */
export const operatorMapping: Record<string, FilterOperator> = {
  eq: FilterOperator.EQ,
  neq: FilterOperator.NEQ,
  gt: FilterOperator.GT,
  gte: FilterOperator.GTE,
  lt: FilterOperator.LT,
  lte: FilterOperator.LTE,
  like: FilterOperator.LIKE,
  ilike: FilterOperator.ILIKE,
  in: FilterOperator.IN,
  notIn: FilterOperator.NOT_IN,
  isNull: FilterOperator.IS_NULL,
  isNotNull: FilterOperator.IS_NOT_NULL,
  between: FilterOperator.BETWEEN,
  startsWith: FilterOperator.STARTS_WITH,
  endsWith: FilterOperator.ENDS_WITH,
  contains: FilterOperator.CONTAINS,
  notContains: FilterOperator.NOT_CONTAINS,
};
