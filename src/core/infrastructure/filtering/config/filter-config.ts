import { FilterOperator, FilterValue } from '../types/filter.types';

export interface FilterableField {
  operators?: FilterOperator[];
  searchable?: boolean;
  sortable?: boolean;
  validate?: (value: FilterValue) => boolean | Promise<boolean>;
  validationMessage?: string;
  relation?: string;
  relationField?: string;
  type?: 'string' | 'number' | 'boolean' | 'date';
  transform?: (value: string) => any;
}

// Add this type for transformed values
export type TransformedValue = string | number | boolean | Date;

export interface FilterConfig {
  fields: Record<string, FilterableField>;
  defaultSort?: { field: string; direction: 'ASC' | 'DESC' };
  maxTake?: number;
  defaultTake?: number;
  relations: Record<string, string>; // Make relations required, not optional
}

/**
 * Builder class for creating filter configurations
 */
export class FilterConfigBuilder {
  private config: FilterConfig = {
    fields: {},
    maxTake: 100,
    defaultTake: 10,
    relations: {}, // Initialize with empty object
  };

  /**
   * Add a filterable field to the configuration
   */
  addField(fieldName: string, options: Partial<FilterableField> = {}): this {
    this.config.fields[fieldName] = options;
    return this;
  }

  /**
   * Set the default sort field and direction
   */
  setDefaultSort(field: string, direction: 'ASC' | 'DESC'): this {
    this.config.defaultSort = { field, direction };
    return this;
  }

  /**
   * Set the maximum number of items that can be taken
   */
  setMaxTake(maxTake: number): this {
    this.config.maxTake = maxTake;
    return this;
  }

  /**
   * Set the default number of items to take
   */
  setDefaultTake(defaultTake: number): this {
    this.config.defaultTake = defaultTake;
    return this;
  }

  /**
   * Mark fields as searchable
   */
  setSearchableFields(fields: string[]): this {
    for (const field of fields) {
      if (this.config.fields[field]) {
        this.config.fields[field].searchable = true;
      } else {
        this.config.fields[field] = { searchable: true };
      }
    }
    return this;
  }

  /**
   * Mark fields as sortable
   */
  setSortableFields(fields: string[]): this {
    for (const field of fields) {
      if (this.config.fields[field]) {
        this.config.fields[field].sortable = true;
      } else {
        this.config.fields[field] = { sortable: true };
      }
    }
    return this;
  }

  /**
   * Set relations mapping for the filter configuration
   * @param relations Record of relation aliases and their paths
   */
  setRelations(relations: Record<string, string>): this {
    this.config.relations = relations;
    return this;
  }

  /**
   * Add a single relation to the filter configuration
   * @param alias Relation alias
   * @param path Relation path
   */
  addRelation(alias: string, path: string): this {
    this.config.relations[alias] = path;
    return this;
  }

  /**
   * Build and return the final filter configuration
   */
  build(): FilterConfig {
    this.validateConfig();
    return { ...this.config };
  }

  /**
   * Validate the filter configuration
   */
  private validateConfig(): void {
    if (!this.config.fields || Object.keys(this.config.fields).length === 0) {
      throw new Error('Filter config must define at least one field');
    }

    if (this.config.defaultSort) {
      const field = this.config.fields[this.config.defaultSort.field];
      if (!field?.sortable) {
        throw new Error(
          `Default sort field '${this.config.defaultSort.field}' must be marked as sortable`,
        );
      }
    }

    if (this.config.maxTake && this.config.maxTake < 1) {
      throw new Error('maxTake must be greater than 0');
    }

    if (this.config.defaultTake && this.config.defaultTake < 1) {
      throw new Error('defaultTake must be greater than 0');
    }

    if (
      this.config.defaultTake &&
      this.config.maxTake &&
      this.config.defaultTake > this.config.maxTake
    ) {
      throw new Error('defaultTake cannot be greater than maxTake');
    }

    // Validate relations
    const relationFields = Object.keys(this.config.fields).filter(
      (field) => this.config.fields[field].relation,
    );

    // Check if all relation fields have corresponding relation paths
    relationFields.forEach((field) => {
      const relation = this.config.fields[field].relation;
      if (relation && !(relation in this.config.relations)) {
        throw new Error(
          `Relation '${relation}' used in field '${field}' must be defined in relations config`,
        );
      }
    });
  }
}
