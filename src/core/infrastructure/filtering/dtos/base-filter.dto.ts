import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { FilterCondition, LogicalOperator } from '../types/filter.types';
import { PageParamsRequest } from '../../../../utils/page-params-request';
import { SortDirection } from '../../../../utils/types/sort-direction.type';

export class BaseFilterDto extends PageParamsRequest {
  @ApiPropertyOptional({ example: 'createdAt' })
  @IsString()
  @IsOptional()
  sortField: string;

  @ApiPropertyOptional({ enum: SortDirection })
  @IsEnum(SortDirection)
  @IsOptional()
  sortDirection: string;

  @ApiPropertyOptional({ example: 'Mazda' })
  @IsString()
  @IsOptional()
  searchTerm: string;

  // This property is used internally by the filter parser
  // but not exposed to API consumers directly :)
  where?: FilterCondition | LogicalOperator;
}
