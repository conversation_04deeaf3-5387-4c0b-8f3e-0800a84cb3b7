import { ModuleOperationsBuilder } from './builders/module-operation.builder';
import {
  BatchOperationResult,
  OperationMetadata,
} from './types/operation.types';

export const MONITORING_SERVICE = Symbol('MONITORING_SERVICE');

export interface IMonitoringService {
  forModule(moduleName: string): ModuleOperationsBuilder;

  executeOperation<T>(
    metadata: OperationMetadata,
    operation: () => Promise<T>,
  ): Promise<T>;

  processBatch<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    options?: {
      concurrency?: number;
      timeoutMs?: number;
      stopOnError?: boolean;
    },
  ): Promise<BatchOperationResult<R>>;

  withRetry<T>(
    operation: () => Promise<T>,
    config: {
      maxAttempts: number;
      delayMs: number;
      useBackoff?: boolean;
      shouldRetry?: (error: Error) => boolean;
    },
  ): Promise<T>;
}
