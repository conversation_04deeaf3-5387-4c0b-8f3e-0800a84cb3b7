import {
  ErrorConfig,
  LoggingConfig,
  OperationMetadata,
  TransactionConfig,
} from '@core/infrastructure/monitoring/types/operation.types';
import { ErrorSeverity } from '@core/infrastructure/logger/types/severity.types';
import { IMonitoringService } from '@core/infrastructure/monitoring/monitoring.interface';

/**
 * @class OperationConfigBuilder
 * @description Builder for constructing operation configurations with a fluent API
 *
 * @typeParam T - Return type of the operation being configured
 */
export class OperationBuilder<T = unknown> {
  private readonly config: Partial<OperationMetadata> = {};

  constructor(
    private readonly moduleName: string,
    private readonly monitoringService: IMonitoringService,
    private readonly defaultConfig?: Partial<OperationMetadata>,
  ) {
    this.config.moduleName = moduleName;

    if (defaultConfig) {
      this.config = {
        ...this.config,
        ...defaultConfig,
        moduleName: this.moduleName,
      };
    }
  }

  /**
   * Sets the operation type and initializes configuration
   *
   * @param type - Unique identifier for the operation type
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * const builder = new OperationConfigBuilder('users')
   *   .forOperation('CREATE_USER');
   * ```
   */
  forOperation(type: string): this {
    this.config.operationType = type;
    return this;
  }

  /**
   * Sets the specific action being performed
   *
   * @param action - Name of the action
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.withAction('create');
   * ```
   */
  withAction(action: string): this {
    this.config.actionName = action;
    return this;
  }

  /**
   * Configures error handling settings
   *
   * @param config - Error handling configuration
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.withErrorConfig({
   *   severity: ErrorSeverity.HIGH,
   *   reportToErrorTracking: true,
   *   tags: { module: 'users' }
   * });
   * ```
   */
  withErrorConfig(config: Partial<ErrorConfig>): this {
    this.config.errorConfig = {
      severity: ErrorSeverity.MEDIUM, // Default severity
      ...(this.config.errorConfig || {}),
      ...config,
    } as ErrorConfig;
    return this;
  }

  /**
   * Sets error severity level
   *
   * @param severity - Error severity level
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.withSeverity(ErrorSeverity.HIGH);
   * ```
   */
  withSeverity(severity: ErrorSeverity): this {
    return this.withErrorConfig({ severity });
  }

  /**
   * Adds tags for categorization
   *
   * @param tags - Key-value pairs for categorization
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.withTags({
   *   environment: 'production',
   *   region: 'eu-west-1'
   * });
   * ```
   */
  withTags(tags: Record<string, string>): this {
    this.config.errorConfig = {
      severity: this.config.errorConfig?.severity || ErrorSeverity.MEDIUM,
      ...(this.config.errorConfig || {}),
      tags: {
        ...(this.config.errorConfig?.tags || {}),
        ...tags,
      },
    };
    return this;
  }

  /**
   * Configures logging behavior
   *
   * @param config - Logging configuration
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.withLogging({
   *   successLevel: LogLevel.INFO,
   *   errorLevel: LogLevel.ERROR,
   *   context: { batchId: 'batch_123' }
   * });
   * ```
   */
  withLogging(config: Partial<LoggingConfig>): this {
    this.config.logging = {
      ...(this.config.logging || {}),
      ...config,
    };
    return this;
  }

  /**
   * Adds trace attributes for monitoring
   *
   * @param attributes - Key-value pairs for tracing
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.withTrace({
   *   'user.id': userId,
   *   'request.source': 'api',
   *   'batch.size': items.length
   * });
   * ```
   */
  withTrace(attributes: Record<string, any>): this {
    this.config.traceAttributes = {
      ...(this.config.traceAttributes || {}),
      ...attributes,
    };
    return this;
  }

  /**
   * Enables custom business transaction tracing
   *
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.useCustomTracer();
   * ```
   */
  useCustomTracer(): this {
    this.config.useCustomTracer = true;
    return this;
  }

  /**
   * Configures transaction behavior
   *
   * @param config - Transaction configuration
   * @returns Builder instance for chaining
   *
   * @example
   * ```typescript
   * builder.withTransaction({
   *   enabled: true,
   *   timeoutMs: 5000
   * });
   * ```
   */
  withTransaction(config: Partial<TransactionConfig>): this {
    this.config.transaction = {
      enabled: true,
      ...(this.config.transaction || {}),
      ...config,
    } as TransactionConfig;
    return this;
  }

  /**
   * Builds the final operation metadata
   * @internal
   */
  build(): OperationMetadata {
    const finalConfig = {
      ...(this.defaultConfig || {}),
      ...this.config,
    };

    // Ensure required fields
    if (!finalConfig.operationType) {
      throw new Error('Operation type must be specified');
    }
    if (!finalConfig.moduleName) {
      throw new Error('Module name must be specified');
    }
    if (!finalConfig.actionName) {
      throw new Error('Action name must be specified');
    }

    return finalConfig as OperationMetadata;
  }

  /**
   * Executes the operation with current configuration
   *
   * @param operation - Operation to execute
   * @returns Promise resolving to operation result
   *
   * @example
   * ```typescript
   * const result = await builder
   *   .forOperation('CREATE_USER')
   *   .withSeverity(ErrorSeverity.HIGH)
   *   .execute(async () => {
   *     return userRepository.create(data);
   *   });
   * ```
   */
  async execute(operation: () => Promise<T>): Promise<T> {
    if (!this.config.operationType) {
      throw new Error('Operation type must be specified using forOperation()');
    }

    if (!this.config.actionName) {
      // Default action name to operation type if not set
      this.config.actionName = this.config.operationType.toLowerCase();
    }

    return this.monitoringService.executeOperation(
      this.config as OperationMetadata,
      operation,
    );
  }
}
