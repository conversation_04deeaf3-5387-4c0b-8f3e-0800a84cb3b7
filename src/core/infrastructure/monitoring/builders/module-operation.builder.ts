import {
  LoggingConfig,
  OperationMetadata,
} from '@core/infrastructure/monitoring/types/operation.types';
import { ErrorSeverity } from '@core/infrastructure/logger/types/severity.types';
import { LogLevel } from '@core/infrastructure/logger/types/log-level.types';
import { OperationBuilder } from '@core/infrastructure/monitoring/builders/operation.builder';
import { IMonitoringService } from '@core/infrastructure/monitoring/monitoring.interface';

/**
 * @class ModuleOperationsBuilder
 * @description Builder for creating module-specific operation configurations
 */
export class ModuleOperationsBuilder {
  private readonly defaultConfig: Partial<OperationMetadata> = {};

  constructor(
    private readonly moduleName: string,
    private readonly monitoringService: IMonitoringService,
  ) {
    this.defaultConfig = {
      moduleName: this.moduleName,
      errorConfig: {
        severity: ErrorSeverity.MEDIUM,
        tags: { module: this.moduleName },
      },
      logging: {
        successLevel: LogLevel.INFO,
        errorLevel: LogLevel.ERROR,
      },
    };
  }

  /**
   * Sets default severity for all operations
   */
  withDefaultSeverity(severity: ErrorSeverity): this {
    if (!this.defaultConfig.errorConfig) {
      this.defaultConfig.errorConfig = {};
    }
    this.defaultConfig.errorConfig.severity = severity;
    return this;
  }

  /**
   * Sets default tags for all operations
   */
  withDefaultTags(tags: Record<string, string>): this {
    if (!this.defaultConfig.errorConfig) {
      this.defaultConfig.errorConfig = {};
    }
    this.defaultConfig.errorConfig.tags = {
      ...(this.defaultConfig.errorConfig.tags || {}),
      ...tags,
    };
    return this;
  }

  /**
   * Sets default logging configuration
   */
  withDefaultLogging(config: Partial<LoggingConfig>): this {
    this.defaultConfig.logging = {
      ...(this.defaultConfig.logging || {}),
      ...config,
    };
    return this;
  }

  /**
   * Creates an operation builder for this module
   */
  operation<T>(): OperationBuilder<T> {
    return new OperationBuilder<T>(
      this.moduleName,
      this.monitoringService,
      this.defaultConfig,
    );
  }
}
