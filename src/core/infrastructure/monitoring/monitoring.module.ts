import { Module } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';
import { LoggerModule } from '../logger/logger.module';
import { TracingModule } from '../tracing/tracing.module';
import { ErrorTrackingModule } from '@core/infrastructure/errorTracking/error-tracking.module';
import { MONITORING_SERVICE } from './monitoring.interface';

@Module({
  imports: [LoggerModule, TracingModule, ErrorTrackingModule],
  providers: [
    {
      provide: MONITORING_SERVICE,
      useClass: MonitoringService,
    },
  ],
  exports: [MONITORING_SERVICE],
})
export class MonitoringModule {}
