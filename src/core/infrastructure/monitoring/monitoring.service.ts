import { Inject, Injectable } from '@nestjs/common';
import { ModuleOperationsBuilder } from './builders/module-operation.builder';
import {
  BatchOperationResult,
  OperationCriticality,
  OperationMetadata,
  OperationTimeouts,
} from '@core/infrastructure/monitoring/types/operation.types';
import { ErrorSeverity } from '@core/infrastructure/logger/types/severity.types';
import { LogLevel } from '@core/infrastructure/logger/types/log-level.types';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';
import {
  ITracerService,
  TRACER_SERVICE,
} from '@core/infrastructure/tracing/tracer.interface';
import {
  ERROR_TRACKING_SERVICE,
  IErrorTrackingService,
} from '@core/infrastructure/errorTracking/error-tracking.interface';
import chalk from 'chalk';

@Injectable()
export class MonitoringService {
  constructor(
    @Inject(LOGGER_SERVICE) private readonly logger: ILoggerService,
    @Inject(TRACER_SERVICE) private readonly tracer: ITracerService,
    @Inject(ERROR_TRACKING_SERVICE)
    private readonly errorTracking: IErrorTrackingService,
  ) {}

  forModule(moduleName: string): ModuleOperationsBuilder {
    return new ModuleOperationsBuilder(moduleName, this);
  }

  async executeOperation<T>(
    metadata: OperationMetadata,
    operation: () => Promise<T>,
  ): Promise<T> {
    const operationId = this.generateOperationId(metadata);
    const startTime = process.hrtime();

    const context = this.createContext(metadata, operationId);
    const attributes = this.createAttributes(metadata, operationId);

    try {
      this.logStart(metadata, context);
      const result = await this.executeTraced(metadata, operation, attributes);
      this.logSuccess(metadata, context, this.getDuration(startTime));
      return result;
    } catch (error) {
      const duration = this.getDuration(startTime);
      await this.handleError(error, metadata, context, duration);
      throw error;
    }
  }

  async processBatch<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    options: {
      concurrency?: number;
      timeoutMs?: number;
      stopOnError?: boolean;
    } = {},
  ): Promise<BatchOperationResult<R>> {
    const startTime = Date.now();
    const results: BatchOperationResult<R> = {
      successful: [],
      failed: [],
      stats: {
        total: items.length,
        succeeded: 0,
        failed: 0,
        durationMs: 0,
      },
    };

    const concurrency = options.concurrency || 5;
    let currentIndex = 0;

    async function processNext(): Promise<void> {
      if (currentIndex >= items.length) return;

      const item = items[currentIndex++];
      try {
        const result = await processor(item);
        results.successful.push(result);
        results.stats.succeeded++;
      } catch (error) {
        results.failed.push({ item, error });
        results.stats.failed++;

        if (options.stopOnError) {
          throw error;
        }
      }

      return processNext();
    }

    try {
      await Promise.all(
        Array.from({ length: concurrency }).map(() => processNext()),
      );
    } finally {
      results.stats.durationMs = Date.now() - startTime;
    }

    return results;
  }

  async withRetry<T>(
    operation: () => Promise<T>,
    config: {
      maxAttempts: number;
      delayMs: number;
      useBackoff?: boolean;
      shouldRetry?: (error: Error) => boolean;
    },
  ): Promise<T> {
    let lastError: Error;
    let attempt = 1;

    while (attempt <= config.maxAttempts) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (
          attempt === config.maxAttempts ||
          (config.shouldRetry && !config.shouldRetry(error))
        ) {
          throw error;
        }

        const delay = config.useBackoff
          ? config.delayMs * Math.pow(2, attempt - 1)
          : config.delayMs;

        await new Promise((resolve) => setTimeout(resolve, delay));
        attempt++;
      }
    }

    throw lastError!;
  }

  private async executeTraced<T>(
    metadata: OperationMetadata,
    operation: () => Promise<T>,
    attributes: Record<string, any>,
  ): Promise<T> {
    const traceName = `${metadata.moduleName}.${metadata.actionName}`;

    if (metadata.transaction?.enabled) {
      return this.executeWithTransaction(
        traceName,
        operation,
        attributes,
        metadata,
      );
    }

    return metadata.useCustomTracer
      ? this.tracer.customTrace(traceName, operation, attributes)
      : this.tracer.trace(traceName, operation, attributes);
  }

  private async executeWithTransaction<T>(
    traceName: string,
    operation: () => Promise<T>,
    attributes: Record<string, any>,
    metadata: OperationMetadata,
  ): Promise<T> {
    const timeoutMs = metadata.transaction?.timeoutMs;

    const wrappedOperation = async () => {
      try {
        const result = await operation();
        return result;
      } catch (error) {
        throw error;
      }
    };

    if (timeoutMs) {
      return Promise.race([
        metadata.useCustomTracer
          ? this.tracer.customTrace(traceName, wrappedOperation, attributes)
          : this.tracer.trace(traceName, wrappedOperation, attributes),
        new Promise<T>((_, reject) =>
          setTimeout(
            () => reject(new Error(`Operation timed out after ${timeoutMs}ms`)),
            timeoutMs,
          ),
        ),
      ]);
    }

    return metadata.useCustomTracer
      ? this.tracer.customTrace(traceName, wrappedOperation, attributes)
      : this.tracer.trace(traceName, wrappedOperation, attributes);
  }

  private async handleError(
    error: Error,
    metadata: OperationMetadata,
    context: Record<string, any>,
    duration: number,
  ): Promise<void> {
    const errorId = await this.errorTracking.trackError(error, {
      severity: metadata.errorConfig?.severity || ErrorSeverity.MEDIUM,
      context: {
        ...context,
        duration,
        operation: metadata.operationType,
      },
      tags: {
        module: metadata.moduleName,
        action: metadata.actionName,
        ...(metadata.errorConfig?.tags || {}),
      },
    });

    this.logError(metadata, error, {
      ...context,
      errorId,
      duration,
    });
  }

  private createContext(
    metadata: OperationMetadata,
    operationId: string,
  ): Record<string, any> {
    return {
      operationId,
      type: metadata.operationType,
      module: metadata.moduleName,
      action: metadata.actionName,
      timestamp: new Date().toISOString(),
      ...metadata.logging?.context,
    };
  }

  private createAttributes(
    metadata: OperationMetadata,
    operationId: string,
  ): Record<string, any> {
    return {
      'operation.id': operationId,
      'operation.type': metadata.operationType,
      'operation.module': metadata.moduleName,
      'operation.action': metadata.actionName,
      ...metadata.traceAttributes,
    };
  }

  private logStart(
    metadata: OperationMetadata,
    context: Record<string, any>,
  ): void {
    const level = metadata.logging?.successLevel || LogLevel.DEBUG;
    const message = `Starting operation: ${metadata.operationType}`;
    switch (level) {
      case LogLevel.DEBUG:
        this.logger.debug(message, context);
        break;
      case LogLevel.INFO:
        this.logger.info(message, context);
        break;
      case LogLevel.WARN:
        this.logger.warn(message, context);
        break;
      case LogLevel.ERROR:
        this.logger.error(message, new Error('Operation start error'), context);
        break;
    }
  }

  private logSuccess(
    metadata: OperationMetadata,
    context: Record<string, any>,
    duration: number,
  ): void {
    const level = metadata.logging?.successLevel || LogLevel.DEBUG;
    const message = `Operation completed: ${metadata.operationType}`;
    switch (level) {
      case LogLevel.DEBUG:
        this.logger.debug(message, { ...context, duration });
        break;
      case LogLevel.INFO:
        this.logger.info(message, { ...context, duration });
        break;
      case LogLevel.WARN:
        this.logger.warn(message, { ...context, duration });
        break;
      case LogLevel.ERROR:
        this.logger.error(message, new Error('Operation success error'), {
          ...context,
          duration,
        });
        break;
    }
  }
  private logError(
    metadata: OperationMetadata,
    error: Error,
    context: Record<string, any>,
  ): void {
    const level = metadata.logging?.errorLevel || LogLevel.ERROR;
    const message = `Operation failed: ${metadata.operationType}`;

    // Helper function to format JSON-like context for better readability
    const formattedContext = JSON.stringify(context, null, 2); // Indent JSON for readability
    const formattedError = JSON.stringify(
      {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      null,
      2,
    );

    const logMessage = `${chalk.red.bold(message)}\n${chalk.yellow(
      'Context:',
    )}\n${chalk.cyan(formattedContext)}\n${chalk.red('Error:')}\n${chalk.gray(
      formattedError,
    )}`;

    switch (level) {
      case LogLevel.DEBUG:
        this.logger.debug(logMessage);
        break;
      case LogLevel.INFO:
        this.logger.info(logMessage);
        break;
      case LogLevel.WARN:
        this.logger.warn(logMessage);
        break;
      case LogLevel.ERROR:
        this.logger.error(message, error, context); // Log raw message separately if required
        break;
    }
  }

  private generateOperationId(metadata: OperationMetadata): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).slice(2, 6);
    return `${metadata.moduleName}_${metadata.actionName}_${timestamp}_${random}`;
  }

  private getDuration(startTime: [number, number]): number {
    const diff = process.hrtime(startTime);
    return (diff[0] * 1e9 + diff[1]) / 1e6; // Convert to milliseconds
  }

  getOperationCriticality(level: keyof typeof OperationCriticality) {
    return OperationCriticality[level];
  }

  getOperationTimeout(type: keyof typeof OperationTimeouts) {
    return OperationTimeouts[type];
  }
}
