import { LogLevel } from '@core/infrastructure/logger/types/log-level.types';
import { ErrorSeverity } from '@core/infrastructure/logger/types/severity.types';

export interface OperationResult<T> {
  data: T;
  metadata: {
    durationMs: number;
    operationId: string;
    startTime: Date;
    endTime: Date;
  };
}

export interface BatchOperationResult<T> {
  successful: T[];
  failed: Array<{
    item: any;
    error: Error;
  }>;
  stats: {
    total: number;
    succeeded: number;
    failed: number;
    durationMs: number;
  };
}

export interface OperationMetadata {
  operationType: string;
  moduleName: string;
  actionName: string;
  useCustomTracer?: boolean;
  traceAttributes?: Record<string, any>;
  logging?: Partial<LoggingConfig>;
  errorConfig?: Partial<ErrorConfig>;
  transaction?: Partial<TransactionConfig>;
}

export interface ErrorConfig {
  severity: ErrorSeverity;
  reportToErrorTracking?: boolean;
  context?: Record<string, any>;
  tags?: Record<string, string>;
}

export interface LoggingConfig {
  successLevel: LogLevel;
  errorLevel: LogLevel;
  context?: Record<string, any>;
}

export interface TransactionConfig {
  enabled: boolean;
  timeoutMs?: number;
}

export const OperationTimeouts = {
  DEFAULT: 30000,
  QUICK: 5000,
  LONG_RUNNING: 120000,
  BATCH: 300000,
} as const;

export const OperationCriticality = {
  LOW: {
    severity: ErrorSeverity.LOW,
    timeout: OperationTimeouts.QUICK,
    logging: {
      successLevel: LogLevel.DEBUG,
      errorLevel: LogLevel.WARN,
    },
  },
  MEDIUM: {
    severity: ErrorSeverity.MEDIUM,
    timeout: OperationTimeouts.DEFAULT,
    logging: {
      successLevel: LogLevel.INFO,
      errorLevel: LogLevel.ERROR,
    },
  },
  HIGH: {
    severity: ErrorSeverity.HIGH,
    timeout: OperationTimeouts.DEFAULT,
    logging: {
      successLevel: LogLevel.INFO,
      errorLevel: LogLevel.ERROR,
    },
  },
  CRITICAL: {
    severity: ErrorSeverity.CRITICAL,
    timeout: OperationTimeouts.LONG_RUNNING,
    logging: {
      successLevel: LogLevel.INFO,
      errorLevel: LogLevel.ERROR,
    },
  },
} as const;
