import { forwardRef, Inject, Injectable, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { context as otelContext, trace, Span } from '@opentelemetry/api';
import { AsyncLocalStorage } from 'async_hooks';
import { v4 as uuidv4 } from 'uuid';
import {
  CoreCorrelationContext,
  CorrelationContext,
} from '@core/infrastructure/correlation/types/correlation.types';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';

export const correlationStorage = new AsyncLocalStorage<CorrelationContext>();

/**
 * Service responsible for managing correlation context across the application
 */
@Injectable({ scope: Scope.REQUEST })
export class CorrelationService {
  private readonly environment: string;
  private readonly serviceName: string;

  constructor(
    private readonly config: ConfigService,
    @Inject(forwardRef(() => LOGGER_SERVICE))
    private readonly logger: ILoggerService,
  ) {
    this.environment = this.config.get<string>('NODE_ENV', 'development');
    this.serviceName = this.config.get<string>('APP_NAME', 'unknown-service');
  }

  /**
   * Gets current correlation context or creates a new one
   */
  getCurrentContext(): CorrelationContext {
    const existingContext = correlationStorage.getStore();
    if (existingContext) {
      return existingContext;
    }

    const activeSpan = trace.getSpan(otelContext.active());
    const context = this.createContextFromSpan(activeSpan);

    return {
      ...context,
      environment: this.environment,
      service: this.serviceName,
    };
  }

  /**
   * Creates new correlation context
   */
  createContext(options?: Partial<CorrelationContext>): CorrelationContext {
    const span = trace.getSpan(otelContext.active());
    const baseContext = this.createContextFromSpan(span);

    const context: CorrelationContext = {
      ...baseContext,
      environment: this.environment,
      service: this.serviceName,
      ...options,
    };

    this.logger.debug('Created correlation context', {
      metadata: { context },
      tags: ['correlation'],
    });

    return context;
  }

  /**
   * Runs callback within correlation context
   */
  async withContext<T>(
    context: CorrelationContext,
    callback: () => Promise<T>,
  ): Promise<T> {
    return correlationStorage.run(context, callback);
  }

  /**
   * Creates correlation headers for outgoing requests
   */
  getCorrelationHeaders(): Record<string, string> {
    const context = this.getCurrentContext();

    return {
      'x-correlation-id': context.correlationId,
      'x-trace-id': context.traceId,
      'x-span-id': context.spanId,
      'x-request-id': context.requestId || '',
      'x-tenant-id': context.tenantId || '',
      'x-source': context.source || '',
    };
  }

  /**
   * Creates context from OpenTelemetry span
   * @internal
   */
  private createContextFromSpan(span?: Span): CoreCorrelationContext {
    if (!span) {
      return {
        correlationId: uuidv4(),
        traceId: uuidv4().replace(/-/g, ''),
        spanId: this.generateSpanId(),
      };
    }

    const spanContext = span.spanContext();

    return {
      correlationId: uuidv4(),
      traceId: spanContext.traceId,
      spanId: spanContext.spanId,
      // Get parent span ID from the span's parent context if available
      parentSpanId: this.getParentSpanId(span),
    };
  }

  /**
   * Gets parent span ID from a span
   * @internal
   */
  private getParentSpanId(span: Span): string | undefined {
    // Parent span ID is not directly accessible in OpenTelemetry
    // You might need to use context or span attributes
    const ctx = span.spanContext();
    // This is implementation specific - adjust based on your OpenTelemetry setup
    return (ctx as any).parentSpanId || undefined;
  }

  /**
   * Generates span ID in OpenTelemetry format
   * @internal
   */
  private generateSpanId(): string {
    return uuidv4().replace(/-/g, '').slice(0, 16);
  }
}
