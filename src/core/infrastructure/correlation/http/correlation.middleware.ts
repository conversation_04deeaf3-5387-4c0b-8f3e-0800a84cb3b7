import { Injectable, NestMiddleware, Inject } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CorrelationService } from '../correlation.service';
import { v4 as uuidv4 } from 'uuid';
import { CorrelationContext } from '../types/correlation.types';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';

export interface CorrelatedRequest extends Request {
  correlationContext?: CorrelationContext;
}

@Injectable()
export class CorrelationMiddleware implements NestMiddleware {
  constructor(
    private readonly correlationService: CorrelationService,
    @Inject(LOGGER_SERVICE) private readonly logger: ILoggerService,
  ) {}

  async use(req: CorrelatedRequest, res: Response, next: NextFunction) {
    try {
      const context = this.createContextFromRequest(req);
      this.setCorrelationHeaders(res, context);
      req.correlationContext = context;

      await this.correlationService.withContext(context, (): any => {
        this.logger.debug('Request started', {
          metadata: {
            method: req.method,
            path: req.path,
            query: req.query,
          },
          tags: ['http', 'request-start'],
        });

        next();
      });
    } catch (error) {
      this.logger.error(
        'Failed to create correlation context',
        error as Error,
        {
          metadata: {
            path: req.path,
            method: req.method,
          },
        },
      );
      next(error);
    }
  }

  private createContextFromRequest(req: Request): CorrelationContext {
    return this.correlationService.createContext({
      correlationId: (req.headers['x-correlation-id'] as string) || uuidv4(),
      requestId: req.headers['x-request-id'] as string,
      sessionId: req.headers['x-session-id'] as string,
      tenantId: req.headers['x-tenant-id'] as string,
      userId: (req as any).user?.id,
      source: 'http',
      attributes: {
        httpMethod: req.method,
        httpPath: req.path,
        userAgent: req.headers['user-agent'],
        clientIp: this.getClientIp(req),
      },
    });
  }

  private setCorrelationHeaders(
    res: Response,
    context: CorrelationContext,
  ): void {
    const headers = {
      'x-correlation-id': context.correlationId,
      'x-trace-id': context.traceId,
      'x-request-id': context.requestId || '',
    };

    Object.entries(headers).forEach(([key, value]) => {
      if (value) {
        res.setHeader(key, value);
      }
    });
  }

  private getClientIp(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      req.socket.remoteAddress ||
      ''
    );
  }
}
