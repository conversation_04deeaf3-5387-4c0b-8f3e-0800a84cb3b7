import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Inject,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CorrelationService } from '../correlation.service';
import { CorrelatedRequest } from './correlation.middleware';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';

@Injectable()
export class CorrelationInterceptor implements NestInterceptor {
  constructor(
    private readonly correlationService: CorrelationService,
    @Inject(LOGGER_SERVICE) private readonly logger: ILoggerService,
  ) {
    if (!logger) {
      throw new Error('Logger service not provided to CorrelationInterceptor');
    }
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const httpContext = context.switchToHttp();
    const request = httpContext.getRequest<CorrelatedRequest>();

    const correlationContext = request.correlationContext;

    if (!correlationContext) {
      this.logger.warn('No correlation context found in request', {
        metadata: {
          path: request.path,
          method: request.method,
        },
        tags: ['correlation', 'warning'],
      });
    }

    return next.handle().pipe(
      tap({
        next: () => {
          const duration = Date.now() - startTime;
          this.logger.info('Request completed successfully', {
            metadata: {
              path: request.path,
              method: request.method,
              duration,
              correlationId: correlationContext?.correlationId,
            },
            metrics: {
              requestDurationMs: duration,
            },
            tags: ['http', 'request-end', 'success'],
          });
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          this.logger.error('Request failed', error, {
            metadata: {
              path: request.path,
              method: request.method,
              duration,
              correlationId: correlationContext?.correlationId,
            },
            metrics: {
              requestDurationMs: duration,
            },
            tags: ['http', 'request-end', 'error'],
          });
        },
      }),
    );
  }
}
