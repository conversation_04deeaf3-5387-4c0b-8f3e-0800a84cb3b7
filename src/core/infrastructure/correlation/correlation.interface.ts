import { CorrelationContext } from './types/correlation.types';

export const CORRELATION_SERVICE = Symbol('CORRELATION_SERVICE');

export interface ICorrelationService {
  /**
   * Gets current correlation context or creates a new one
   */
  getCurrentContext(): CorrelationContext;

  /**
   * Creates new correlation context
   */
  createContext(options?: Partial<CorrelationContext>): CorrelationContext;

  /**
   * Runs callback within correlation context
   */
  withContext<T>(
    context: CorrelationContext,
    callback: () => Promise<T>,
  ): Promise<T>;

  /**
   * Creates correlation headers for outgoing requests
   */
  getCorrelationHeaders(): Record<string, string>;

  /**
   * Creates context from OpenTelemetry span
   */
  getErrorContext?(): Record<string, unknown>;
}
