import {
  Global,
  MiddlewareConsumer,
  Module,
  NestModule,
  forwardRef,
  Scope,
} from '@nestjs/common';
import { CorrelationService } from './correlation.service';
import { ConfigModule } from '@nestjs/config';
import { CorrelationMiddleware } from './http/correlation.middleware';
import { LoggerModule } from '@core/infrastructure/logger/logger.module';
import { CorrelationInterceptor } from './http/correlation.interceptor';
import { CORRELATION_SERVICE } from './correlation.interface';

@Global()
@Module({
  imports: [ConfigModule, forwardRef(() => LoggerModule)],
  providers: [
    {
      provide: CORRELATION_SERVICE,
      useClass: CorrelationService,
      scope: Scope.REQUEST, // Explicitly define the scope
    },
    CorrelationInterceptor,
    CorrelationMiddleware,
    // Provide the actual service class for backward compatibility if needed
    CorrelationService,
  ],
  exports: [
    CORRELATION_SERVICE,
    CorrelationService, // Export both token and class
    CorrelationInterceptor,
  ],
})
export class CorrelationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(CorrelationMiddleware).forRoutes('*');
  }
}
