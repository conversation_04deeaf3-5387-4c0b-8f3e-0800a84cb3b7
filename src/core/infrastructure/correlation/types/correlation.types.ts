/**
 * Core correlation context that links different parts of a request/operation
 */
export interface CoreCorrelationContext {
  /** Unique correlation ID for the request/operation */
  correlationId: string;
  /** OpenTelemetry trace ID */
  traceId: string;
  /** Current span ID */
  spanId: string;
  /** Parent span ID if exists */
  parentSpanId?: string;
}

/**
 * Extended correlation context with business context
 */
export interface CorrelationContext extends CoreCorrelationContext {
  /** User performing the operation */
  userId?: string;
  /** Tenant context if applicable */
  tenantId?: string;
  /** Request ID from API gateway/client */
  requestId?: string;
  /** Session ID if applicable */
  sessionId?: string;
  /** Source of the operation (API, worker, scheduler, etc.) */
  source?: string;
  /** Environment (production, staging, etc.) */
  environment: string;
  /** Service/component name */
  service: string;
  /** Additional custom attributes */
  attributes?: Record<string, any>;
}
