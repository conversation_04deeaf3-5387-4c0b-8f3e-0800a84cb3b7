import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from './logger/logger.module';
import { TracingModule } from './tracing/tracing.module';
import { CorrelationModule } from './correlation/correlation.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { ErrorTrackingModule } from './errorTracking/error-tracking.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { TraceInterceptor } from './tracing/interceptors/trace.interceptor';
import { CorrelationInterceptor } from './correlation/http/correlation.interceptor';
import { Reflector } from '@nestjs/core';
import {
  ITracerService,
  TRACER_SERVICE,
} from '@core/infrastructure/tracing/tracer.interface';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';
import { CorrelationService } from '@core/infrastructure/correlation/correlation.service';
import { ExceptionsModule } from '@core/infrastructure/exceptions/exceptions.module';

@Global()
@Module({
  imports: [
    ConfigModule.forRoot(),
    LoggerModule,
    TracingModule,
    CorrelationModule,
    MonitoringModule,
    ErrorTrackingModule,
    ExceptionsModule,
  ],
  providers: [
    Reflector,
    {
      provide: APP_INTERCEPTOR,
      useFactory: (reflector: Reflector, tracer: ITracerService) => {
        return new TraceInterceptor(reflector, tracer);
      },
      inject: [Reflector, TRACER_SERVICE],
    },
    {
      provide: APP_INTERCEPTOR,
      useFactory: (
        logger: ILoggerService,
        correlationService: CorrelationService,
      ) => {
        return new CorrelationInterceptor(correlationService, logger);
      },
      inject: [LOGGER_SERVICE, CorrelationService],
    },
  ],
  exports: [
    LoggerModule,
    TracingModule,
    CorrelationModule,
    MonitoringModule,
    ErrorTrackingModule,
  ],
})
export class InfrastructureModule {}
