import {
  forwardRef,
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  context,
  Span,
  SpanKind,
  SpanStatusCode,
  trace,
  Tracer,
} from '@opentelemetry/api';
import { Resource } from '@opentelemetry/resources';
import {
  ATTR_SERVICE_NAME,
  ATTR_SERVICE_VERSION,
} from '@opentelemetry/semantic-conventions';
import { NodeSDK } from '@opentelemetry/sdk-node';
import {
  BatchSpanProcessor,
  TraceIdRatioBasedSampler,
} from '@opentelemetry/sdk-trace-base';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { NodeTracerProvider } from '@opentelemetry/sdk-trace-node';
import { CorrelationService } from '@core/infrastructure/correlation/correlation.service';
import { TRACING_CONSTANTS } from '@core/infrastructure/tracing/tracing.constants';
import {
  TraceAttributes,
  TracingConfig,
} from '@core/infrastructure/tracing/types/tracing.types';
import {
  ILoggerService,
  LOGGER_SERVICE,
} from '@core/infrastructure/logger/logger.interface';

/**
 * Service responsible for managing OpenTelemetry tracing functionality.
 * Handles tracer initialization, configuration, and cleanup.
 */
@Injectable()
export class TracerService implements OnModuleInit, OnModuleDestroy {
  private sdk: NodeSDK;
  private defaultTracer: Tracer;
  private customTracer: Tracer;
  private defaultProvider: NodeTracerProvider;
  private customProvider: NodeTracerProvider;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => CorrelationService))
    private readonly correlation: CorrelationService,
    @Inject(LOGGER_SERVICE)
    private readonly logger: ILoggerService,
  ) {
    this.initializeTracers();
  }
  /**
   * Gets current active span
   */
  getCurrentSpan(): Span | undefined {
    return trace.getSpan(context.active());
  }

  /**
   * Initializes the OpenTelemetry tracers and SDK.
   * @private
   */
  private initializeTracers(): void {
    const config = this.getTracingConfig();
    console.log('Tracing config:', config);

    console.log('Creating tracers for services:', {
      default: TRACING_CONSTANTS.DEFAULT_SERVICE_NAME,
      custom: TRACING_CONSTANTS.CUSTOM_TRACES_SERVICE_NAME,
    });

    // Create resources for each service
    const defaultResource = new Resource({
      [ATTR_SERVICE_NAME]: TRACING_CONSTANTS.DEFAULT_SERVICE_NAME,
      [ATTR_SERVICE_VERSION]: config.serviceVersion,
      environment: config.environment,
    });

    const customResource = new Resource({
      [ATTR_SERVICE_NAME]: TRACING_CONSTANTS.CUSTOM_TRACES_SERVICE_NAME,
      [ATTR_SERVICE_VERSION]: config.serviceVersion,
      environment: config.environment,
    });

    // Create providers with their respective resources
    this.defaultProvider = new NodeTracerProvider({
      resource: defaultResource,
      sampler: config?.sampling?.ratio
        ? new TraceIdRatioBasedSampler(config.sampling.ratio)
        : undefined, // Or provide a default sampler like AlwaysOnSampler
    });

    this.customProvider = new NodeTracerProvider({
      resource: customResource,
      sampler: config?.sampling?.ratio
        ? new TraceIdRatioBasedSampler(config.sampling.ratio)
        : undefined,
    });

    // Create exporter
    const exporter = new OTLPTraceExporter({
      url: config.endpoint,
      timeoutMillis: 15000,
      concurrencyLimit: 10,
    });

    // Set up processors for each provider
    this.defaultProvider.addSpanProcessor(
      new BatchSpanProcessor(
        exporter,
        TRACING_CONSTANTS.DEFAULT_BATCH_PROCESSOR_OPTIONS,
      ),
    );
    this.customProvider.addSpanProcessor(
      new BatchSpanProcessor(
        exporter,
        TRACING_CONSTANTS.DEFAULT_BATCH_PROCESSOR_OPTIONS,
      ),
    );

    // Register the providers
    this.defaultProvider.register();
    this.customProvider.register();

    // Get tracers from their respective providers
    this.defaultTracer = this.defaultProvider.getTracer(
      TRACING_CONSTANTS.DEFAULT_SERVICE_NAME,
      config.serviceVersion,
    );
    this.customTracer = this.customProvider.getTracer(
      TRACING_CONSTANTS.CUSTOM_TRACES_SERVICE_NAME,
      config.serviceVersion,
    );

    // Initialize SDK with auto-instrumentations
    this.sdk = new NodeSDK({
      resource: defaultResource,
      spanProcessor: new BatchSpanProcessor(exporter),
      instrumentations: [
        getNodeAutoInstrumentations({
          '@opentelemetry/instrumentation-http': {
            enabled: true,
          },
          '@opentelemetry/instrumentation-express': { enabled: true },
          '@opentelemetry/instrumentation-nestjs-core': { enabled: true },
          '@opentelemetry/instrumentation-pg': { enabled: true },
          '@opentelemetry/instrumentation-fs': { enabled: false },
          '@opentelemetry/instrumentation-dns': { enabled: false },
        }),
      ],
    });
  }

  /**
   * Creates the tracing configuration from environment variables and defaults.
   * @private
   * @returns {TracingConfig} The complete tracing configuration
   */
  private getTracingConfig(): TracingConfig {
    return {
      serviceName: this.configService.get(
        'OTEL_SERVICE_NAME',
        TRACING_CONSTANTS.DEFAULT_SERVICE_NAME,
        { infer: true },
      ),
      serviceVersion: this.configService.get('OTEL_SERVICE_VERSION', '1.0.0', {
        infer: true,
      }),
      environment: this.configService.get('NODE_ENV', 'development', {
        infer: true,
      }),
      endpoint: this.configService.get(
        'OTEL_EXPORTER_OTLP_ENDPOINT',
        TRACING_CONSTANTS.DEFAULT_OTLP_ENDPOINT,
        { infer: true },
      ),
      sampling: {
        ratio: parseFloat(
          this.configService.get(
            'OTEL_SAMPLING_RATIO',
            TRACING_CONSTANTS.DEFAULT_SAMPLING_RATIO.toString(),
            { infer: true },
          ),
        ),
      },
    };
  }

  /**
   * Starts a new trace span with the default tracer.
   * @param {string} name - Name of the span
   * @param operation - The operation to trace
   * @param {TraceAttributes} [attributes] - Optional attributes to attach to the span
   * @returns {Promise<T>} Result of the traced operation
   */
  async trace<T>(
    name: string,
    operation: () => Promise<T>,
    attributes: Record<string, any> = {},
  ): Promise<T> {
    const tracer = trace.getTracer('app-tracer');
    const correlationContext = this.correlation.getCurrentContext();

    const span = tracer.startSpan(name, {
      kind: SpanKind.INTERNAL,
      attributes: {
        ...attributes,
        'correlation.id': correlationContext.correlationId,
        'tenant.id': correlationContext.tenantId,
        'user.id': correlationContext.userId,
        'service.name': correlationContext.service,
        environment: correlationContext.environment,
      },
    });

    // Run operation in span context
    return context.with(trace.setSpan(context.active(), span), async () => {
      try {
        const result = await operation();
        span.setStatus({ code: SpanStatusCode.OK });
        return result;
      } catch (error) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error instanceof Error ? error.message : 'Unknown error',
        });
        span.recordException(error as Error);
        throw error;
      } finally {
        span.end();
      }
    });
  }

  /**
   * Starts a new trace span with the custom tracer for specific business operations.
   * @param {string} name - Name of the span
   * @param operation - The operation to trace
   * @param {TraceAttributes} [attributes] - Optional attributes to attach to the span
   * @returns {Promise<T>} Result of the traced operation
   */
  async customTrace<T>(
    name: string,
    operation: () => Promise<T>,
    attributes: TraceAttributes = {},
  ): Promise<T> {
    const correlationContext = this.correlation.getCurrentContext();

    // Log operation start with correlation
    this.logger.debug(`Starting custom trace: ${name}`, {
      metadata: {
        attributes,
        correlationId: correlationContext.correlationId,
      },
    });

    const startTime = Date.now();
    try {
      const result = await this.trace(name, operation, attributes);

      // Log success with timing
      this.logger.debug(`Completed custom trace: ${name}`, {
        metadata: {
          attributes,
          correlationId: correlationContext.correlationId,
        },
        metrics: {
          durationMs: Date.now() - startTime,
        },
      });

      return result;
    } catch (error) {
      // Log failure with correlation
      this.logger.error(`Failed custom trace: ${name}`, error as Error, {
        metadata: {
          attributes,
          correlationId: correlationContext.correlationId,
        },
        metrics: {
          durationMs: Date.now() - startTime,
        },
      });
      throw error;
    }
  }

  /**
   * Lifecycle hook that starts the OpenTelemetry SDK.
   */
  async onModuleInit() {
    console.log('Starting OpenTelemetry SDK...');
    try {
      await this.sdk.start();
      console.log('OpenTelemetry SDK started successfully');
    } catch (error) {
      console.error('Failed to start OpenTelemetry SDK:', error);
    }
  }

  /**
   * Lifecycle hook that ensures proper cleanup of tracing resources.
   */
  async onModuleDestroy() {
    await this.sdk.shutdown();
  }
}
