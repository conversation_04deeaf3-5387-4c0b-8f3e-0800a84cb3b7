import { Span } from '@opentelemetry/api';
import { TraceAttributes } from './types/tracing.types';

export const TRACER_SERVICE = Symbol('TRACER_SERVICE');

export interface ITracerService {
  getCurrentSpan(): Span | undefined;

  trace<T>(
    name: string,
    operation: () => Promise<T>,
    attributes?: Record<string, any>,
  ): Promise<T>;

  customTrace<T>(
    name: string,
    operation: () => Promise<T>,
    attributes?: TraceAttributes,
  ): Promise<T>;
}
