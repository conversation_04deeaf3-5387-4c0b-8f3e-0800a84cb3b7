import { Global, Module, forwardRef } from '@nestjs/common';
import { TracerService } from './tracer.service';
import { LoggerModule } from '@core/infrastructure/logger/logger.module';
import { CorrelationModule } from '@core/infrastructure/correlation/correlation.module';
import { TRACER_SERVICE } from './tracer.interface';
import { ConfigModule } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { TraceInterceptor } from './interceptors/trace.interceptor';

@Global()
@Module({
  imports: [
    ConfigModule,
    forwardRef(() => LoggerModule),
    forwardRef(() => CorrelationModule),
  ],
  providers: [
    Reflector,
    TraceInterceptor,
    {
      provide: TRACER_SERVICE,
      useClass: TracerService,
    },
  ],
  exports: [TRACER_SERVICE, TraceInterceptor, Reflector],
})
export class TracingModule {}
