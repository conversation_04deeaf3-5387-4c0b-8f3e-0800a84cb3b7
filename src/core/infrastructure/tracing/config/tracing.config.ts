import { registerAs } from '@nestjs/config';
import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsOptional } from 'class-validator';
import { TracingConfig } from '@core/infrastructure/tracing/types/tracing.types';
import validateConfig from '@utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  OTEL_SERVICE_NAME: string;

  @IsString()
  @IsOptional()
  OTEL_SERVICE_VERSION: string;

  @IsString()
  @IsOptional()
  OTEL_EXPORTER_OTLP_ENDPOINT: string;

  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  OTEL_SAMPLING_RATIO: number;
}

export default registerAs<TracingConfig>('tracing', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    serviceName: process.env.OTEL_SERVICE_NAME || 'transport-app-full',
    environment: process.env.NODE_ENV || 'development',
    serviceVersion: process.env.OTEL_SERVICE_VERSION || '1.0.0',
    endpoint:
      process.env.OTEL_EXPORTER_OTLP_ENDPOINT ||
      'http://localhost:4318/v1/traces',
    samplingRatio: process.env.OTEL_SAMPLING_RATIO
      ? parseFloat(process.env.OTEL_SAMPLING_RATIO)
      : 0.1,
  };
});
