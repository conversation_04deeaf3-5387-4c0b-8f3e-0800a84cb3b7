/**
 * Represents configuration options for the tracing system.
 * @interface TracingConfig
 */
export interface TracingConfig {
  /** Name of the service that will appear in traces */
  serviceName: string;
  /** Current version of the service */
  serviceVersion: string;
  /** Environment the service is running in (e.g., 'production', 'staging') */
  environment: string;
  /** Endpoint where traces should be sent */
  endpoint: string;
  /** Optional headers to include with trace exports */
  headers?: Record<string, string>;
  /** Sampling configuration */
  sampling?: {
    /** Ratio of traces to sample (0.0 to 1.0) */
    ratio?: number;
  };
}

/**
 * Represents additional trace attributes that can be attached to spans.
 * @interface TraceAttributes
 */
export interface TraceAttributes {
  [key: string]: string | number | boolean | string[] | undefined;
}

/**
 * Configuration for a specific trace decorator.
 * @interface TraceOptions
 */
export interface TraceOptions {
  /** Name of the trace within a specific service/app context */
  name: string;
  /** Specific service name override for this trace */
  serviceName?: string;
  /** Additional attributes to attach to the trace */
  attributes?: TraceAttributes;
}
