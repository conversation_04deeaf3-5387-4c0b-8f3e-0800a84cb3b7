import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { ITracerService, TRACER_SERVICE } from '../tracer.interface';
import { TraceOptions } from '../types/tracing.types';
import { TRACE_METADATA_KEY } from '@core/infrastructure/tracing/tracing.constants';

@Injectable()
export class TraceInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,
    @Inject(TRACER_SERVICE) private readonly tracerService: ITracerService,
  ) {
    if (!reflector) {
      throw new Error('Reflector not injected into TraceInterceptor');
    }
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const traceOptions = this.reflector.get<TraceOptions>(
      TRACE_METADATA_KEY,
      context.getHandler(),
    );

    if (!traceOptions) {
      return next.handle();
    }

    const { name, serviceName, attributes } = traceOptions;
    const ctx = context.switchToHttp();
    const request = ctx.getRequest();

    // Add default HTTP attributes
    const defaultAttributes = {
      'http.method': request.method,
      'http.url': request.url,
      'http.route': request.route?.path,
      'handler.class': context.getClass().name,
      'handler.method': context.getHandler().name,
    };

    return new Observable((subscriber) => {
      const traceMethod = serviceName ? 'customTrace' : 'trace';

      this.tracerService[traceMethod](name, () => next.handle().toPromise(), {
        ...defaultAttributes,
        ...attributes,
      })
        .then((value) => {
          subscriber.next(value);
          subscriber.complete();
        })
        .catch((error) => {
          subscriber.error(error);
        });
    });
  }
}
