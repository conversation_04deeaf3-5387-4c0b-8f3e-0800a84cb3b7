import { Global, Module } from '@nestjs/common';
import { APP_FILTER, HttpAdapterHost } from '@nestjs/core';
import { AllExceptionsFilter } from './filters/all-exceptions.filter';
import { ExceptionsService } from './exceptions.service';
import { EXCEPTIONS_SERVICE } from './exceptions.interface';
import { LoggerModule } from '../logger/logger.module';
import { ErrorTrackingModule } from '../errorTracking/error-tracking.module';
import { CorrelationModule } from '../correlation/correlation.module';
import { CORRELATION_SERVICE } from '@core/infrastructure/correlation/correlation.interface';

@Global()
@Module({
  imports: [LoggerModule, ErrorTrackingModule, CorrelationModule],
  providers: [
    {
      provide: EXCEPTIONS_SERVICE,
      useClass: ExceptionsService,
    },
    {
      provide: APP_FILTER,
      useFactory: (
        httpAdapterHost: HttpAdapterHost,
        exceptionsService: ExceptionsService,
        correlationService: any,
      ) => {
        return new AllExceptionsFilter(
          httpAdapterHost,
          exceptionsService,
          correlationService,
        );
      },
      inject: [HttpAdapterHost, EXCEPTIONS_SERVICE, CORRELATION_SERVICE],
    },
  ],
  exports: [EXCEPTIONS_SERVICE],
})
export class ExceptionsModule {}
