import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  Inject,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { Request, Response } from 'express';
import {
  CORRELATION_SERVICE,
  ICorrelationService,
} from '../../correlation/correlation.interface';
import {
  EXCEPTIONS_SERVICE,
  IExceptionsService,
} from '../exceptions.interface';
import chalk from 'chalk';
import { IErrorResponse } from '../types/exception.types';
import { CorrelationContext } from '@core/infrastructure/correlation/types/correlation.types';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    @Inject(EXCEPTIONS_SERVICE)
    private readonly exceptionsService: IExceptionsService,
    @Inject(CORRELATION_SERVICE)
    private readonly correlationService: ICorrelationService,
  ) {
    this.logger.log('AllExceptionsFilter initialized');
    if (!correlationService) {
      throw new Error('CorrelationService not injected');
    }
  }

  async catch(exception: unknown, host: ArgumentsHost): Promise<void> {
    const httpAdapter =
      this.httpAdapterHost?.httpAdapter || host.getType() === 'http'
        ? host.switchToHttp().getResponse().app
        : null;

    if (!httpAdapter) {
      this.logger.error('HTTP Adapter not available', {
        error: exception,
        context: 'ExceptionFilter',
      });
      throw exception;
    }

    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const correlationContext = this.correlationService.getCurrentContext();
    const startTime = process.hrtime();

    try {
      const errorContext = {
        correlationId: correlationContext.correlationId,
        traceId: correlationContext.traceId,
        request: this.exceptionsService.sanitizeRequest(request),
        timestamp: new Date().toISOString(),
      };

      // Process the exception
      const processedError = await this.exceptionsService.processException(
        exception,
        request,
        errorContext,
      );

      // Log with pretty console output in development
      if (process.env.NODE_ENV !== 'production') {
        this.logPrettyError(processedError, this.getDuration(startTime));
      }

      // Log structured error
      this.exceptionsService.logException(
        processedError,
        exception instanceof Error ? exception : new Error(String(exception)),
        errorContext,
      );

      // Set correlation headers
      this.setResponseHeaders(response);

      // Send response
      httpAdapter.reply(response, processedError, processedError.statusCode);
    } catch (error) {
      // Fallback error handling
      this.handleFallbackError(
        error,
        httpAdapter,
        response,
        correlationContext,
      );
    }
  }

  private getDuration(startTime: [number, number]): number {
    const [seconds, nanoseconds] = process.hrtime(startTime);
    return seconds * 1000 + nanoseconds / 1000000; // Convert to milliseconds
  }

  private setResponseHeaders(response: Response): void {
    const headers = this.correlationService.getCorrelationHeaders();
    Object.entries(headers).forEach(([key, value]) => {
      if (value) response.setHeader(key, String(value));
    });
  }

  private logPrettyError(error: IErrorResponse, duration: number): void {
    const separator = chalk.gray('─'.repeat(80));

    console.error('\n' + separator);
    console.error(
      chalk.red.bold('🚨 Error:'),
      chalk.yellow(`[${error.timestamp}]`),
    );
    console.error(separator);

    // Error details
    console.error(chalk.red('Status:'), chalk.yellow(String(error.statusCode)));
    console.error(chalk.red('Code:'), chalk.yellow(error.errorCode));
    console.error(chalk.red('Message:'), chalk.yellow(error.message));

    if (error.details) {
      console.error(
        chalk.red('Details:'),
        chalk.gray(JSON.stringify(error.details, null, 2)),
      );
    }

    // Context information
    console.error(separator);
    console.error(chalk.blue.bold('Context:'));
    console.error(chalk.blue('Path:'), chalk.gray(error.path));
    console.error(chalk.blue('Error ID:'), chalk.gray(error.errorId));
    console.error(
      chalk.blue('Correlation ID:'),
      chalk.gray(error.correlationId),
    );
    console.error(chalk.blue('Trace ID:'), chalk.gray(error.traceId));
    console.error(
      chalk.blue('Duration:'),
      chalk.gray(`${duration.toFixed(2)}ms`),
    );

    console.error(separator + '\n');
  }

  private handleFallbackError(
    error: unknown,
    httpAdapter: any,
    response: Response,
    correlationContext: CorrelationContext,
  ): void {
    this.logger.error('Critical error in exception filter', error);

    const fallbackError = {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Internal server error',
      errorCode: 'ERR1000',
      timestamp: new Date().toISOString(),
      errorId: 'internal-error',
      correlationId: String(correlationContext.correlationId),
      traceId: String(correlationContext.traceId),
    };

    httpAdapter.reply(
      response,
      fallbackError,
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}
