export const ERROR_CODES = {
  // Generic errors (1000-1999)
  UNKNOWN: 'ERR1000',
  VALIDATION_ERROR: 'ERR1001',
  UNAUTHORIZED: 'ERR1002',
  FORBIDDEN: 'ERR1003',
  NOT_FOUND: 'ERR1004',

  // Resource errors (2000-2099)
  RESOURCE_NOT_FOUND: 'ERR2000',
  RESOURCE_ALREADY_EXISTS: 'ERR2001',
  RESOURCE_INVALID: 'ERR2002',

  // Data errors (3000-3099)
  DATA_VALIDATION_ERROR: 'ERR3000',
  DATA_INTEGRITY_ERROR: 'ERR3001',

  // Infrastructure errors (4000-4099)
  INFRASTRUCTURE_ERROR: 'ERR4000',
  EXTERNAL_SERVICE_ERROR: 'ERR4001',
  DATABASE_ERROR: 'ERR4002',
} as const;

export type ErrorCode = (typeof ERROR_CODES)[keyof typeof ERROR_CODES];
