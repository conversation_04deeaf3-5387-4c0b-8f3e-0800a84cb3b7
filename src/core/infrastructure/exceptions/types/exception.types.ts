import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '../constants/error-codes.constants';

export interface IErrorResponse {
  statusCode: HttpStatus;
  message: string;
  errorCode: ErrorCode;
  timestamp: string;
  path: string;
  correlationId: string;
  traceId: string;
  errorId: string;
  details?: Record<string, unknown>;
}

export interface ErrorContext {
  request?: {
    method: string;
    path: string;
    query?: Record<string, unknown>;
    body?: Record<string, unknown>;
    headers?: Record<string, string>;
  };
  correlationId?: string;
  traceId?: string;
  timestamp?: string;
  additionalContext?: Record<string, unknown>;
}
