import { Inject, Injectable } from '@nestjs/common';
import { IExceptionsService } from './exceptions.interface';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ErrorContext, IErrorResponse } from './types/exception.types';
import { InfrastructureException } from './exception';
import { ERROR_CODES } from './constants/error-codes.constants';
import { ILoggerService, LOGGER_SERVICE } from '../logger/logger.interface';
import {
  ERROR_TRACKING_SERVICE,
  IErrorTrackingService,
} from '../errorTracking/error-tracking.interface';
import { LogContext } from '../logger/types/logger.types';
import { ErrorSeverity } from '../logger/types/severity.types';
import { HttpStatus } from '@nestjs/common';

@Injectable()
export class ExceptionsService implements IExceptionsService {
  private readonly sensitiveFields = new Set([
    'password',
    'token',
    'authorization',
    'secret',
    'key',
    'apiKey',
  ]);

  constructor(
    @Inject(LOGGER_SERVICE)
    private readonly logger: ILoggerService,
    @Inject(ERROR_TRACKING_SERVICE)
    private readonly errorTracking: IErrorTrackingService,
  ) {}

  async processException(
    error: unknown,
    request: Request,
    context: ErrorContext,
  ): Promise<IErrorResponse> {
    const errorId = uuidv4();
    const httpStatus = this.determineHttpStatus(error);
    const timestamp = new Date().toISOString();

    if (error instanceof InfrastructureException) {
      error.enrich({
        correlationId: context.correlationId,
        traceId: context.traceId,
        path: request.path,
        errorId,
      });

      const response = error.getResponse() as IErrorResponse;
      return {
        ...response,
        path: request.path,
        errorId,
        correlationId: context.correlationId ?? 'unknown',
        traceId: context.traceId ?? 'unknown',
      };
    }

    // Track error
    await this.errorTracking.trackError(
      error instanceof Error ? error : new Error(String(error)),
      {
        severity: this.determineErrorSeverity(httpStatus),
        context: {
          request: this.sanitizeRequest(request),
          ...context,
        },
        tags: {
          'error.type': 'infrastructure',
          'http.status': httpStatus.toString(),
          'request.path': request.path,
        },
      },
    );

    return {
      statusCode: httpStatus,
      message: error instanceof Error ? error.message : 'Internal Server Error',
      errorCode: ERROR_CODES.UNKNOWN,
      timestamp,
      path: request.path,
      errorId,
      correlationId: context.correlationId ?? 'unknown',
      traceId: context.traceId ?? 'unknown',
      details:
        error instanceof Error
          ? {
              name: error.name,
              stack: error.stack,
            }
          : undefined,
    };
  }

  logException(
    error: IErrorResponse,
    originalError: Error,
    context: ErrorContext,
  ): void {
    const logContext: LogContext = {
      correlationId: error.correlationId,
      traceId: error.traceId,
      metadata: {
        errorId: error.errorId,
        statusCode: error.statusCode,
        errorCode: error.errorCode,
        ...context,
      },
      error: {
        name: originalError.name,
        message: originalError.message,
        stack: originalError.stack,
      },
      tags: ['exception', `status-${error.statusCode}`, error.errorCode],
    };

    this.logger.error(
      `Request failed: ${context.request?.method} ${error.path}`,
      originalError,
      logContext,
    );
  }

  sanitizeRequest(request: Request) {
    return {
      method: request.method,
      path: request.path,
      headers: this.sanitizeHeaders(request.headers),
      query: this.sanitizeData(request.query),
      body: this.sanitizeData(request.body),
    };
  }

  determineHttpStatus(error: unknown): HttpStatus {
    if (error instanceof InfrastructureException) {
      return error.getStatus();
    }
    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  private determineErrorSeverity(httpStatus: HttpStatus): ErrorSeverity {
    if (httpStatus >= 500) return ErrorSeverity.HIGH;
    if (httpStatus === 429) return ErrorSeverity.MEDIUM;
    if (httpStatus >= 400) return ErrorSeverity.LOW;
    return ErrorSeverity.MEDIUM;
  }

  private sanitizeHeaders(
    headers: Record<string, unknown>,
  ): Record<string, string> {
    const sanitized: Record<string, string> = {};
    for (const [key, value] of Object.entries(headers)) {
      if (this.sensitiveFields.has(key.toLowerCase())) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = Array.isArray(value)
          ? value.join(', ')
          : String(value);
      }
    }
    return sanitized;
  }

  private sanitizeData(data: unknown): Record<string, unknown> | undefined {
    if (!data || typeof data !== 'object') {
      return undefined;
    }

    try {
      const sanitized = JSON.parse(JSON.stringify(data));
      return this.redactSensitiveData(sanitized);
    } catch {
      return undefined;
    }
  }

  private redactSensitiveData(
    obj: Record<string, unknown>,
  ): Record<string, unknown> {
    const result = { ...obj };
    for (const [key, value] of Object.entries(result)) {
      if (this.sensitiveFields.has(key.toLowerCase())) {
        result[key] = '[REDACTED]';
      } else if (value && typeof value === 'object') {
        result[key] = this.redactSensitiveData(
          value as Record<string, unknown>,
        );
      }
    }
    return result;
  }
}
