import { HttpStatus } from '@nestjs/common';
import { ErrorContext, IErrorResponse } from './types/exception.types';
import { Request } from 'express';

export const EXCEPTIONS_SERVICE = Symbol('EXCEPTIONS_SERVICE');

export interface IExceptionsService {
  processException(
    error: unknown,
    request: Request,
    context: ErrorContext,
  ): Promise<IErrorResponse>;

  logException(
    error: IErrorResponse,
    originalError: Error,
    context: ErrorContext,
  ): void;

  sanitizeRequest(request: Request): {
    method: string;
    path: string;
    query?: Record<string, unknown>;
    headers?: Record<string, string>;
    body?: Record<string, unknown>;
  };

  determineHttpStatus(error: unknown): HttpStatus;
}
