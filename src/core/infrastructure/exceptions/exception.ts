import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCode } from './constants/error-codes.constants';
import { IErrorResponse } from './types/exception.types';

export class InfrastructureException extends HttpException {
  public readonly errorCode: ErrorCode;
  public readonly timestamp: string;
  public correlationId?: string;
  public traceId?: string;
  public errorId?: string;

  constructor(
    message: string,
    errorCode: ErrorCode,
    statusCode: HttpStatus,
    details?: Record<string, unknown>,
  ) {
    const response: IErrorResponse = {
      statusCode,
      message,
      errorCode,
      timestamp: new Date().toISOString(),
      path: '', // Will be set by the filter
      correlationId: '', // Will be set by the filter
      traceId: '', // Will be set by the filter
      errorId: '', // Will be set by the filter
      details,
    };

    super(response, statusCode);
    this.errorCode = errorCode;
    this.timestamp = response.timestamp;
  }

  public enrich(context: {
    correlationId?: string;
    traceId?: string;
    path?: string;
    errorId?: string;
  }): this {
    if (context.correlationId) {
      this.correlationId = context.correlationId;
    }
    if (context.traceId) {
      this.traceId = context.traceId;
    }
    if (context.errorId) {
      this.errorId = context.errorId;
    }
    return this;
  }
}
