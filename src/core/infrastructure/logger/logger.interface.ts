import { LogContext } from './types/logger.types';

export const LOGGER_SERVICE = Symbol('LOGGER_SERVICE');

export interface ILoggerService {
  debug(message: string, context?: LogContext): void;
  info(message: string, context?: LogContext): void;
  warn(message: string, context?: LogContext): void;
  error(message: string, error: Error, context?: LogContext): void;

  startOperation(name: string): void;
  endOperation(): void;

  time<T>(
    message: string,
    operation: () => Promise<T> | T,
    context?: LogContext,
  ): Promise<T>;
}
