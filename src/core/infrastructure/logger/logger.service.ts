import chalk from 'chalk'; // Import chalk for CLI styling
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AttributeValue, Span } from '@opentelemetry/api';
import { CorrelationService } from '../correlation/correlation.service';
import { LogContext, LogEntry } from './types/logger.types';
import { LogLevel } from './types/log-level.types';
import { ILoggerService } from '@core/infrastructure/logger/logger.interface';
import {
  ITracerService,
  TRACER_SERVICE,
} from '@core/infrastructure/tracing/tracer.interface';

@Injectable()
export class LoggerService implements ILoggerService {
  private operationStack: { name: string; startTime: number }[] = [];

  constructor(
    private readonly config: ConfigService,
    @Inject(forwardRef(() => CorrelationService))
    private readonly correlation: CorrelationService,
    @Inject(forwardRef(() => TRACER_SERVICE))
    private readonly tracer: ITracerService,
  ) {
    if (!config) throw new Error('ConfigService not provided to LoggerService');
    if (!correlation)
      throw new Error('CorrelationService not provided to LoggerService');
    if (!tracer) throw new Error('TracerService not provided to LoggerService');
  }

  debug(message: string, context?: LogContext): void {
    if (this.isDebugEnabled()) {
      this.log(LogLevel.DEBUG, message, context);
    }
  }

  info(message: string, context?: LogContext): void {
    this.log(LogLevel.INFO, message, context);
  }

  warn(message: string, context?: LogContext): void {
    this.log(LogLevel.WARN, message, context);
  }

  error(message: string, error: Error, context?: LogContext): void {
    this.log(LogLevel.ERROR, message, {
      ...context,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    });
  }

  async time<T>(
    message: string,
    operation: () => Promise<T> | T,
    context?: LogContext,
  ): Promise<T> {
    const start = performance.now();
    try {
      const result = await operation();
      const duration = performance.now() - start;

      this.info(message, {
        ...context,
        metrics: {
          ...(context?.metrics || {}),
          durationMs: Math.round(duration),
        },
      });

      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.error(message, error as Error, {
        ...context,
        metrics: {
          ...(context?.metrics || {}),
          durationMs: Math.round(duration),
        },
      });
      throw error;
    }
  }

  startOperation(name: string): void {
    this.operationStack.push({
      name,
      startTime: performance.now(),
    });
  }

  endOperation(): void {
    const operation = this.operationStack.pop();
    if (operation) {
      const duration = performance.now() - operation.startTime;
      this.info(`Operation ${operation.name} completed`, {
        metrics: { durationMs: Math.round(duration) },
      });
    }
  }

  private log(level: LogLevel, message: string, context?: LogContext): void {
    const entry = this.createLogEntry(level, message, context);

    if (this.operationStack.length > 0) {
      const currentOp = this.operationStack[this.operationStack.length - 1];
      entry.context.action = currentOp.name;
    }

    this.writeLog(entry);
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: LogContext,
  ): LogEntry {
    const correlationContext = this.correlation.getCurrentContext();
    const currentSpan = this.tracer.getCurrentSpan();
    const spanAttributes = currentSpan
      ? this.getSpanAttributes(currentSpan)
      : undefined;

    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: {
        ...context,
        correlationId: correlationContext.correlationId,
        traceId: correlationContext.traceId,
        spanId: correlationContext.spanId,
        tenantId: correlationContext.tenantId,
        userId: correlationContext.userId,
        environment: correlationContext.environment,
        service: correlationContext.service,
        ...(spanAttributes && { spanAttributes }),
      },
    };
  }

  private getSpanAttributes(span: Span): Record<string, AttributeValue> {
    try {
      const spanContext = span.spanContext();
      return {
        'span.id': spanContext.spanId,
        'trace.id': spanContext.traceId,
        'trace.flags': spanContext.traceFlags.toString(),
        'trace.state': spanContext.traceState?.serialize() || '',
      };
    } catch (error) {
      return {
        'span.id': span.spanContext().spanId,
        error: 'Failed to get span attributes',
      };
    }
  }

  private writeLog(entry: LogEntry): void {
    const formattedMessage = this.formatLogMessage(entry);

    switch (entry.level) {
      case LogLevel.ERROR:
        console.error(chalk.red(formattedMessage));
        break;
      case LogLevel.WARN:
        console.warn(chalk.yellow(formattedMessage));
        break;
      case LogLevel.DEBUG:
        console.debug(chalk.blue(formattedMessage));
        break;
      default:
        console.log(chalk.green(formattedMessage));
    }
  }

  private formatLogMessage(entry: LogEntry): string {
    const { timestamp, level, message, context } = entry;
    const { correlationId, traceId, spanId, ...restContext } = context;

    const correlationInfo = chalk.gray(
      `[cid:${correlationId}] [tid:${traceId}] [sid:${spanId}]`,
    );
    const contextStr = this.formatContext(restContext);

    return `${chalk.bold(`[${timestamp}]`)} ${chalk.magenta(
      `[${level}]`,
    )} ${correlationInfo} ${chalk.cyan(message)} ${chalk.dim(contextStr)}`;
  }

  private formatContext(context: Record<string, any>): string {
    return Object.entries(context || {})
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) =>
        typeof value === 'object'
          ? `${chalk.yellow(key)}=${chalk.gray(JSON.stringify(value))}`
          : `${chalk.yellow(key)}=${chalk.gray(value)}`,
      )
      .join(' ');
  }

  private isDebugEnabled(): boolean {
    return this.config.get('logger.debug', false);
  }
}
