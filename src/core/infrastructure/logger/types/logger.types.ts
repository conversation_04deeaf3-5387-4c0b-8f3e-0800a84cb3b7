export interface LogContext {
  /** Operation or request ID for correlation */
  correlationId?: string;
  /** OpenTelemetry trace ID */
  traceId?: string;
  /** OpenTelemetry span ID */
  spanId?: string;
  /** User or service performing the action */
  actor?: string;
  /** Module or component name */
  module?: string;
  /** Specific action or operation name */
  action?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Resource identifiers */
  resources?: Record<string, string>;
  /** Performance metrics */
  metrics?: Record<string, number>;
  /** Custom tags for filtering */
  tags?: string[];
  duration?: number;
  /** Error information when logging errors */
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string | number;
    details?: Record<string, any>;
  };
  /** Span attributes from OpenTelemetry */
  spanAttributes?: Record<string, any>;
  /** Business Context Properties */
  tenantId?: string;
  userId?: string;
  environment?: string;
  service?: string;
  operation?: string;
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  context: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}
