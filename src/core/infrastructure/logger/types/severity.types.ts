/**
 * Error severity levels for system errors and operations
 * Used to categorize and prioritize error handling and notifications
 */
export enum ErrorSeverity {
  /**
   * Minor issues that don't affect core functionality
   * Example: Non-critical cache misses, slow but successful operations
   */
  LOW = 'LOW',

  /**
   * Issues that degrade but don't prevent functionality
   * Example: Retryable API failures, performance degradation
   */
  MEDIUM = 'MEDIUM',

  /**
   * Issues that prevent core functionality
   * Example: Database connection failures, critical service unavailability
   */
  HIGH = 'HIGH',

  /**
   * Critical system failures requiring immediate attention
   * Example: Data corruption, security breaches, complete system outage
   */
  CRITICAL = 'CRITICAL',
}
