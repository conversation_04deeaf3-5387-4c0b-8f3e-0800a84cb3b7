/**
 * Available log levels for system logging
 * Follows standard logging hierarchy for filtering and categorization
 */
export enum LogLevel {
  /** Detailed information for debugging purposes */
  DEBUG = 'debug',

  /** General operational information */
  INFO = 'info',

  /** Warning messages for potential issues */
  WARN = 'warn',

  /** Error messages for actual problems */
  ERROR = 'error',
}
