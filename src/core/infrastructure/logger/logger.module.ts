import { Global, Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LoggerService } from './logger.service';
import { LOGGER_SERVICE } from './logger.interface';
import { CorrelationModule } from '../correlation/correlation.module';
import { TracingModule } from '../tracing/tracing.module';

@Global()
@Module({
  imports: [
    ConfigModule,
    forwardRef(() => CorrelationModule),
    forwardRef(() => TracingModule),
  ],
  providers: [
    {
      provide: LOGGER_SERVICE,
      useClass: LoggerService,
    },
  ],
  exports: [LOGGER_SERVICE],
})
export class LoggerModule {}
