import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class FileUploadAuthGuard implements CanActivate {
  private jwtService: JwtService;

  constructor(private reflector: Reflector) {
    this.jwtService = new JwtService({
      secret: process.env.JWT_SECRET,
    });
  }

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    // const token = this.extractTokenFromHeader(request);

    // if (!token) {
    //   return false;
    // }

    try {
      // TODO: as of now this auth is not doing much of anything, but we should make it so that it should check the auth based on type of user
      const payload = '' as any; // Replace with actual JWT payload type

      // Store the user in the request object
      request['user'] = payload;

      // Check if the token has a 'type' field to determine the user type
      if (payload.type) {
        switch (payload.type) {
          case 'contact':
            // Contact-specific validation if needed
            return true;
          case 'tenant':
            // Tenant-specific validation if needed
            return true;
          case 'driver':
            // Driver-specific validation if needed
            return true;
          default:
            // Default case for other user types
            return true;
        }
      }

      // If no type is specified, allow access (general JWT token)
      return true;
    } catch {
      return false;
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
