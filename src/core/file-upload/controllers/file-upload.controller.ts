import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Body,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Res,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { FileUploadService } from '../services/file-upload.service';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiResponse,
} from '@nestjs/swagger';

@ApiTags('File Uploads')
@Controller('file-uploads')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a single file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload',
        },
        entityType: {
          type: 'string',
          description: 'Type of entity (tenant, contact, driver, etc.)',
          example: 'contact',
        },
        entityId: {
          type: 'string',
          description: 'ID of the entity',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
        type: {
          type: 'string',
          description: 'Type of file (profile, document, receipt, etc.)',
          example: 'profile',
        },
        userId: {
          type: 'string',
          description: 'ID of the user uploading the file (optional)',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
        metadata: {
          type: 'object',
          description: 'Additional metadata for the file (optional)',
          example: { description: 'Profile photo', tags: ['profile', 'photo'] },
        },
      },
      required: ['file', 'entityType', 'entityId', 'type'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'File uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        fileId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
        filename: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000.jpg',
        },
        url: {
          type: 'string',
          example:
            '/api/v1/file-uploads/download/550e8400-e29b-41d4-a716-446655440000',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('entityType') entityType: string,
    @Body('entityId') entityId: string,
    @Body('type') type: string,
    @Body('metadata') metadata: Record<string, any>,
    @Body('userId') userId?: string,
  ) {
    return this.fileUploadService.uploadFile(
      file,
      entityType,
      entityId,
      type,
      userId || null,
      metadata as any,
    );
  }

  @Post('upload-multiple')
  @ApiOperation({ summary: 'Upload multiple files' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Files to upload',
        },
        entityType: {
          type: 'string',
          description: 'Type of entity (tenant, contact, driver, etc.)',
          example: 'contact',
        },
        entityId: {
          type: 'string',
          description: 'ID of the entity',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
        type: {
          type: 'string',
          description: 'Type of file (profile, document, receipt, etc.)',
          example: 'document',
        },
        userId: {
          type: 'string',
          description: 'ID of the user uploading the file (optional)',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
        metadata: {
          type: 'object',
          description: 'Additional metadata for the file (optional)',
          example: { description: 'Contract documents', tags: ['contract'] },
        },
      },
      required: ['files', 'entityType', 'entityId', 'type'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Files uploaded successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          fileId: {
            type: 'string',
            example: '550e8400-e29b-41d4-a716-446655440000',
          },
          filename: {
            type: 'string',
            example: '550e8400-e29b-41d4-a716-446655440000.pdf',
          },
          url: {
            type: 'string',
            example:
              '/api/v1/file-uploads/download/550e8400-e29b-41d4-a716-446655440000',
          },
        },
      },
    },
  })
  @UseInterceptors(FilesInterceptor('files'))
  async uploadMultipleFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Body('entityType') entityType: string,
    @Body('entityId') entityId: string,
    @Body('type') type: string,
    @Body('metadata') metadata: Record<string, any>,
    @Body('userId') userId?: string,
  ) {
    return this.fileUploadService.uploadMultipleFiles(
      files,
      entityType,
      entityId,
      type,
      userId || null,
      metadata as any,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get file metadata by ID' })
  @ApiParam({
    name: 'id',
    description: 'File ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'File metadata',
  })
  async getFileById(@Param('id') id: string) {
    return this.fileUploadService.getFileById(id);
  }

  @Get('entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Get files by entity type and ID' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type',
    example: 'contact',
  })
  @ApiParam({
    name: 'entityId',
    description: 'Entity ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'List of files',
  })
  async getFilesByEntityTypeAndId(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
  ) {
    return this.fileUploadService.getFilesByEntityTypeAndId(
      entityType,
      entityId,
    );
  }

  @Get('download/:id')
  @ApiOperation({ summary: 'Download a file' })
  @ApiParam({
    name: 'id',
    description: 'File ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'File content',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async downloadFile(@Param('id') id: string, @Res() res: Response) {
    const fileData = await this.fileUploadService.getFileStream(id);

    if (!fileData) {
      return res.status(404).send('File not found');
    }

    res.setHeader('Content-Type', fileData.mimeType);
    fileData.stream.pipe(res);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a file' })
  @ApiParam({
    name: 'id',
    description: 'File ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        userId: {
          type: 'string',
          description: 'ID of the user deleting the file (optional)',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'File deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  async deleteFile(@Param('id') id: string, @Body('userId') userId?: string) {
    const result = await this.fileUploadService.deleteFile(id, userId || null);
    return { success: result };
  }
}
