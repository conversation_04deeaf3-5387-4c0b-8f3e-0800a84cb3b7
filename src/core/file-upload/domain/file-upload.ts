import { AutoMap } from '@automapper/classes';
import { FileUploadStatus } from './file-upload.types';

export class FileUpload {
  @AutoMap()
  id: string;

  @AutoMap()
  filename: string;

  @AutoMap()
  originalFilename: string;

  @AutoMap()
  type: string; // Can be anything (profile, document, receipt, etc.)

  @AutoMap()
  mimeType: string;

  @AutoMap()
  size: number; // Size in bytes

  @AutoMap()
  path: string;

  @AutoMap()
  url?: string;

  @AutoMap()
  metadata?: Record<string, any>;

  // Related entities
  @AutoMap()
  entityType: string; // 'tenant', 'contact', 'driver', 'order', etc.

  @AutoMap()
  entityId: string; // ID of the related entity

  // Timestamps
  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  // User who uploaded the file
  @AutoMap()
  createdBy: string | null;

  @AutoMap()
  updatedBy?: string | null;

  // Status
  @AutoMap()
  status: FileUploadStatus;
}
