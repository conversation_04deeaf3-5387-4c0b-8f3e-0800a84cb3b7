import { Module } from '@nestjs/common';
import { FileUploadService } from './services/file-upload.service';
import { FileUploadController } from './controllers/file-upload.controller';
import { FileUploadRelationalPersistenceModule } from './infrastructure/relational-persistence.module';
import { MulterModule } from '@nestjs/platform-express';

@Module({
  imports: [
    FileUploadRelationalPersistenceModule,
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  ],
  providers: [FileUploadService],
  controllers: [FileUploadController],
  exports: [FileUploadService],
})
export class FileUploadModule {}
