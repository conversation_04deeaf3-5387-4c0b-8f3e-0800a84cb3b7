import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileUploadEntity } from './entities/file-upload.entity';
import { FileUploadRepository } from './repositories/file-upload.repository';

@Module({
  imports: [TypeOrmModule.forFeature([FileUploadEntity])],
  providers: [FileUploadRepository],
  exports: [FileUploadRepository],
})
export class FileUploadRelationalPersistenceModule {}
