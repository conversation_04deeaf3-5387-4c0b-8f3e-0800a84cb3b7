import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { FileUploadStatus } from '../../domain/file-upload.types';

@Entity('file_uploads')
export class FileUploadEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column({ length: 255, name: 'filename' })
  filename: string;

  @AutoMap()
  @Column({ length: 255, name: 'original_filename' })
  originalFilename: string;

  @AutoMap()
  @Column({ length: 100, name: 'type' })
  type: string;

  @AutoMap()
  @Column({ length: 100, name: 'mime_type' })
  mimeType: string;

  @AutoMap()
  @Column({ type: 'bigint', name: 'size' })
  size: number;

  @AutoMap()
  @Column({ length: 255, name: 'path' })
  path: string;

  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'url' })
  url: string;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'metadata' })
  metadata: Record<string, any>;

  // Related entities
  @AutoMap()
  @Column({ length: 50, name: 'entity_type' })
  @Index()
  entityType: string;

  @AutoMap()
  @Column('uuid', { name: 'entity_id' })
  @Index()
  entityId: string;

  // Timestamps
  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  // User who uploaded the file
  @AutoMap()
  @Column('uuid', { nullable: true, name: 'created_by' })
  createdBy: string | null;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'updated_by' })
  updatedBy: string | null;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser: UserEntity;

  // Status
  @AutoMap()
  @Column({
    type: 'enum',
    enum: FileUploadStatus,
    default: FileUploadStatus.Active,
    name: 'status',
  })
  status: FileUploadStatus;
}
