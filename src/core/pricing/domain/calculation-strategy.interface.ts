import { IOrder } from './order.interface';
import { IPriceModifier } from './price-modifier.interface';

export interface ICalculationStrategy {
  /**
   * Performs the price calculation based on the previously set parameters
   * @returns The calculated price amount
   */
  calculate(): number;

  /**
   * Sets the necessary parameters for the calculation
   * @param order The order data
   * @param modifier The price modifier configuration
   */
  setParams(order: IOrder, modifier: IPriceModifier): void;

  /**
   * Returns the type of strategy
   */
  getStrategyType(): string;
}
