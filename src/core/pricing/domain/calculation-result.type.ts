import { CalculationType } from '@core/pricing/domain/price-modifier.interface';

export interface IModifierResult {
  id: string;
  name: string;
  type: CalculationType;
  amount: number;
  isGroupResult: boolean;
  children?: IModifierResult[];
}

export interface ICalculationResult {
  orderId: string;
  basePrice: number;
  modifiers: IModifierResult[];
  totalPrice: number;
  errors: string[];
}
