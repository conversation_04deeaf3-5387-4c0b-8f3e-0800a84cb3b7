import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StrategyRegistryService } from './calculation/strategies/registry/strategy-registry.service';
import { IOrder } from './domain/order.interface';
import {
  GroupBehavior,
  IPriceModifier,
} from './domain/price-modifier.interface';
import {
  ICalculationResult,
  IModifierResult,
} from './domain/calculation-result.type';
import {
  InvalidModifierDataException,
  MaxNestingLevelExceededException,
  PriceCalculationException,
} from './calculation/exceptions/calculation.exceptions';

@Injectable()
export class PriceCalculatorService {
  private readonly logger = new Logger(PriceCalculatorService.name);
  private readonly maxNestingLevel: number;

  constructor(
    private readonly strategyRegistry: StrategyRegistryService,
    private readonly configService: ConfigService,
  ) {
    // eslint-disable-next-line no-restricted-syntax
    this.maxNestingLevel = this.configService.get<number>(
      'pricing.maxNestingLevel',
      10,
    );
    this.logger.log(
      `Initialized with max nesting level: ${this.maxNestingLevel}`,
    );
  }

  /**
   * Calculates the final price for an order with the given modifiers
   * @param order The order data
   * @param modifiers The price modifiers to apply
   * @returns The calculation result
   */
  calculatePrice(
    order: IOrder,
    modifiers: IPriceModifier[],
  ): ICalculationResult {
    this.logger.debug(
      `Calculating price for order: ${order.id} with ${modifiers.length} modifiers`,
    );
    try {
      const result: ICalculationResult = {
        orderId: order.id,
        basePrice: order.basePrice,
        modifiers: [],
        totalPrice: order.basePrice,
        errors: [],
      };

      // Process each modifier
      for (const modifier of modifiers) {
        try {
          const modifierResult = this.processModifier(order, modifier);
          result.modifiers.push(modifierResult);
          result.totalPrice += modifierResult.amount;
        } catch (error) {
          const errorMessage = `Error processing modifier ${modifier.id}: ${error.message}`;
          this.logger.error(errorMessage);
          result.errors.push(errorMessage);
        }
      }

      this.logger.debug(
        `Calculation complete. Base price: ${result.basePrice}, Total: ${result.totalPrice}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`Calculation failed: ${error.message}`, error.stack);
      throw new PriceCalculationException(
        `Failed to calculate price for order ${order.id}`,
        error,
      );
    }
  }

  /**
   * Processes a single price modifier
   * @param order The order data
   * @param modifier The price modifier to apply
   * @param nestingLevel Current nesting level for group modifiers
   * @returns The modifier calculation result
   */
  private processModifier(
    order: IOrder,
    modifier: IPriceModifier,
    nestingLevel: number = 0,
  ): IModifierResult {
    if (nestingLevel > this.maxNestingLevel) {
      throw new MaxNestingLevelExceededException(this.maxNestingLevel);
    }

    if (!modifier) {
      throw new InvalidModifierDataException(
        'undefined',
        'Modifier is undefined',
      );
    }

    if (!modifier.isEnabled) {
      return {
        id: modifier.id,
        name: modifier.name,
        type: modifier.calculationType,
        amount: 0,
        isGroupResult: modifier.isGroupModifier,
      };
    }

    if (modifier.isGroupModifier) {
      return this.processGroupModifier(order, modifier, nestingLevel);
    } else {
      return this.processIndividualModifier(order, modifier);
    }
  }

  /**
   * Processes a group modifier
   */
  private processGroupModifier(
    order: IOrder,
    groupModifier: IPriceModifier,
    nestingLevel: number,
  ): IModifierResult {
    if (!groupModifier.modifiers || groupModifier.modifiers.length === 0) {
      return {
        id: groupModifier.id,
        name: groupModifier.name,
        type: groupModifier.calculationType,
        amount: 0,
        isGroupResult: true,
        children: [],
      };
    }

    const childResults: IModifierResult[] = [];
    let groupAmount = 0;

    // Process each child modifier
    for (const childModifier of groupModifier.modifiers) {
      try {
        const childResult = this.processModifier(
          order,
          childModifier,
          nestingLevel + 1,
        );
        childResults.push(childResult);

        // Apply group behavior
        switch (groupModifier.groupBehavior) {
          case GroupBehavior.USE_SUM:
            groupAmount += childResult.amount;
            break;
          case GroupBehavior.USE_HIGHEST:
            if (childResults.length === 1 || childResult.amount > groupAmount) {
              groupAmount = childResult.amount;
            }
            break;
          case GroupBehavior.USE_LOWEST:
            if (childResults.length === 1 || childResult.amount < groupAmount) {
              groupAmount = childResult.amount;
            }
            break;
          default:
            this.logger.warn(
              `Unknown group behavior: ${groupModifier.groupBehavior}, using sum`,
            );
            groupAmount += childResult.amount;
        }
      } catch (error) {
        this.logger.error(`Error processing child modifier: ${error.message}`);
        throw error;
      }
    }

    return {
      id: groupModifier.id,
      name: groupModifier.name,
      type: groupModifier.calculationType,
      amount: groupAmount,
      isGroupResult: true,
      children: childResults,
    };
  }

  /**
   * Processes an individual modifier
   */
  private processIndividualModifier(
    order: IOrder,
    modifier: IPriceModifier,
  ): IModifierResult {
    const strategy = this.strategyRegistry.getStrategy(
      modifier.calculationType,
    );

    strategy.setParams(order, modifier);
    const amount = strategy.calculate();

    return {
      id: modifier.id,
      name: modifier.name,
      type: modifier.calculationType,
      amount,
      isGroupResult: false,
    };
  }
}
