import { Module } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { StrategyRegistryService } from './calculation/strategies/registry/strategy-registry.service';
import { PriceCalculatorService } from './price-calculator.service';
import { FlatAmountStrategy } from './calculation/strategies/implementations/flat-amount.strategy';
import { CalculationUtilsService } from './calculation/utils/calculation-utils.service';
import { FlatPercentageStrategy } from '@core/pricing/calculation/strategies/implementations/flat-percentage.strategy';
import { FlatOverageAmountStrategy } from '@core/pricing/calculation/strategies/implementations/flat-overage-amount.strategy';
import { FlatOveragePercentageStrategy } from '@core/pricing/calculation/strategies/implementations/flat-overage-percentage.strategy';
import { IncrementalOverageAmountStrategy } from '@core/pricing/calculation/strategies/implementations/incremental-overage-amount.strategy';
import { IncrementalOveragePercentageStrategy } from '@core/pricing/calculation/strategies/implementations/incremental-overage-percentage.strategy';
import { TieredFixedOverageAmountStrategy } from '@core/pricing/calculation/strategies/implementations/tiered-fixed-overage-amount.strategy';
import { TieredFixedOveragePercentageStrategy } from '@core/pricing/calculation/strategies/implementations/tiered-fixed-overage-percentage.strategy';
import { TieredIncrementalOverageAmountStrategy } from '@core/pricing/calculation/strategies/implementations/tiered-incremental-overage-amount.strategy';
import { TieredIncrementalOveragePercentageStrategy } from '@core/pricing/calculation/strategies/implementations/tiered-incremental-overage-percentage.strategy';

@Module({
  imports: [DiscoveryModule],
  providers: [
    StrategyRegistryService,
    PriceCalculatorService,
    CalculationUtilsService,
    FlatAmountStrategy,
    FlatPercentageStrategy,
    FlatOverageAmountStrategy,
    FlatOveragePercentageStrategy,
    IncrementalOverageAmountStrategy,
    IncrementalOveragePercentageStrategy,
    TieredFixedOverageAmountStrategy,
    TieredFixedOveragePercentageStrategy,
    TieredIncrementalOverageAmountStrategy,
    TieredIncrementalOveragePercentageStrategy,
  ],
  exports: [PriceCalculatorService],
})
export class PriceCalculatorModule {}
