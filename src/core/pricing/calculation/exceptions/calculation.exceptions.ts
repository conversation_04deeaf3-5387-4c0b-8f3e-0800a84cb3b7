import { HttpException, HttpStatus } from '@nestjs/common';

export class CalculationStrategyNotFoundException extends HttpException {
  constructor(strategyType: string) {
    super(
      `Calculation strategy not found: ${strategyType}`,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class PriceCalculationException extends HttpException {
  constructor(
    message: string,
    public readonly cause: unknown,
  ) {
    super(message, HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class InvalidModifierDataException extends HttpException {
  constructor(modifierId: string, message: string) {
    super(
      `Invalid modifier data (ID: ${modifierId}): ${message}`,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class MaxNestingLevelExceededException extends HttpException {
  constructor(maxLevel: number) {
    super(
      `Maximum nesting level (${maxLevel}) for modifiers exceeded`,
      HttpStatus.BAD_REQUEST,
    );
  }
}
