import { Injectable, Logger, OnModuleInit, Type } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { ICalculationStrategy } from '../../../domain/calculation-strategy.interface';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { STRATEGY_TYPE_METADATA } from '../decorators/strategy.decorator';
import { CalculationStrategyNotFoundException } from '../../exceptions/calculation.exceptions';

@Injectable()
export class StrategyRegistryService implements OnModuleInit {
  private readonly logger = new Logger(StrategyRegistryService.name);
  private strategies = new Map<string, Type<ICalculationStrategy>>();

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
  ) {}

  onModuleInit() {
    this.discoverStrategies();
  }

  /**
   * Discovers and registers all calculation strategies in the application
   */
  private discoverStrategies() {
    const providers = this.discoveryService.getProviders();

    providers
      .filter(
        (wrapper) =>
          wrapper.instance &&
          wrapper.instance instanceof BaseCalculationStrategy,
      )
      .forEach((wrapper) => {
        const { instance } = wrapper;
        const strategyType = this.reflector.get(
          STRATEGY_TYPE_METADATA,
          instance.constructor,
        );

        if (!strategyType) {
          this.logger.warn(
            `Strategy ${instance.constructor.name} has no type metadata. Skipping.`,
          );
          return;
        }

        if (this.strategies.has(strategyType)) {
          this.logger.warn(
            `Duplicate strategy type found: ${strategyType}. Overriding.`,
          );
        }

        this.strategies.set(
          strategyType,
          instance.constructor as Type<ICalculationStrategy>,
        );
        this.logger.log(
          `Registered strategy: ${strategyType} (${instance.constructor.name})`,
        );
      });

    this.logger.log(
      `Registered ${this.strategies.size} calculation strategies`,
    );
  }

  /**
   * Gets a strategy by type
   * @param type The strategy type
   * @returns The strategy instance
   * @throws CalculationStrategyNotFoundException if strategy not found
   */
  getStrategy(type: string): ICalculationStrategy {
    const strategyClass = this.strategies.get(type);

    if (!strategyClass) {
      this.logger.error(`Strategy not found for type: ${type}`);
      throw new CalculationStrategyNotFoundException(type);
    }

    // Return the instance from provider registry
    return this.discoveryService
      .getProviders()
      .find((provider) => provider.instance instanceof strategyClass)
      ?.instance as ICalculationStrategy;
  }

  /**
   * Gets all registered strategy types
   */
  getAllStrategyTypes(): string[] {
    return Array.from(this.strategies.keys());
  }
}
