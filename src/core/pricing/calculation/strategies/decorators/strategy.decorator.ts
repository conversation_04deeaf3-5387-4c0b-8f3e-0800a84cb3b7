import { Injectable } from '@nestjs/common';

export const STRATEGY_TYPE_METADATA = 'pricing:strategy_type';

/**
 * Decorator that marks a class as a pricing calculation strategy
 * @param type The calculation type this strategy implements
 */
export function PricingStrategy(type: string) {
  return (target: any) => {
    Reflect.defineMetadata(STRATEGY_TYPE_METADATA, type, target);
    return Injectable()(target);
  };
}
