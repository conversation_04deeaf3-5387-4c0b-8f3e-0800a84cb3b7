import { Logger } from '@nestjs/common';
import { ICalculationStrategy } from '../../domain/calculation-strategy.interface';
import { IOrder } from '../../domain/order.interface';
import {
  CalculationField,
  IPriceModifier,
} from '../../domain/price-modifier.interface';
import { STRATEGY_TYPE_METADATA } from './decorators/strategy.decorator';

export abstract class BaseCalculationStrategy implements ICalculationStrategy {
  protected readonly logger: Logger;

  constructor() {
    this.logger = new Logger(this.constructor.name);
  }

  /**
   * Get the field value from the order based on the specified calculation field
   */
  protected getFieldValue(order: IOrder, fieldName: CalculationField): number {
    this.logger.debug(`Getting field value for: ${fieldName}`);

    switch (fieldName) {
      case CalculationField.BASE_PRICE:
        // Base price in currency units
        return order.basePrice;
      case CalculationField.DECLARED_PRICE:
        // Declared value in currency units (for insurance purposes)
        return order.declaredPrice;
      case CalculationField.WEIGHT:
        // Weight in kilograms (kg)
        return order.weight || 0;
      case CalculationField.DISTANCE:
        // Distance in kilometers (km)
        return order.distance || 0;
      case CalculationField.QUANTITY:
        // Number of items (unitless)
        return order.quantity || 0;
      case CalculationField.HEIGHT:
        // Height in centimeters (cm)
        return typeof order.height === 'number' ? order.height : 0;
      case CalculationField.WIDTH:
        // Width in centimeters (cm)
        return typeof order.width === 'number' ? order.width : 0;
      case CalculationField.LENGTH:
        // Length in centimeters (cm)
        return typeof order.length === 'number' ? order.length : 0;
      case CalculationField.CUBIC_DIMENSIONS:
        // Volume in cubic centimeters (cm³) - calculated as Height × Width × Length
        if (typeof order.cubicDimensions === 'number') {
          return order.cubicDimensions;
        }

        // Try to calculate from individual dimensions if they are numbers
        const height = typeof order.height === 'number' ? order.height : 0;
        const width = typeof order.width === 'number' ? order.width : 0;
        const length = typeof order.length === 'number' ? order.length : 0;

        if (height && width && length) {
          return height * width * length;
        }

        return 0;
      case CalculationField.CUSTOM_AMOUNT:
        // Custom amount in currency units (for special calculations)
        return typeof order.customAmount === 'number' ? order.customAmount : 0;
      case CalculationField.COLLECTION_WAIT_TIME:
        // Collection waiting time in minutes (min)
        return typeof order.collectionWaitTime === 'number'
          ? order.collectionWaitTime
          : 0;
      case CalculationField.DELIVERY_WAIT_TIME:
        // Delivery waiting time in minutes (min)
        return typeof order.deliveryWaitTime === 'number'
          ? order.deliveryWaitTime
          : 0;
      default:
        this.logger.warn(`Unknown field name: ${fieldName}, returning 0`);
        return 0;
    }
  }

  /**
   * Returns the strategy type as defined by the decorator
   */
  getStrategyType(): string {
    const type = Reflect.getMetadata(STRATEGY_TYPE_METADATA, this.constructor);

    if (!type) {
      this.logger.error(
        `No strategy type metadata found for ${this.constructor.name}`,
      );
      throw new Error(`Strategy type not defined for ${this.constructor.name}`);
    }

    return type;
  }

  abstract calculate(): number;
  abstract setParams(order: IOrder, modifier: IPriceModifier): void;
}
