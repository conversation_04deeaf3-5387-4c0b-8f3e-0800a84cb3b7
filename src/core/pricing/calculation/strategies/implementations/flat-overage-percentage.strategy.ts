import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.FLAT_OVERAGE_PERCENTAGE)
@Injectable()
export class FlatOveragePercentageStrategy extends BaseCalculationStrategy {
  private percentage: number = 0;
  private calculationValue: number = 0;
  private basePrice: number = 0;
  private isEnabled: boolean = true;
  private applicableRange?: any;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for FlatOveragePercentage: ${modifier.id}`,
    );

    // Get the field value based on calculation field
    this.calculationValue = this.getFieldValue(
      order,
      modifier.calculationField,
    );

    // Store the base price for percentage calculation
    this.basePrice = order.basePrice;

    // Set the percentage value
    this.percentage = Number(modifier.value);

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    // Set applicable range if present
    this.applicableRange = modifier.applicableRange;

    this.logger.debug(
      `FlatOveragePercentage parameters set: percentage=${this.percentage}, fieldValue=${this.calculationValue}, basePrice=${this.basePrice}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'FlatOveragePercentage calculation skipped: modifier disabled',
      );
      return 0;
    }

    // Check if value is within applicable range
    if (this.applicableRange) {
      if (
        !this.calculationUtils.isInRange(
          this.calculationValue,
          this.applicableRange,
        )
      ) {
        this.logger.debug(
          'FlatOveragePercentage calculation skipped: value outside applicable range',
        );
        return 0;
      }
    }

    // Calculate percentage of base price
    if (this.basePrice < 0) {
      this.logger.debug(
        'FlatOveragePercentage calculation skipped: negative base price',
      );
      return 0;
    }

    const result = (this.basePrice * this.percentage) / 100;
    this.logger.debug(`FlatOveragePercentage calculation result: ${result}`);
    return result;
  }
}
