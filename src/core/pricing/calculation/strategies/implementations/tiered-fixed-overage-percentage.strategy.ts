import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
  ITieredRange,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.TIERED_FIXED_OVERAGE_PERCENTAGE)
@Injectable()
export class TieredFixedOveragePercentageStrategy extends BaseCalculationStrategy {
  private calculationValue: number = 0;
  private tieredRanges: ITieredRange[] = [];
  private isEnabled: boolean = true;
  private applicableRange?: any;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for TieredFixedOveragePercentage: ${modifier.id}`,
    );

    // Get the value from the specified field
    this.calculationValue = this.getFieldValue(
      order,
      modifier.calculationField,
    );

    // Set the tiered ranges
    this.tieredRanges = modifier.tieredRanges || [];

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    // Set applicable range if present
    this.applicableRange = modifier.applicableRange;

    this.logger.debug(
      `TieredFixedOveragePercentage parameters set: calculationValue=${this.calculationValue}, ` +
        `tiers=${this.tieredRanges.length}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'TieredFixedOveragePercentage calculation skipped: modifier disabled',
      );
      return 0;
    }

    // Check if value is within applicable range
    if (this.applicableRange) {
      if (
        !this.calculationUtils.isInRange(
          this.calculationValue,
          this.applicableRange,
        )
      ) {
        this.logger.debug(
          'TieredFixedOveragePercentage calculation skipped: value outside applicable range',
        );
        return 0;
      }
    }

    if (!this.tieredRanges || this.tieredRanges.length === 0) {
      this.logger.debug(
        'TieredFixedOveragePercentage calculation skipped: no tiered ranges defined',
      );
      return 0;
    }

    // Evaluate which tier the value falls into and get the corresponding percentage
    const percentage = this.calculationUtils.evaluateTieredRange(
      this.calculationValue,
      this.tieredRanges,
    );

    if (percentage <= 0) {
      this.logger.debug(
        'TieredFixedOveragePercentage calculation skipped: no matching tier or zero percentage',
      );
      return 0;
    }

    // Apply the percentage to the calculation value
    const result = (this.calculationValue * percentage) / 100;

    this.logger.debug(
      `TieredFixedOveragePercentage calculation: ` +
        `tier percentage=${percentage}%, ` +
        `result=${result}`,
    );

    return result;
  }
}
