import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';

@PricingStrategy(CalculationType.FLAT_AMOUNT)
export class FlatAmountStrategy extends BaseCalculationStrategy {
  private amount: number = 0;
  private isEnabled: boolean = true;

  /**
   * Sets the parameters needed for the calculation
   * @param _order
   * @param modifier Price modifier configuration
   */
  setParams(_order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(`Setting parameters for FlatAmount: ${modifier.id}`);

    this.amount = Number(modifier.value);
    this.isEnabled = modifier.isEnabled;

    this.logger.debug(
      `FlatAmount parameters set: amount=${this.amount}, enabled=${this.isEnabled}`,
    );
  }

  /**
   * Calculates the flat amount to be applied
   * @returns The flat amount if enabled, 0 otherwise
   */
  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug('FlatAmount calculation skipped: modifier disabled');
      return 0;
    }

    this.logger.debug(`FlatAmount calculation result: ${this.amount}`);
    return this.amount;
  }
}
