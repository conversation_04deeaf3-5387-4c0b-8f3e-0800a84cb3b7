import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
  ITieredRange,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.TIERED_INCREMENTAL_OVERAGE_AMOUNT)
@Injectable()
export class TieredIncrementalOverageAmountStrategy extends BaseCalculationStrategy {
  private calculationValue: number = 0;
  private calculationStartAfter: number = 0;
  private increment: number = 1;
  private tieredRanges: ITieredRange[] = [];
  private isEnabled: boolean = true;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for TieredIncrementalOverageAmount: ${modifier.id}`,
    );

    // Get the value from the specified field
    this.calculationValue = this.getFieldValue(
      order,
      modifier.calculationField,
    );

    // Set the tiered ranges
    this.tieredRanges = modifier.tieredRanges || [];

    // Set the calculation threshold
    this.calculationStartAfter = Number(modifier.calculationStartAfter) || 0;

    // Set the increment value
    this.increment = Number(modifier.increment) || 1;

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    this.logger.debug(
      `TieredIncrementalOverageAmount parameters set: calculationValue=${this.calculationValue}, ` +
        `threshold=${this.calculationStartAfter}, increment=${this.increment}, ` +
        `tiers=${this.tieredRanges.length}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'TieredIncrementalOverageAmount calculation skipped: modifier disabled',
      );
      return 0;
    }

    if (!this.tieredRanges || this.tieredRanges.length === 0) {
      this.logger.debug(
        'TieredIncrementalOverageAmount calculation skipped: no tiered ranges defined',
      );
      return 0;
    }

    // Evaluate which tier the value falls into and get the corresponding amount per increment
    const amountPerIncrement = this.calculationUtils.evaluateTieredRange(
      this.calculationValue,
      this.tieredRanges,
    );

    if (amountPerIncrement <= 0) {
      this.logger.debug(
        'TieredIncrementalOverageAmount calculation skipped: no matching tier or zero amount',
      );
      return 0;
    }

    // Calculate the value above threshold
    const valueOverThreshold = Math.max(
      0,
      this.calculationValue - this.calculationStartAfter,
    );

    if (valueOverThreshold <= 0) {
      this.logger.debug(
        'TieredIncrementalOverageAmount calculation skipped: value not exceeding threshold',
      );
      return 0;
    }

    // Calculate number of increments
    const numberOfIncrements = Math.ceil(valueOverThreshold / this.increment);

    // Calculate final amount
    const result = numberOfIncrements * amountPerIncrement;

    this.logger.debug(
      `TieredIncrementalOverageAmount calculation: ` +
        `${valueOverThreshold} over threshold, ` +
        `${numberOfIncrements} increments at ${amountPerIncrement} each, ` +
        `result=${result}`,
    );

    return result;
  }
}
