import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationField,
  CalculationType,
  IPriceModifier,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';

@PricingStrategy(CalculationType.FLAT_PERCENTAGE)
export class FlatPercentageStrategy extends BaseCalculationStrategy {
  private percentage: number = 0;
  private field: CalculationField = CalculationField.BASE_PRICE;
  private isEnabled: boolean = true;
  private order: IOrder;

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.order = order;
    this.logger.debug(`Setting parameters for FlatPercentage: ${modifier.id}`);
    this.percentage = Number(modifier.value);
    this.field = modifier.calculationField;
    this.isEnabled = modifier.isEnabled;
    this.logger.debug(
      `FlatPercentage parameters set: percentage=${this.percentage}, field=${this.field}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'FlatPercentage calculation skipped: modifier disabled',
      );
      return 0;
    }

    const fieldValue = this.getFieldValue(this.order, this.field);
    const result = (fieldValue * this.percentage) / 100;

    this.logger.debug(`FlatPercentage calculation result: ${result}`);
    return result;
  }
}
