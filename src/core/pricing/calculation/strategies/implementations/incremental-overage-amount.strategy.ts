import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.INCREMENTAL_OVERAGE_AMOUNT)
@Injectable()
export class IncrementalOverageAmountStrategy extends BaseCalculationStrategy {
  private originalValue: number = 0;
  private calculationValue: number = 0;
  private calculationStartAfter: number = 0;
  private increment: number = 1;
  private amountPerIncrement: number = 0;
  private isEnabled: boolean = true;
  private applicableRange?: any;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for IncrementalOverageAmount: ${modifier.id}`,
    );
    // Get the original field value
    this.originalValue = this.getFieldValue(order, modifier.calculationField);

    // Set the increment value (default to 1 if not specified)
    this.increment = Number(modifier.increment) || 1;

    // Set the amount per increment
    this.amountPerIncrement = Number(modifier.value);

    // Set the calculation threshold
    this.calculationStartAfter = Number(modifier.calculationStartAfter) || 0;

    // Calculate the value above threshold
    this.calculationValue = Math.max(
      0,
      this.originalValue - this.calculationStartAfter,
    );

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    // Set applicable range if present
    this.applicableRange = modifier.applicableRange;

    this.logger.debug(
      `IncrementalOverageAmount parameters set: originalValue=${this.originalValue}, ` +
        `threshold=${this.calculationStartAfter}, increment=${this.increment}, ` +
        `amountPerIncrement=${this.amountPerIncrement}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'IncrementalOverageAmount calculation skipped: modifier disabled',
      );
      return 0;
    }

    // Check if original value is within applicable range
    if (this.applicableRange) {
      if (
        !this.calculationUtils.isInRange(
          this.originalValue,
          this.applicableRange,
        )
      ) {
        this.logger.debug(
          'IncrementalOverageAmount calculation skipped: value outside applicable range',
        );
        return 0;
      }
    }

    // If value doesn't exceed the threshold, no charge
    if (this.calculationValue <= 0) {
      this.logger.debug(
        'IncrementalOverageAmount calculation skipped: value below threshold',
      );
      return 0;
    }

    // Calculate number of increments and resulting charge
    const numberOfIncrements = Math.ceil(
      this.calculationValue / this.increment,
    );
    const result = numberOfIncrements * this.amountPerIncrement;

    this.logger.debug(
      `IncrementalOverageAmount calculation: ` +
        `${this.calculationValue} over threshold, ` +
        `${numberOfIncrements} increments, ` +
        `result=${result}`,
    );

    return result;
  }
}
