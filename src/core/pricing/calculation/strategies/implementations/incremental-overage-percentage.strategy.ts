import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.INCREMENTAL_OVERAGE_PERCENTAGE)
@Injectable()
export class IncrementalOveragePercentageStrategy extends BaseCalculationStrategy {
  private fieldValue: number = 0;
  private basePrice: number = 0;
  private percentagePerIncrement: number = 0;
  private increment: number = 1;
  private isEnabled: boolean = true;
  private applicableRange?: any;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for IncrementalOveragePercentage: ${modifier.id}`,
    );

    // Get the value from the specified field
    this.fieldValue = this.getFieldValue(order, modifier.calculationField);

    // Store the base price
    this.basePrice = order.basePrice;

    // Set the percentage per increment
    this.percentagePerIncrement = Number(modifier.value);

    // Set the increment value (default to 1 if not specified)
    this.increment = Number(modifier.increment) || 1;

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    // Set applicable range if present
    this.applicableRange = modifier.applicableRange;

    this.logger.debug(
      `IncrementalOveragePercentage parameters set: fieldValue=${this.fieldValue}, ` +
        `basePrice=${this.basePrice}, percentagePerIncrement=${this.percentagePerIncrement}, ` +
        `increment=${this.increment}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'IncrementalOveragePercentage calculation skipped: modifier disabled',
      );
      return 0;
    }

    // Check if value is within applicable range
    if (this.applicableRange) {
      if (
        !this.calculationUtils.isInRange(this.fieldValue, this.applicableRange)
      ) {
        this.logger.debug(
          'IncrementalOveragePercentage calculation skipped: value outside applicable range',
        );
        return 0;
      }
    }

    // Calculate number of increments
    const numberOfIncrements = Math.ceil(this.fieldValue / this.increment);

    // Calculate total percentage
    const totalPercentage = this.percentagePerIncrement * numberOfIncrements;

    // Calculate result
    const result = (this.basePrice * totalPercentage) / 100;

    this.logger.debug(
      `IncrementalOveragePercentage calculation: ` +
        `${numberOfIncrements} increments, ` +
        `${totalPercentage}% total, ` +
        `result=${result}`,
    );

    return result;
  }
}
