import { Injectable } from '@nestjs/common';
import { BaseCalculationStrategy } from '../base-strategy.abstract';
import { IOrder } from '../../../domain/order.interface';
import {
  CalculationType,
  IPriceModifier,
} from '../../../domain/price-modifier.interface';
import { PricingStrategy } from '../decorators/strategy.decorator';
import { CalculationUtilsService } from '../../utils/calculation-utils.service';

@PricingStrategy(CalculationType.FLAT_OVERAGE_AMOUNT)
@Injectable()
export class FlatOverageAmountStrategy extends BaseCalculationStrategy {
  private amount: number = 0;
  private calculationValue: number = 0;
  private isEnabled: boolean = true;
  private applicableRange?: any;

  constructor(private readonly calculationUtils: CalculationUtilsService) {
    super();
  }

  setParams(order: IOrder, modifier: IPriceModifier): void {
    this.logger.debug(
      `Setting parameters for FlatOverageAmount: ${modifier.id}`,
    );

    // Get the field value based on calculation field
    this.calculationValue = this.getFieldValue(
      order,
      modifier.calculationField,
    );

    // Set the amount value
    this.amount = Number(modifier.value);

    // Set enabled status
    this.isEnabled = modifier.isEnabled;

    // Set applicable range if present
    this.applicableRange = modifier.applicableRange;

    this.logger.debug(
      `FlatOverageAmount parameters set: amount=${this.amount}, fieldValue=${this.calculationValue}, enabled=${this.isEnabled}`,
    );
  }

  calculate(): number {
    if (!this.isEnabled) {
      this.logger.debug(
        'FlatOverageAmount calculation skipped: modifier disabled',
      );
      return 0;
    }

    // Check if value is within applicable range
    if (this.applicableRange) {
      if (
        !this.calculationUtils.isInRange(
          this.calculationValue,
          this.applicableRange,
        )
      ) {
        this.logger.debug(
          'FlatOverageAmount calculation skipped: value outside applicable range',
        );
        return 0;
      }
    }

    this.logger.debug(`FlatOverageAmount calculation result: ${this.amount}`);
    return this.amount;
  }
}
