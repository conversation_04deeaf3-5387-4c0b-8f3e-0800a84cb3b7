import { Injectable, Logger } from '@nestjs/common';
import { PaymentRepository } from '../infrastructure/repositories/payment.repository';
import { PaymentMethodRepository } from '../infrastructure/repositories/payment-method.repository';
import { PaymentProviderRepository } from '../infrastructure/repositories/payment-provider.repository';
import { StripeService } from './providers/stripe.service';
import { Payment } from '../domain/payment';
import { PaymentMethod } from '../domain/payment-method';
import {
  PaymentMethodType,
  PaymentProvider,
  PaymentResult,
  PaymentStatus,
  PaymentTriggerType,
  RefundResult,
  RefundStatus,
} from '../domain/payment.types';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    private readonly paymentRepository: PaymentRepository,
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly paymentProviderRepository: PaymentProviderRepository,
    private readonly stripeService: StripeService,
  ) {}

  /**
   * Create a new payment
   */
  async createPayment(
    tenantId: string,
    userId: string,
    amount: number,
    currency: string,
    entityType: string,
    entityId: string,
    customerId: string,
    customerEmail: string,
    customerName: string,
    paymentMethodId?: string,
    description?: string,
    metadata?: Record<string, any>,
    triggerType: PaymentTriggerType = PaymentTriggerType.Manual,
  ): Promise<Payment> {
    this.logger.log(
      `Creating payment for ${entityType} ${entityId} with amount ${amount} ${currency}`,
    );

    // Determine payment method type
    let paymentMethodType = PaymentMethodType.CreditCard;
    let paymentMethod: PaymentMethod | null = null;

    if (paymentMethodId) {
      paymentMethod =
        await this.paymentMethodRepository.findById(paymentMethodId);
      if (paymentMethod) {
        paymentMethodType = paymentMethod.type;
      }
    }

    // Create payment record in pending status
    const payment = new Payment();
    payment.tenantId = tenantId;
    payment.amount = amount;
    payment.currency = currency;
    payment.status = PaymentStatus.Pending;
    payment.provider = PaymentProvider.Stripe; // Default to Stripe for now
    payment.paymentMethodId = paymentMethodId;
    payment.paymentMethodType = paymentMethodType;
    payment.triggerType = triggerType;
    payment.description = description;
    payment.metadata = metadata || {};
    payment.entityType = entityType;
    payment.entityId = entityId;
    payment.customerId = customerId;
    payment.customerEmail = customerEmail;
    payment.customerName = customerName;
    payment.createdBy = userId;

    // Save the payment record
    const savedPayment = await this.paymentRepository.create(payment);

    // If we have a payment method, process the payment
    if (paymentMethodId && paymentMethod) {
      await this.processPayment(savedPayment.id, userId);
    }

    return savedPayment;
  }

  /**
   * Process a payment using the specified provider
   */
  async processPayment(
    paymentId: string,
    userId: string,
  ): Promise<PaymentResult> {
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      throw new Error(`Payment with ID ${paymentId} not found`);
    }

    // Update payment status to processing
    await this.paymentRepository.updateStatus(
      paymentId,
      PaymentStatus.Processing,
    );

    let result: PaymentResult;

    try {
      // Process payment based on provider
      if (payment.provider === PaymentProvider.Stripe) {
        // Get payment method if available
        let stripePaymentMethodId: string | null = null;
        if (payment.paymentMethodId) {
          const paymentMethod = await this.paymentMethodRepository.findById(
            payment.paymentMethodId,
          );
          if (paymentMethod && paymentMethod.providerMethodId) {
            stripePaymentMethodId = paymentMethod.providerMethodId;
          }
        }

        // Create metadata for Stripe
        const stripeMetadata = {
          paymentId: payment.id,
          entityType: payment.entityType,
          entityId: payment.entityId,
          tenantId: payment.tenantId,
          ...payment.metadata,
        };

        // Create payment intent with Stripe
        result = await this.stripeService.createPaymentIntent(
          payment.amount,
          payment.currency,
          payment.customerId,
          stripeMetadata,
          stripePaymentMethodId || undefined,
        );

        // Update payment with Stripe payment intent ID
        if (result.success) {
          await this.paymentRepository.update(paymentId, {
            providerTransactionId: result.paymentId,
            metadata: {
              ...payment.metadata,
              stripePaymentIntentId: result.paymentId,
              clientSecret: result.metadata?.clientSecret,
            },
          });

          // If payment is completed, update status
          if (result.status === 'Completed') {
            await this.paymentRepository.updateStatus(
              paymentId,
              PaymentStatus.Completed,
              {
                receiptUrl: result.metadata?.receiptUrl,
              },
            );
          }
        } else {
          // Payment failed
          await this.paymentRepository.updateStatus(
            paymentId,
            PaymentStatus.Failed,
            {
              errorMessage: result.message,
              errorCode: result.errorCode,
            },
          );
        }
      } else {
        throw new Error(`Unsupported payment provider: ${payment.provider}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing payment ${paymentId}: ${error.message}`,
        error.stack,
      );

      // Update payment status to failed
      await this.paymentRepository.updateStatus(
        paymentId,
        PaymentStatus.Failed,
        {
          errorMessage: error.message,
        },
      );

      result = {
        success: false,
        status: PaymentStatus.Failed,
        message: error.message,
      };
    }

    return result;
  }

  /**
   * Confirm a payment (for client-side payment flows)
   */
  async confirmPayment(
    paymentId: string,
    userId: string,
    paymentMethodId?: string,
  ): Promise<PaymentResult> {
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      throw new Error(`Payment with ID ${paymentId} not found`);
    }

    if (!payment.providerTransactionId) {
      throw new Error(`Payment ${paymentId} has no provider transaction ID`);
    }

    let result: PaymentResult;

    try {
      // Confirm payment based on provider
      if (payment.provider === PaymentProvider.Stripe) {
        // Get payment method if available
        let stripePaymentMethodId: string | null = null;
        if (paymentMethodId) {
          const paymentMethod =
            await this.paymentMethodRepository.findById(paymentMethodId);
          if (paymentMethod && paymentMethod.providerMethodId) {
            stripePaymentMethodId = paymentMethod.providerMethodId;
          }
        }

        // Confirm payment intent with Stripe
        result = await this.stripeService.confirmPaymentIntent(
          payment.providerTransactionId,
          stripePaymentMethodId || undefined,
        );

        // Update payment status based on result
        if (result.success) {
          if (result.status === 'Completed') {
            await this.paymentRepository.updateStatus(
              paymentId,
              PaymentStatus.Completed,
            );
          } else {
            await this.paymentRepository.updateStatus(
              paymentId,
              PaymentStatus.Processing,
              {
                stripePaymentStatus: result.metadata?.stripePaymentStatus,
              },
            );
          }
        } else {
          // Payment failed
          await this.paymentRepository.updateStatus(
            paymentId,
            PaymentStatus.Failed,
            {
              errorMessage: result.message,
              errorCode: result.errorCode,
            },
          );
        }
      } else {
        throw new Error(`Unsupported payment provider: ${payment.provider}`);
      }
    } catch (error) {
      this.logger.error(
        `Error confirming payment ${paymentId}: ${error.message}`,
        error.stack,
      );

      // Update payment status to failed
      await this.paymentRepository.updateStatus(
        paymentId,
        PaymentStatus.Failed,
        {
          errorMessage: error.message,
        },
      );

      result = {
        success: false,
        status: PaymentStatus.Failed,
        message: error.message,
      };
    }

    return result;
  }

  /**
   * Check payment status with provider
   */
  async checkPaymentStatus(paymentId: string): Promise<PaymentResult> {
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      throw new Error(`Payment with ID ${paymentId} not found`);
    }

    if (!payment.providerTransactionId) {
      throw new Error(`Payment ${paymentId} has no provider transaction ID`);
    }

    let result: PaymentResult;

    try {
      // Check payment status based on provider
      if (payment.provider === PaymentProvider.Stripe) {
        // Retrieve payment intent from Stripe
        result = await this.stripeService.retrievePaymentIntent(
          payment.providerTransactionId,
        );

        // Update payment status based on result
        if (result.success) {
          if (result.status === 'Completed') {
            await this.paymentRepository.updateStatus(
              paymentId,
              PaymentStatus.Completed,
              {
                receiptUrl: result.metadata?.receiptUrl,
              },
            );
          } else if (result.status === 'Cancelled') {
            await this.paymentRepository.updateStatus(
              paymentId,
              PaymentStatus.Cancelled,
              {
                stripePaymentStatus: result.metadata?.stripePaymentStatus,
              },
            );
          } else {
            await this.paymentRepository.updateStatus(
              paymentId,
              PaymentStatus.Processing,
              {
                stripePaymentStatus: result.metadata?.stripePaymentStatus,
              },
            );
          }
        }
      } else {
        throw new Error(`Unsupported payment provider: ${payment.provider}`);
      }
    } catch (error) {
      this.logger.error(
        `Error checking payment status ${paymentId}: ${error.message}`,
        error.stack,
      );

      result = {
        success: false,
        status: payment.status,
        message: error.message,
      };
    }

    return result;
  }

  /**
   * Cancel a payment
   */
  async cancelPayment(
    paymentId: string,
    userId: string,
    reason?: string,
  ): Promise<PaymentResult> {
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      throw new Error(`Payment with ID ${paymentId} not found`);
    }

    // Only pending or processing payments can be cancelled
    if (
      payment.status !== PaymentStatus.Pending &&
      payment.status !== PaymentStatus.Processing
    ) {
      throw new Error(`Cannot cancel payment in ${payment.status} status`);
    }

    let result: PaymentResult;

    try {
      // If payment has a provider transaction ID, cancel with provider
      if (payment.providerTransactionId) {
        if (payment.provider === PaymentProvider.Stripe) {
          // Cancel payment intent with Stripe
          result = await this.stripeService.cancelPaymentIntent(
            payment.providerTransactionId,
            reason,
          );
        } else {
          throw new Error(`Unsupported payment provider: ${payment.provider}`);
        }
      } else {
        // No provider transaction ID, just update status
        result = {
          success: true,
          paymentId: payment.id,
          status: PaymentStatus.Cancelled,
        };
      }

      // Update payment status to cancelled
      await this.paymentRepository.updateStatus(
        paymentId,
        PaymentStatus.Cancelled,
        {
          cancellationReason: reason,
          updatedBy: userId,
        },
      );
    } catch (error) {
      this.logger.error(
        `Error cancelling payment ${paymentId}: ${error.message}`,
        error.stack,
      );

      result = {
        success: false,
        status: payment.status,
        message: error.message,
      };
    }

    return result;
  }

  /**
   * Refund a payment
   */
  async refundPayment(
    paymentId: string,
    userId: string,
    amount?: number,
    reason?: string,
  ): Promise<RefundResult> {
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      throw new Error(`Payment with ID ${paymentId} not found`);
    }

    // Only completed payments can be refunded
    if (payment.status !== PaymentStatus.Completed) {
      throw new Error(`Cannot refund payment in ${payment.status} status`);
    }

    if (!payment.providerTransactionId) {
      throw new Error(`Payment ${paymentId} has no provider transaction ID`);
    }

    let result: RefundResult;

    try {
      // Process refund based on provider
      if (payment.provider === PaymentProvider.Stripe) {
        // Create metadata for Stripe
        const stripeMetadata = {
          paymentId: payment.id,
          entityType: payment.entityType,
          entityId: payment.entityId,
          tenantId: payment.tenantId,
          refundedBy: userId,
          ...payment.metadata,
        };

        // Create refund with Stripe
        result = await this.stripeService.createRefund(
          payment.providerTransactionId,
          amount,
          reason,
          stripeMetadata,
        );

        // Update payment status based on refund result
        if (result.success) {
          // If full refund, update to Refunded, otherwise PartiallyRefunded
          const refundStatus =
            !amount || amount >= payment.amount
              ? PaymentStatus.Refunded
              : PaymentStatus.PartiallyRefunded;

          await this.paymentRepository.updateStatus(paymentId, refundStatus, {
            refundId: result.refundId,
            refundAmount: amount || payment.amount,
            refundReason: reason,
            refundedBy: userId,
            refundedAt: new Date(),
          });
        }
      } else {
        throw new Error(`Unsupported payment provider: ${payment.provider}`);
      }
    } catch (error) {
      this.logger.error(
        `Error refunding payment ${paymentId}: ${error.message}`,
        error.stack,
      );

      result = {
        success: false,
        status: RefundStatus.Failed,
        message: error.message,
      };
    }

    return result;
  }

  /**
   * Get payment by ID
   */
  async getPaymentById(paymentId: string): Promise<Payment> {
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      throw new Error(`Payment with ID ${paymentId} not found`);
    }
    return payment;
  }

  /**
   * Get payments for an entity
   */
  async getPaymentsForEntity(
    entityType: string,
    entityId: string,
  ): Promise<Payment[]> {
    return this.paymentRepository.findByEntityId(entityType, entityId);
  }

  /**
   * Get payments for a tenant with filtering
   */
  async getPaymentsByTenant(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<{
    data: Payment[];
    pagination: { total: number; page: number; limit: number };
  }> {
    return this.paymentRepository.findByTenantId(tenantId, filter);
  }

  /**
   * Create a payment method
   */
  async createPaymentMethod(
    tenantId: string,
    userId: string,
    customerId: string,
    type: PaymentMethodType,
    provider: PaymentProvider,
    name: string,
    paymentMethodData: any,
    isDefault: boolean = false,
  ): Promise<PaymentMethod> {
    this.logger.log(
      `Creating payment method for customer ${customerId} with provider ${provider}`,
    );

    let providerMethodId: string | null = null;
    let paymentMethodDetails: any = {};

    try {
      // Create payment method with provider
      if (provider === PaymentProvider.Stripe) {
        // Check if customer has a Stripe customer ID
        const stripeCustomerId = paymentMethodData.stripeCustomerId;

        // Create payment method with Stripe
        const stripePaymentMethod =
          await this.stripeService.createPaymentMethod(
            type,
            paymentMethodData,
            stripeCustomerId,
          );

        providerMethodId = stripePaymentMethod.id;
        paymentMethodDetails = {
          last4: stripePaymentMethod.card?.last4,
          brand: stripePaymentMethod.card?.brand,
          expiryMonth: stripePaymentMethod.card?.exp_month,
          expiryYear: stripePaymentMethod.card?.exp_year,
          country: stripePaymentMethod.card?.country,
        };
      } else {
        throw new Error(`Unsupported payment provider: ${provider}`);
      }

      // Create payment method record
      const paymentMethod = new PaymentMethod();
      paymentMethod.tenantId = tenantId;
      paymentMethod.customerId = customerId;
      paymentMethod.type = type;
      paymentMethod.provider = provider;
      paymentMethod.providerMethodId = providerMethodId;
      paymentMethod.isDefault = isDefault;
      paymentMethod.isEnabled = true;
      paymentMethod.name = name;
      paymentMethod.displayName = name;
      paymentMethod.lastFour = paymentMethodDetails.last4;
      paymentMethod.expiryMonth = paymentMethodDetails.expiryMonth;
      paymentMethod.expiryYear = paymentMethodDetails.expiryYear;
      paymentMethod.brand = paymentMethodDetails.brand;
      paymentMethod.country = paymentMethodDetails.country;
      paymentMethod.metadata = paymentMethodData;
      paymentMethod.createdBy = userId;

      // If this is the default payment method, unset any existing defaults
      if (isDefault) {
        // Find existing payment methods for this customer
        const existingMethods =
          await this.paymentMethodRepository.findByCustomerId(customerId);

        // If there are existing methods, unset default
        if (existingMethods.length > 0) {
          for (const method of existingMethods) {
            if (method.isDefault) {
              await this.paymentMethodRepository.update(method.id, {
                isDefault: false,
                updatedBy: userId,
              });
            }
          }
        }
      }

      // Save the payment method
      return this.paymentMethodRepository.create(paymentMethod);
    } catch (error) {
      this.logger.error(
        `Error creating payment method: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get payment methods for a customer
   */
  async getPaymentMethodsForCustomer(
    customerId: string,
  ): Promise<PaymentMethod[]> {
    return this.paymentMethodRepository.findByCustomerId(customerId);
  }

  /**
   * Set a payment method as default
   */
  async setDefaultPaymentMethod(
    paymentMethodId: string,
    customerId: string,
  ): Promise<PaymentMethod> {
    const method = await this.paymentMethodRepository.setDefault(
      paymentMethodId,
      customerId,
    );
    if (!method) {
      throw new Error(`Payment method with ID ${paymentMethodId} not found`);
    }
    return method;
  }

  /**
   * Delete a payment method
   */
  async deletePaymentMethod(paymentMethodId: string): Promise<boolean> {
    return this.paymentMethodRepository.delete(paymentMethodId);
  }

  /**
   * Handle webhook events from payment providers
   */
  async handleWebhookEvent(
    provider: PaymentProvider,
    eventType: string,
    eventData: any,
    signature?: string,
    payload?: Buffer,
  ): Promise<void> {
    this.logger.log(
      `Handling webhook event ${eventType} from provider ${provider}`,
    );

    try {
      if (provider === PaymentProvider.Stripe) {
        // Verify webhook signature if provided
        let event: any;
        if (signature && payload) {
          event = await this.stripeService.processWebhookEvent(
            signature,
            payload,
          );
        } else {
          event = eventData;
        }

        // Process Stripe webhook event
        await this.processStripeWebhookEvent(event);
      } else {
        throw new Error(`Unsupported payment provider: ${provider}`);
      }
    } catch (error) {
      this.logger.error(
        `Error handling webhook event: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process Stripe webhook event
   */
  private async processStripeWebhookEvent(event: any): Promise<void> {
    const eventType = event.type;
    const data = event.data.object;

    this.logger.log(`Processing Stripe webhook event: ${eventType}`);

    try {
      // Handle different event types
      switch (eventType) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(data);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(data);
          break;
        case 'payment_intent.canceled':
          await this.handlePaymentIntentCanceled(data);
          break;
        case 'charge.refunded':
          await this.handleChargeRefunded(data);
          break;
        default:
          this.logger.log(`Unhandled Stripe event type: ${eventType}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing Stripe webhook event: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle payment_intent.succeeded event
   */
  private async handlePaymentIntentSucceeded(data: any): Promise<void> {
    const paymentIntentId = data.id;
    const metadata = data.metadata || {};
    const paymentId = metadata.paymentId;

    if (!paymentId) {
      this.logger.warn(
        `Received payment_intent.succeeded event without payment ID: ${paymentIntentId}`,
      );
      return;
    }

    // Get the payment
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      this.logger.warn(
        `Payment not found for payment_intent.succeeded event: ${paymentId}`,
      );
      return;
    }

    // Update payment status to completed
    await this.paymentRepository.updateStatus(
      paymentId,
      PaymentStatus.Completed,
      {
        receiptUrl: data.charges?.data[0]?.receipt_url,
        receiptNumber: data.charges?.data[0]?.receipt_number,
        stripePaymentStatus: data.status,
      },
    );

    this.logger.log(
      `Updated payment ${paymentId} to Completed from webhook event`,
    );
  }

  /**
   * Handle payment_intent.payment_failed event
   */
  private async handlePaymentIntentFailed(data: any): Promise<void> {
    const paymentIntentId = data.id;
    const metadata = data.metadata || {};
    const paymentId = metadata.paymentId;

    if (!paymentId) {
      this.logger.warn(
        `Received payment_intent.payment_failed event without payment ID: ${paymentIntentId}`,
      );
      return;
    }

    // Get the payment
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      this.logger.warn(
        `Payment not found for payment_intent.payment_failed event: ${paymentId}`,
      );
      return;
    }

    // Get error information
    const error = data.last_payment_error;
    const errorMessage = error?.message || 'Payment failed';
    const errorCode = error?.code || 'unknown';

    // Update payment status to failed
    await this.paymentRepository.updateStatus(paymentId, PaymentStatus.Failed, {
      errorMessage,
      errorCode,
      stripePaymentStatus: data.status,
    });

    this.logger.log(
      `Updated payment ${paymentId} to Failed from webhook event: ${errorMessage}`,
    );
  }

  /**
   * Handle payment_intent.canceled event
   */
  private async handlePaymentIntentCanceled(data: any): Promise<void> {
    const paymentIntentId = data.id;
    const metadata = data.metadata || {};
    const paymentId = metadata.paymentId;

    if (!paymentId) {
      this.logger.warn(
        `Received payment_intent.canceled event without payment ID: ${paymentIntentId}`,
      );
      return;
    }

    // Get the payment
    const payment = await this.paymentRepository.findById(paymentId);
    if (!payment) {
      this.logger.warn(
        `Payment not found for payment_intent.canceled event: ${paymentId}`,
      );
      return;
    }

    // Update payment status to cancelled
    await this.paymentRepository.updateStatus(
      paymentId,
      PaymentStatus.Cancelled,
      {
        cancellationReason: data.cancellation_reason,
        stripePaymentStatus: data.status,
      },
    );

    this.logger.log(
      `Updated payment ${paymentId} to Cancelled from webhook event`,
    );
  }

  /**
   * Handle charge.refunded event
   */
  private async handleChargeRefunded(data: any): Promise<void> {
    const chargeId = data.id;
    const paymentIntentId = data.payment_intent;
    const isFullRefund = data.refunded;

    if (!paymentIntentId) {
      this.logger.warn(
        `Received charge.refunded event without payment intent ID: ${chargeId}`,
      );
      return;
    }

    // Find payment by provider transaction ID
    // Create a filter with the provider transaction ID
    const filter = new BaseFilterDto();
    // We'll use a custom approach to find by provider transaction ID
    const allPayments = await this.paymentRepository.findByTenantId('', filter);
    const payments = {
      data: allPayments.data.filter(
        (p) => p.providerTransactionId === paymentIntentId,
      ),
      pagination: allPayments.pagination,
    };

    if (!payments || payments.data.length === 0) {
      this.logger.warn(
        `Payment not found for charge.refunded event: ${paymentIntentId}`,
      );
      return;
    }

    const payment = payments.data[0];

    // Update payment status based on full or partial refund
    const refundStatus = isFullRefund
      ? PaymentStatus.Refunded
      : PaymentStatus.PartiallyRefunded;

    await this.paymentRepository.updateStatus(payment.id, refundStatus, {
      refundId: data.refunds?.data[0]?.id,
      refundAmount: data.amount_refunded / 100,
      refundedAt: new Date(),
    });

    this.logger.log(
      `Updated payment ${payment.id} to ${refundStatus} from webhook event`,
    );
  }
}
