import { Injectable, Logger } from '@nestjs/common';
import Strip<PERSON> from 'stripe';
import { PaymentConfigService } from '../payment-config.service';
import {
  PaymentMethodType,
  PaymentResult,
  RefundResult,
  RefundStatus,
  PaymentStatus,
} from '../../domain/payment.types';

@Injectable()
export class StripeService {
  private readonly stripe: Stripe;
  private readonly logger = new Logger(StripeService.name);

  constructor(private readonly configService: PaymentConfigService) {
    // Use a dummy key if not provided to prevent startup errors
    const apiKey =
      this.configService.stripeSecretKey || 'sk_test_dummy_key_for_development';
    this.stripe = new Stripe(apiKey, {
      apiVersion: '2025-03-31.basil',
    });
  }

  /**
   * Create a payment intent with Stripe
   */
  async createPaymentIntent(
    amount: number,
    currency: string,
    customerId: string,
    metadata: Record<string, any> = {},
    paymentMethodId?: string,
  ): Promise<PaymentResult> {
    try {
      const amountInCents = Math.round(amount * 100);

      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: amountInCents,
        currency: currency.toLowerCase(),
        metadata,
        capture_method: 'automatic',
      };

      // If we have a customer ID in Stripe, use it
      if (metadata.stripeCustomerId) {
        paymentIntentParams.customer = metadata.stripeCustomerId;
      }

      // If we have a payment method ID, use it
      if (paymentMethodId) {
        paymentIntentParams.payment_method = paymentMethodId;
        paymentIntentParams.confirm = true;
        paymentIntentParams.return_url = metadata.returnUrl || '';
      }

      const paymentIntent =
        await this.stripe.paymentIntents.create(paymentIntentParams);

      return {
        success: true,
        paymentId: paymentIntent.id,
        status: this.mapStripePaymentStatus(
          paymentIntent.status,
        ) as PaymentStatus,
        metadata: {
          clientSecret: paymentIntent.client_secret,
          stripePaymentIntentId: paymentIntent.id,
          stripePaymentStatus: paymentIntent.status,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error creating Stripe payment intent: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        status: PaymentStatus.Failed,
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  /**
   * Confirm a payment intent with Stripe
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId?: string,
  ): Promise<PaymentResult> {
    try {
      const confirmParams: Stripe.PaymentIntentConfirmParams = {};

      if (paymentMethodId) {
        confirmParams.payment_method = paymentMethodId;
      }

      const paymentIntent = await this.stripe.paymentIntents.confirm(
        paymentIntentId,
        confirmParams,
      );

      return {
        success: true,
        paymentId: paymentIntent.id,
        status: this.mapStripePaymentStatus(
          paymentIntent.status,
        ) as PaymentStatus,
        metadata: {
          stripePaymentStatus: paymentIntent.status,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error confirming Stripe payment intent: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        status: PaymentStatus.Failed,
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  /**
   * Retrieve a payment intent from Stripe
   */
  async retrievePaymentIntent(paymentIntentId: string): Promise<PaymentResult> {
    try {
      const paymentIntent =
        await this.stripe.paymentIntents.retrieve(paymentIntentId);

      return {
        success: true,
        paymentId: paymentIntent.id,
        status: this.mapStripePaymentStatus(
          paymentIntent.status,
        ) as PaymentStatus,
        metadata: {
          stripePaymentStatus: paymentIntent.status,
          receiptUrl: (paymentIntent as any).charges?.data?.[0]?.receipt_url,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving Stripe payment intent: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        status: PaymentStatus.Failed,
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  /**
   * Cancel a payment intent with Stripe
   */
  async cancelPaymentIntent(
    paymentIntentId: string,
    cancellationReason?: string,
  ): Promise<PaymentResult> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.cancel(
        paymentIntentId,
        {
          cancellation_reason:
            cancellationReason as Stripe.PaymentIntentCancelParams.CancellationReason,
        },
      );

      return {
        success: true,
        paymentId: paymentIntent.id,
        status: this.mapStripePaymentStatus(
          paymentIntent.status,
        ) as PaymentStatus,
        metadata: {
          stripePaymentStatus: paymentIntent.status,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error canceling Stripe payment intent: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        status: PaymentStatus.Failed,
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  /**
   * Create a refund with Stripe
   */
  async createRefund(
    paymentIntentId: string,
    amount?: number,
    reason?: string,
    metadata: Record<string, any> = {},
  ): Promise<RefundResult> {
    try {
      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: paymentIntentId,
        metadata,
      };

      if (amount) {
        refundParams.amount = Math.round(amount * 100);
      }

      if (reason) {
        refundParams.reason = reason as Stripe.RefundCreateParams.Reason;
      }

      const refund = await this.stripe.refunds.create(refundParams);

      return {
        success: true,
        refundId: refund.id,
        status: this.mapStripeRefundStatus(refund.status || ''),
        amount: refund.amount / 100,
        metadata: {
          stripeRefundId: refund.id,
          stripeRefundStatus: refund.status,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error creating Stripe refund: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        status: RefundStatus.Failed,
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  /**
   * Create a customer in Stripe
   */
  async createCustomer(
    email: string,
    name: string,
    metadata: Record<string, any> = {},
  ): Promise<string> {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata,
      });

      return customer.id;
    } catch (error) {
      this.logger.error(
        `Error creating Stripe customer: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create a payment method in Stripe
   */
  async createPaymentMethod(
    type: PaymentMethodType,
    paymentMethodData: any,
    customerId?: string,
  ): Promise<Stripe.PaymentMethod> {
    try {
      const stripeType = this.mapPaymentMethodType(type);

      const paymentMethod = await this.stripe.paymentMethods.create({
        type: stripeType as Stripe.PaymentMethodCreateParams.Type,
        [stripeType]: paymentMethodData,
      });

      // If customer ID is provided, attach the payment method to the customer
      if (customerId) {
        await this.stripe.paymentMethods.attach(paymentMethod.id, {
          customer: customerId,
        });
      }

      return paymentMethod;
    } catch (error) {
      this.logger.error(
        `Error creating Stripe payment method: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process a webhook event from Stripe
   */
  async processWebhookEvent(
    signature: string,
    payload: Buffer,
  ): Promise<Stripe.Event> {
    try {
      const webhookSecret = this.configService.stripeWebhookSecret;
      return this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
    } catch (error) {
      this.logger.error(
        `Error processing Stripe webhook: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Map Stripe payment status to our payment status
   */
  private mapStripePaymentStatus(status: string): string {
    switch (status) {
      case 'requires_payment_method':
      case 'requires_confirmation':
      case 'requires_action':
      case 'requires_capture':
        return 'Processing';
      case 'processing':
        return 'Processing';
      case 'succeeded':
        return 'Completed';
      case 'canceled':
        return 'Cancelled';
      default:
        return 'Pending';
    }
  }

  /**
   * Map Stripe refund status to our refund status
   */
  private mapStripeRefundStatus(status: string): RefundStatus {
    switch (status) {
      case 'pending':
        return RefundStatus.Pending;
      case 'succeeded':
        return RefundStatus.Completed;
      case 'failed':
        return RefundStatus.Failed;
      default:
        return RefundStatus.Processing;
    }
  }

  /**
   * Map our payment method type to Stripe payment method type
   */
  private mapPaymentMethodType(type: PaymentMethodType): string {
    switch (type) {
      case PaymentMethodType.CreditCard:
        return 'card';
      case PaymentMethodType.BankTransfer:
        return 'bank_transfer';
      case PaymentMethodType.DigitalWallet:
        return 'wallet';
      default:
        return 'card';
    }
  }
}
