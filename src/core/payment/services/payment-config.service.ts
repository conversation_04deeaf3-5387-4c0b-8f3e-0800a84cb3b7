import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PaymentConfig } from '../config/payment-config.type';

@Injectable()
export class PaymentConfigService {
  constructor(private readonly configService: ConfigService) {}

  get stripeSecretKey(): string {
    return this.configService.get<string>('payment.stripe.secretKey', '');
  }

  get stripePublishableKey(): string {
    return this.configService.get<string>('payment.stripe.publishableKey', '');
  }

  get stripeWebhookSecret(): string {
    return this.configService.get<string>('payment.stripe.webhookSecret', '');
  }

  get isSandboxMode(): boolean {
    return this.configService.get<boolean>('payment.sandboxMode', false);
  }

  getConfig(): PaymentConfig {
    return {
      stripe: {
        secretKey: this.stripeSecretKey,
        publishableKey: this.stripePublishableKey,
        webhookSecret: this.stripeWebhookSecret,
      },
      sandboxMode: this.isSandboxMode,
    };
  }
}
