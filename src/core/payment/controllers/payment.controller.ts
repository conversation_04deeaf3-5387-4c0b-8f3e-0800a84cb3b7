import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Query,
  Patch,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
  ApiBody,
  ApiNoContentResponse,
} from '@nestjs/swagger';
import { PaymentService } from '../services/payment.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { PaymentResponseDto } from '../dto/payment-response.dto';
import { ProcessPaymentDto } from '../dto/process-payment.dto';
import { PaymentResultDto } from '../dto/payment-result.dto';
import { CancelPaymentDto } from '../dto/cancel-payment.dto';
import { RefundPaymentDto } from '../dto/refund-payment.dto';
import { RefundResultDto } from '../dto/refund-result.dto';
import { CreatePaymentMethodDto } from '../dto/create-payment-method.dto';
import { PaymentMethodResponseDto } from '../dto/payment-method-response.dto';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { TenantValidationService } from '@app/business/user/tenants/tenant-validation.service';
import { RequestWithUser } from '@core/auth/interceptors/tenant-context.interceptor';

@ApiTags('Core - Payments')
@ApiBearerAuth()
@Controller({
  path: '/payments',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly tenantValidationService: TenantValidationService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new payment' })
  @ApiCreatedResponse({
    description: 'Payment created successfully',
    type: PaymentResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiUnauthorizedResponse({ description: 'User not authenticated' })
  @ApiBody({ type: CreatePaymentDto })
  async create(
    @Req() request: RequestWithUser,
    @Body() createPaymentDto: CreatePaymentDto,
  ): Promise<PaymentResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payment = await this.paymentService.createPayment(
      tenant.id,
      request.user.id,
      createPaymentDto.amount,
      createPaymentDto.currency,
      createPaymentDto.entityType,
      createPaymentDto.entityId,
      createPaymentDto.customerId,
      createPaymentDto.customerEmail,
      createPaymentDto.customerName,
      createPaymentDto.paymentMethodId,
      createPaymentDto.description,
      createPaymentDto.metadata,
      createPaymentDto.triggerType,
    );

    return this.mapPaymentToResponseDto(payment);
  }

  @Get()
  @ApiOperation({ summary: 'Get all payments for the current tenant' })
  @ApiOkResponse({
    description: 'List of payments with pagination info',
    type: [PaymentResponseDto],
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  async findAll(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ) {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const result = await this.paymentService.getPaymentsByTenant(
      tenant.id,
      filter,
    );

    // Convert our custom pagination format to the expected PaginatedResult format
    const paginatedResult: PaginatedResult<PaymentResponseDto> = {
      data: result.data.map((payment) => this.mapPaymentToResponseDto(payment)),
      total: result.pagination.total,
      page: result.pagination.page,
      limit: result.pagination.limit,
      totalPages: Math.ceil(result.pagination.total / result.pagination.limit),
      hasNextPage:
        result.pagination.page <
        Math.ceil(result.pagination.total / result.pagination.limit),
      hasPreviousPage: result.pagination.page > 1,
    };

    return paginatedResult;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a payment by ID' })
  @ApiOkResponse({
    description: 'Payment details',
    type: PaymentResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Payment not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findOne(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<PaymentResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payment = await this.paymentService.getPaymentById(id);

    if (!payment || payment.tenantId !== tenant.id) {
      throw new Error(`Payment with ID ${id} not found`);
    }

    return this.mapPaymentToResponseDto(payment);
  }

  @Get('entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Get payments for an entity' })
  @ApiOkResponse({
    description: 'List of payments for the entity',
    type: [PaymentResponseDto],
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type (e.g., "order")',
    type: 'string',
    example: 'order',
  })
  @ApiParam({
    name: 'entityId',
    description: 'Entity ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findByEntity(
    @Req() request: RequestWithUser,
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
  ): Promise<PaymentResponseDto[]> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payments = await this.paymentService.getPaymentsForEntity(
      entityType,
      entityId,
    );

    // Filter payments by tenant
    const tenantPayments = payments.filter(
      (payment) => payment.tenantId === tenant.id,
    );

    return tenantPayments.map((payment) =>
      this.mapPaymentToResponseDto(payment),
    );
  }

  @Post(':id/process')
  @ApiOperation({ summary: 'Process a payment' })
  @ApiOkResponse({
    description: 'Payment processed successfully',
    type: PaymentResultDto,
  })
  @ApiNotFoundResponse({ description: 'Payment not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: ProcessPaymentDto })
  async processPayment(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() processPaymentDto: ProcessPaymentDto,
  ): Promise<PaymentResultDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payment = await this.paymentService.getPaymentById(id);

    if (!payment || payment.tenantId !== tenant.id) {
      throw new Error(`Payment with ID ${id} not found`);
    }

    const result = await this.paymentService.processPayment(
      id,
      request.user.id,
    );

    return {
      success: result.success,
      paymentId: result.paymentId,
      status: result.status,
      message: result.message,
      errorCode: result.errorCode,
      metadata: result.metadata,
    };
  }

  @Post(':id/confirm')
  @ApiOperation({ summary: 'Confirm a payment' })
  @ApiOkResponse({
    description: 'Payment confirmed successfully',
    type: PaymentResultDto,
  })
  @ApiNotFoundResponse({ description: 'Payment not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: ProcessPaymentDto })
  async confirmPayment(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() processPaymentDto: ProcessPaymentDto,
  ): Promise<PaymentResultDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payment = await this.paymentService.getPaymentById(id);

    if (!payment || payment.tenantId !== tenant.id) {
      throw new Error(`Payment with ID ${id} not found`);
    }

    const result = await this.paymentService.confirmPayment(
      id,
      request.user.id,
      processPaymentDto.paymentMethodId,
    );

    return {
      success: result.success,
      paymentId: result.paymentId,
      status: result.status,
      message: result.message,
      errorCode: result.errorCode,
      metadata: result.metadata,
    };
  }

  @Get(':id/status')
  @ApiOperation({ summary: 'Check payment status' })
  @ApiOkResponse({
    description: 'Payment status',
    type: PaymentResultDto,
  })
  @ApiNotFoundResponse({ description: 'Payment not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async checkPaymentStatus(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<PaymentResultDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payment = await this.paymentService.getPaymentById(id);

    if (!payment || payment.tenantId !== tenant.id) {
      throw new Error(`Payment with ID ${id} not found`);
    }

    const result = await this.paymentService.checkPaymentStatus(id);

    return {
      success: result.success,
      paymentId: result.paymentId,
      status: result.status,
      message: result.message,
      errorCode: result.errorCode,
      metadata: result.metadata,
    };
  }

  @Post(':id/cancel')
  @ApiOperation({ summary: 'Cancel a payment' })
  @ApiOkResponse({
    description: 'Payment cancelled successfully',
    type: PaymentResultDto,
  })
  @ApiNotFoundResponse({ description: 'Payment not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: CancelPaymentDto })
  async cancelPayment(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() cancelPaymentDto: CancelPaymentDto,
  ): Promise<PaymentResultDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payment = await this.paymentService.getPaymentById(id);

    if (!payment || payment.tenantId !== tenant.id) {
      throw new Error(`Payment with ID ${id} not found`);
    }

    const result = await this.paymentService.cancelPayment(
      id,
      request.user.id,
      cancelPaymentDto.reason,
    );

    return {
      success: result.success,
      paymentId: result.paymentId,
      status: result.status,
      message: result.message,
      errorCode: result.errorCode,
      metadata: result.metadata,
    };
  }

  @Post(':id/refund')
  @ApiOperation({ summary: 'Refund a payment' })
  @ApiOkResponse({
    description: 'Payment refunded successfully',
    type: RefundResultDto,
  })
  @ApiNotFoundResponse({ description: 'Payment not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: RefundPaymentDto })
  async refundPayment(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() refundPaymentDto: RefundPaymentDto,
  ): Promise<RefundResultDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const payment = await this.paymentService.getPaymentById(id);

    if (!payment || payment.tenantId !== tenant.id) {
      throw new Error(`Payment with ID ${id} not found`);
    }

    const result = await this.paymentService.refundPayment(
      id,
      request.user.id,
      refundPaymentDto.amount,
      refundPaymentDto.reason,
    );

    return {
      success: result.success,
      refundId: result.refundId,
      status: result.status,
      amount: result.amount,
      message: result.message,
      errorCode: result.errorCode,
      metadata: result.metadata,
    };
  }

  // Payment Methods

  @Post('methods')
  @ApiOperation({ summary: 'Create a new payment method' })
  @ApiCreatedResponse({
    description: 'Payment method created successfully',
    type: PaymentMethodResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiUnauthorizedResponse({ description: 'User not authenticated' })
  @ApiBody({ type: CreatePaymentMethodDto })
  async createPaymentMethod(
    @Req() request: RequestWithUser,
    @Body() createPaymentMethodDto: CreatePaymentMethodDto,
  ): Promise<PaymentMethodResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const paymentMethod = await this.paymentService.createPaymentMethod(
      tenant.id,
      request.user.id,
      createPaymentMethodDto.customerId,
      createPaymentMethodDto.type,
      createPaymentMethodDto.provider,
      createPaymentMethodDto.name,
      createPaymentMethodDto.paymentMethodData,
      createPaymentMethodDto.isDefault,
    );

    return this.mapPaymentMethodToResponseDto(paymentMethod);
  }

  @Get('methods/customer/:customerId')
  @ApiOperation({ summary: 'Get payment methods for a customer' })
  @ApiOkResponse({
    description: 'List of payment methods',
    type: [PaymentMethodResponseDto],
  })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'customerId',
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async getPaymentMethodsForCustomer(
    @Req() request: RequestWithUser,
    @Param('customerId') customerId: string,
  ): Promise<PaymentMethodResponseDto[]> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const paymentMethods =
      await this.paymentService.getPaymentMethodsForCustomer(customerId);

    // Filter payment methods by tenant
    const tenantPaymentMethods = paymentMethods.filter(
      (method) => method.tenantId === tenant.id,
    );

    return tenantPaymentMethods.map((method) =>
      this.mapPaymentMethodToResponseDto(method),
    );
  }

  @Patch('methods/:id/default')
  @ApiOperation({ summary: 'Set a payment method as default' })
  @ApiOkResponse({
    description: 'Payment method set as default',
    type: PaymentMethodResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Payment method not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment Method ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async setDefaultPaymentMethod(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<PaymentMethodResponseDto> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const paymentMethod = await this.paymentService.getPaymentById(id);

    if (paymentMethod.tenantId !== tenant.id) {
      throw new Error(`Payment method with ID ${id} not found`);
    }

    const updatedMethod = await this.paymentService.setDefaultPaymentMethod(
      id,
      paymentMethod.customerId,
    );

    return this.mapPaymentMethodToResponseDto(updatedMethod);
  }

  @Delete('methods/:id')
  @ApiOperation({ summary: 'Delete a payment method' })
  @ApiNoContentResponse({
    description: 'Payment method deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Payment method not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient tenant access permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment Method ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePaymentMethod(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    const tenant =
      await this.tenantValidationService.validateTenantAccess(request);

    const paymentMethod = await this.paymentService.getPaymentById(id);

    if (paymentMethod.tenantId !== tenant.id) {
      throw new Error(`Payment method with ID ${id} not found`);
    }

    await this.paymentService.deletePaymentMethod(id);
  }

  /**
   * Map a payment entity to a response DTO
   */
  private mapPaymentToResponseDto(payment: any): PaymentResponseDto {
    const dto = new PaymentResponseDto();
    Object.assign(dto, payment);
    return dto;
  }

  /**
   * Map a payment method entity to a response DTO
   */
  private mapPaymentMethodToResponseDto(
    paymentMethod: any,
  ): PaymentMethodResponseDto {
    const dto = new PaymentMethodResponseDto();
    Object.assign(dto, paymentMethod);
    return dto;
  }
}
