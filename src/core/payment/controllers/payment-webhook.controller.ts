import {
  Controller,
  Post,
  Headers,
  Body,
  RawBodyRequest,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import { PaymentService } from '../services/payment.service';
import { PaymentProvider } from '../domain/payment.types';
import { Request } from 'express';

@ApiTags('Core - Payment Webhooks')
@Controller({
  path: '/payments/webhooks',
  version: '1',
})
export class PaymentWebhookController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post('stripe')
  @ApiOperation({ summary: 'Handle Stripe webhook events' })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
  })
  @ApiBody({
    description: 'Stripe webhook event payload',
    type: Object,
  })
  @HttpCode(HttpStatus.OK)
  async handleStripeWebhook(
    @Headers('stripe-signature') signature: string,
    @Req() request: RawBodyRequest<Request>,
  ): Promise<{ received: boolean }> {
    try {
      // Get the raw body buffer
      const payload = request.rawBody;

      // Process the webhook event
      await this.paymentService.handleWebhookEvent(
        PaymentProvider.Stripe,
        '', // Event type will be extracted from the event
        {}, // Event data will be extracted from the event
        signature,
        payload,
      );

      return { received: true };
    } catch (error) {
      console.error('Error handling Stripe webhook:', error);
      return { received: false };
    }
  }
}
