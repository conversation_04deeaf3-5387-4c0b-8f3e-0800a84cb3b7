import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { PaymentMethodEntity } from './payment-method.entity';
import {
  PaymentMethodType,
  PaymentProvider,
  PaymentStatus,
  PaymentTriggerType,
} from '../../domain/payment.types';

@Entity('payments')
export class PaymentEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid', { name: 'tenant_id' })
  @Index()
  tenantId: string;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    name: 'amount',
  })
  amount: number;

  @AutoMap()
  @Column({ length: 3, name: 'currency', default: 'USD' })
  currency: string;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.Pending,
    name: 'status',
  })
  status: PaymentStatus;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentProvider,
    name: 'provider',
  })
  provider: PaymentProvider;

  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'provider_transaction_id' })
  providerTransactionId: string;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    name: 'provider_fee',
  })
  providerFee: number;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'payment_method_id' })
  paymentMethodId: string;

  @ManyToOne(() => PaymentMethodEntity)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod: PaymentMethodEntity;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentMethodType,
    name: 'payment_method_type',
  })
  paymentMethodType: PaymentMethodType;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'payment_method_details' })
  paymentMethodDetails: Record<string, any>;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentTriggerType,
    default: PaymentTriggerType.Manual,
    name: 'trigger_type',
  })
  triggerType: PaymentTriggerType;

  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'description' })
  description: string;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'metadata' })
  metadata: Record<string, any>;

  // Related entities
  @AutoMap()
  @Column({ length: 50, name: 'entity_type' })
  entityType: string;

  @AutoMap()
  @Column('uuid', { name: 'entity_id' })
  @Index()
  entityId: string;

  // Customer information
  @AutoMap()
  @Column('uuid', { name: 'customer_id' })
  @Index()
  customerId: string;

  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'customer_email' })
  customerEmail: string;

  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'customer_name' })
  customerName: string;

  // Timestamps
  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true, name: 'completed_at' })
  completedAt: Date;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true, name: 'failed_at' })
  failedAt: Date;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true, name: 'cancelled_at' })
  cancelledAt: Date;

  // User who initiated the payment
  @AutoMap()
  @Column('uuid', { name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'updated_by' })
  updatedBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser: UserEntity;

  // Error information
  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'error_message' })
  errorMessage: string;

  @AutoMap()
  @Column({ length: 50, nullable: true, name: 'error_code' })
  errorCode: string;

  // Receipt information
  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'receipt_url' })
  receiptUrl: string;

  @AutoMap()
  @Column({ length: 50, nullable: true, name: 'receipt_number' })
  receiptNumber: string;
}
