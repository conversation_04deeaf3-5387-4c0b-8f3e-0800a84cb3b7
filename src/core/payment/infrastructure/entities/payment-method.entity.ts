import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { PaymentMethodType, PaymentProvider } from '../../domain/payment.types';

@Entity('payment_methods')
export class PaymentMethodEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid', { name: 'tenant_id' })
  @Index()
  tenantId: string;

  @AutoMap()
  @Column('uuid', { name: 'customer_id' })
  @Index()
  customerId: string;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentMethodType,
    name: 'type',
  })
  type: PaymentMethodType;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentProvider,
    name: 'provider',
  })
  provider: PaymentProvider;

  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'provider_method_id' })
  providerMethodId: string;

  @AutoMap()
  @Column({ default: false, name: 'is_default' })
  isDefault: boolean;

  @AutoMap()
  @Column({ default: true, name: 'is_enabled' })
  isEnabled: boolean;

  @AutoMap()
  @Column({ length: 100, name: 'name' })
  name: string;

  @AutoMap()
  @Column({ length: 100, name: 'display_name' })
  displayName: string;

  @AutoMap()
  @Column({ length: 4, nullable: true, name: 'last_four' })
  lastFour: string;

  @AutoMap()
  @Column({ type: 'smallint', nullable: true, name: 'expiry_month' })
  expiryMonth: number;

  @AutoMap()
  @Column({ type: 'smallint', nullable: true, name: 'expiry_year' })
  expiryYear: number;

  @AutoMap()
  @Column({ length: 50, nullable: true, name: 'brand' })
  brand: string;

  @AutoMap()
  @Column({ length: 2, nullable: true, name: 'country' })
  country: string;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'metadata' })
  metadata: Record<string, any>;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @AutoMap()
  @Column('uuid', { name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'updated_by' })
  updatedBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser: UserEntity;
}
