import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { PaymentProvider } from '../../domain/payment.types';

@Entity('payment_providers')
export class PaymentProviderEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid', { name: 'tenant_id' })
  @Index()
  tenantId: string;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentProvider,
    name: 'provider',
  })
  provider: PaymentProvider;

  @AutoMap()
  @Column({ length: 100, name: 'name' })
  name: string;

  @AutoMap()
  @Column({ default: true, name: 'is_enabled' })
  isEnabled: boolean;

  @AutoMap()
  @Column({ default: false, name: 'is_default' })
  isDefault: boolean;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'configuration' })
  configuration: Record<string, any>;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'metadata' })
  metadata: Record<string, any>;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @AutoMap()
  @Column('uuid', { name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'updated_by' })
  updatedBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser: UserEntity;
}
