import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentMethodEntity } from '../entities/payment-method.entity';
import { PaymentMethod } from '../../domain/payment-method';
import { PaymentProvider } from '../../domain/payment.types';

@Injectable()
export class PaymentMethodRepository {
  constructor(
    @InjectRepository(PaymentMethodEntity)
    private readonly paymentMethodRepository: Repository<PaymentMethodEntity>,
  ) {}

  async create(paymentMethod: PaymentMethod): Promise<PaymentMethod> {
    const entity = this.paymentMethodRepository.create(paymentMethod as any);
    const savedEntity = await this.paymentMethodRepository.save(entity);
    return this.mapEntityToDomain(savedEntity);
  }

  async findById(id: string): Promise<PaymentMethod | null> {
    const entity = await this.paymentMethodRepository.findOne({
      where: { id },
    });

    if (!entity) {
      return null;
    }

    return this.mapEntityToDomain(entity);
  }

  async findByCustomerId(customerId: string): Promise<PaymentMethod[]> {
    const entities = await this.paymentMethodRepository.find({
      where: { customerId, isEnabled: true },
      order: { isDefault: 'DESC', createdAt: 'DESC' },
    });

    return entities.map((entity) => this.mapEntityToDomain(entity));
  }

  async findByProviderMethodId(
    provider: PaymentProvider,
    providerMethodId: string,
  ): Promise<PaymentMethod | null> {
    const entity = await this.paymentMethodRepository.findOne({
      where: { provider, providerMethodId },
    });

    if (!entity) {
      return null;
    }

    return this.mapEntityToDomain(entity);
  }

  async update(
    id: string,
    paymentMethod: Partial<PaymentMethod>,
  ): Promise<PaymentMethod | null> {
    await this.paymentMethodRepository.update(id, paymentMethod as any);
    return this.findById(id);
  }

  async setDefault(
    id: string,
    customerId: string,
  ): Promise<PaymentMethod | null> {
    // First, unset default for all customer's payment methods
    await this.paymentMethodRepository.update(
      { customerId },
      { isDefault: false },
    );

    // Then set the specified one as default
    await this.paymentMethodRepository.update(id, { isDefault: true });
    return this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.paymentMethodRepository.delete(id);
    return !!result.affected && result.affected > 0;
  }

  private mapEntityToDomain(entity: PaymentMethodEntity | any): PaymentMethod {
    const paymentMethod = new PaymentMethod();
    Object.assign(paymentMethod, entity);
    return paymentMethod;
  }
}
