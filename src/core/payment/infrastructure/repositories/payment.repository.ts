import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentEntity } from '../entities/payment.entity';
import { Payment } from '../../domain/payment';
import { PaymentStatus } from '../../domain/payment.types';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';

@Injectable()
export class PaymentRepository {
  constructor(
    @InjectRepository(PaymentEntity)
    private readonly paymentRepository: Repository<PaymentEntity>,
  ) {}

  async create(payment: Payment): Promise<Payment> {
    const entity = this.paymentRepository.create(payment as any);
    const savedEntity = await this.paymentRepository.save(entity);
    return this.mapEntityToDomain(savedEntity);
  }

  async findById(id: string): Promise<Payment | null> {
    const entity = await this.paymentRepository.findOne({
      where: { id },
      relations: ['paymentMethod', 'createdByUser'],
    });

    if (!entity) {
      return null;
    }

    return this.mapEntityToDomain(entity);
  }

  async findByEntityId(
    entityType: string,
    entityId: string,
  ): Promise<Payment[]> {
    const entities = await this.paymentRepository.find({
      where: { entityType, entityId },
      relations: ['paymentMethod', 'createdByUser'],
      order: { createdAt: 'DESC' },
    });

    return entities.map((entity) => this.mapEntityToDomain(entity));
  }

  async findByTenantId(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<{
    data: Payment[];
    pagination: { total: number; page: number; limit: number };
  }> {
    // Implementation would use QueryService to handle filtering, sorting, and pagination
    // This is a simplified version
    const [entities, total] = await this.paymentRepository.findAndCount({
      where: { tenantId },
      relations: ['paymentMethod', 'createdByUser'],
      skip: filter.pageNumber
        ? (filter.pageNumber - 1) * (filter.pageSize || 10)
        : 0,
      take: filter.pageSize || 10,
      order: { createdAt: 'DESC' },
    });

    return {
      data: entities.map((entity) => this.mapEntityToDomain(entity)),
      pagination: {
        total,
        page: filter.pageNumber || 1,
        limit: filter.pageSize || 10,
      },
    };
  }

  async update(id: string, payment: Partial<Payment>): Promise<Payment | null> {
    await this.paymentRepository.update(id, payment as any);
    return this.findById(id);
  }

  async updateStatus(
    id: string,
    status: PaymentStatus,
    metadata?: Record<string, any>,
  ): Promise<Payment | null> {
    const updateData: any = { status };

    // Set timestamp based on status
    if (status === PaymentStatus.Completed) {
      updateData.completedAt = new Date();
    } else if (status === PaymentStatus.Failed) {
      updateData.failedAt = new Date();
    } else if (status === PaymentStatus.Cancelled) {
      updateData.cancelledAt = new Date();
    }

    // Update metadata if provided
    if (metadata) {
      const payment = await this.findById(id);
      if (payment) {
        updateData.metadata = {
          ...(payment.metadata || {}),
          ...metadata,
        };
      }
    }

    await this.paymentRepository.update(id, updateData);
    return this.findById(id);
  }

  private mapEntityToDomain(entity: PaymentEntity | any): Payment {
    const payment = new Payment();
    Object.assign(payment, entity);
    return payment;
  }
}
