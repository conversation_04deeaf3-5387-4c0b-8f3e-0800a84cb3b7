import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentProviderEntity } from '../entities/payment-provider.entity';
import { PaymentProvider } from '../../domain/payment.types';

@Injectable()
export class PaymentProviderRepository {
  constructor(
    @InjectRepository(PaymentProviderEntity)
    private readonly paymentProviderRepository: Repository<PaymentProviderEntity>,
  ) {}

  async findByTenantAndProvider(
    tenantId: string,
    provider: PaymentProvider,
  ): Promise<PaymentProviderEntity | null> {
    return this.paymentProviderRepository.findOne({
      where: { tenantId, provider, isEnabled: true },
    });
  }

  async getDefaultProvider(
    tenantId: string,
  ): Promise<PaymentProviderEntity | null> {
    return this.paymentProviderRepository.findOne({
      where: { tenantId, isDefault: true, isEnabled: true },
    });
  }

  async getEnabledProviders(
    tenantId: string,
  ): Promise<PaymentProviderEntity[]> {
    return this.paymentProviderRepository.find({
      where: { tenantId, isEnabled: true },
      order: { isDefault: 'DESC', name: 'ASC' },
    });
  }

  async create(
    provider: Partial<PaymentProviderEntity>,
  ): Promise<PaymentProviderEntity> {
    const entity = this.paymentProviderRepository.create(provider);
    return this.paymentProviderRepository.save(entity);
  }

  async update(
    id: string,
    provider: Partial<PaymentProviderEntity>,
  ): Promise<PaymentProviderEntity | null> {
    await this.paymentProviderRepository.update(id, provider);
    return this.paymentProviderRepository.findOne({ where: { id } });
  }

  async setDefault(
    id: string,
    tenantId: string,
  ): Promise<PaymentProviderEntity | null> {
    // First, unset default for all tenant's providers
    await this.paymentProviderRepository.update(
      { tenantId },
      { isDefault: false },
    );

    // Then set the specified one as default
    await this.paymentProviderRepository.update(id, { isDefault: true });
    return this.paymentProviderRepository.findOne({ where: { id } });
  }
}
