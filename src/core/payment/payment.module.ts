import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentService } from './services/payment.service';
import { PaymentController } from './controllers/payment.controller';
import { PaymentEntity } from './infrastructure/entities/payment.entity';
import { PaymentMethodEntity } from './infrastructure/entities/payment-method.entity';
import { PaymentProviderEntity } from './infrastructure/entities/payment-provider.entity';
import { StripeService } from './services/providers/stripe.service';
import { PaymentRepository } from './infrastructure/repositories/payment.repository';
import { PaymentMethodRepository } from './infrastructure/repositories/payment-method.repository';
import { PaymentProviderRepository } from './infrastructure/repositories/payment-provider.repository';
import { PaymentWebhookController } from './controllers/payment-webhook.controller';
import { PaymentConfigService } from './services/payment-config.service';
import paymentConfig from './config/payment.config';
import { TenantsModule } from '@app/business/user/tenants/tenants.module';

@Module({
  imports: [
    ConfigModule.forFeature(paymentConfig),
    TypeOrmModule.forFeature([
      PaymentEntity,
      PaymentMethodEntity,
      PaymentProviderEntity,
    ]),
    TenantsModule,
  ],
  controllers: [PaymentController, PaymentWebhookController],
  providers: [
    PaymentService,
    StripeService,
    PaymentRepository,
    PaymentMethodRepository,
    PaymentProviderRepository,
    PaymentConfigService,
  ],
  exports: [PaymentService],
})
export class PaymentModule {}
