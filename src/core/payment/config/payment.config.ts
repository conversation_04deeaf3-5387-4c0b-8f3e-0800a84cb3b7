import { registerAs } from '@nestjs/config';
import { IsString, IsOptional, IsBoolean } from 'class-validator';
import validateConfig from '@utils/validate-config';
import { PaymentConfig } from './payment-config.type';

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  STRIPE_SECRET_KEY: string;

  @IsString()
  @IsOptional()
  STRIPE_PUBLISHABLE_KEY: string;

  @IsString()
  @IsOptional()
  STRIPE_WEBHOOK_SECRET: string;

  @IsBoolean()
  @IsOptional()
  PAYMENT_SANDBOX_MODE: boolean;
}

export default registerAs<PaymentConfig>('payment', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    stripe: {
      secretKey: process.env.STRIPE_SECRET_KEY || '',
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
    },
    sandboxMode: process.env.PAYMENT_SANDBOX_MODE === 'true',
  };
});
