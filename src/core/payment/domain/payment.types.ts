export enum PaymentStatus {
  Pending = 'Pending',
  Processing = 'Processing',
  Completed = 'Completed',
  Failed = 'Failed',
  Refunded = 'Refunded',
  PartiallyRefunded = 'PartiallyRefunded',
  Cancelled = 'Cancelled',
}

export enum PaymentProvider {
  Stripe = 'Stripe',
  // Future providers can be added here
}

export enum PaymentMethodType {
  CreditCard = 'CreditCard',
  BankTransfer = 'BankTransfer',
  DigitalWallet = 'DigitalWallet',
  Cash = 'Cash',
  Other = 'Other',
}

export enum PaymentTriggerType {
  Manual = 'Manual',
  OrderCreation = 'OrderCreation',
  OrderCompletion = 'OrderCompletion',
  Scheduled = 'Scheduled',
  Milestone = 'Milestone',
}

export enum RefundStatus {
  Pending = 'Pending',
  Processing = 'Processing',
  Completed = 'Completed',
  Failed = 'Failed',
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  transactionId?: string;
  status: PaymentStatus;
  message?: string;
  errorCode?: string;
  metadata?: Record<string, any>;
}

export interface RefundResult {
  success: boolean;
  refundId?: string;
  transactionId?: string;
  status: RefundStatus;
  amount?: number;
  message?: string;
  errorCode?: string;
  metadata?: Record<string, any>;
}
