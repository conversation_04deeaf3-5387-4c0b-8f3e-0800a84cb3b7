import { AutoMap } from '@automapper/classes';
import { PaymentMethodType, PaymentProvider } from './payment.types';

export class PaymentMethod {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  customerId: string;

  @AutoMap()
  type: PaymentMethodType;

  @AutoMap()
  provider: PaymentProvider;

  @AutoMap()
  providerMethodId?: string;

  @AutoMap()
  isDefault: boolean;

  @AutoMap()
  isEnabled: boolean;

  @AutoMap()
  name: string;

  @AutoMap()
  displayName: string;

  @AutoMap()
  lastFour?: string;

  @AutoMap()
  expiryMonth?: number;

  @AutoMap()
  expiryYear?: number;

  @AutoMap()
  brand?: string;

  @AutoMap()
  country?: string;

  @AutoMap()
  metadata?: Record<string, any>;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy?: string;
}
