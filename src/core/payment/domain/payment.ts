import { AutoMap } from '@automapper/classes';
import {
  PaymentMethodType,
  PaymentProvider,
  PaymentStatus,
  PaymentTriggerType,
} from './payment.types';

export class Payment {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  amount: number;

  @AutoMap()
  currency: string;

  @AutoMap()
  status: PaymentStatus;

  @AutoMap()
  provider: PaymentProvider;

  @AutoMap()
  providerTransactionId?: string;

  @AutoMap()
  providerFee?: number;

  @AutoMap()
  paymentMethodId?: string;

  @AutoMap()
  paymentMethodType: PaymentMethodType;

  @AutoMap()
  paymentMethodDetails?: Record<string, any>;

  @AutoMap()
  triggerType: PaymentTriggerType;

  @AutoMap()
  description?: string;

  @AutoMap()
  metadata?: Record<string, any>;

  // Related entities
  @AutoMap()
  entityType: string; // 'order', 'invoice', etc.

  @AutoMap()
  entityId: string; // ID of the related entity

  // Customer information
  @AutoMap()
  customerId: string;

  @AutoMap()
  customerEmail?: string;

  @AutoMap()
  customerName?: string;

  // Timestamps
  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  completedAt?: Date;

  @AutoMap()
  failedAt?: Date;

  @AutoMap()
  cancelledAt?: Date;

  // User who initiated the payment
  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy?: string;

  // Error information
  @AutoMap()
  errorMessage?: string;

  @AutoMap()
  errorCode?: string;

  // Receipt information
  @AutoMap()
  receiptUrl?: string;

  @AutoMap()
  receiptNumber?: string;
}
