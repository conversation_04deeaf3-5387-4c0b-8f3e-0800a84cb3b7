import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  IsEnum,
  IsObject,
  IsBoolean,
  IsOptional,
} from 'class-validator';
import { PaymentMethodType, PaymentProvider } from '../domain/payment.types';

export class CreatePaymentMethodDto {
  @ApiProperty({
    description: 'Customer ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  customerId: string;

  @ApiProperty({
    description: 'Payment method type',
    enum: PaymentMethodType,
    example: PaymentMethodType.CreditCard,
  })
  @IsEnum(PaymentMethodType)
  type: PaymentMethodType;

  @ApiProperty({
    description: 'Payment provider',
    enum: PaymentProvider,
    example: PaymentProvider.Stripe,
  })
  @IsEnum(PaymentProvider)
  provider: PaymentProvider;

  @ApiProperty({
    description: 'Payment method name',
    example: 'My Visa Card',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Payment method data',
    example: {
      stripeCustomerId: 'cus_123456789',
      card: {
        number: '****************',
        exp_month: 12,
        exp_year: 2025,
        cvc: '123',
      },
    },
  })
  @IsObject()
  paymentMethodData: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Whether this payment method should be the default',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}
