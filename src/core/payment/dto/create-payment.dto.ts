import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsN<PERSON>ber,
  IsUUID,
  IsEmail,
  IsOptional,
  IsEnum,
  Min,
  IsObject,
} from 'class-validator';
import { PaymentTriggerType } from '../domain/payment.types';

export class CreatePaymentDto {
  @ApiProperty({
    description: 'Payment amount',
    example: 99.99,
    type: Number,
  })
  @IsNumber()
  @Min(0.01)
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
    type: String,
  })
  @IsString()
  currency: string;

  @ApiProperty({
    description: 'Entity type (e.g., "order")',
    example: 'order',
    type: String,
  })
  @IsString()
  entityType: string;

  @ApiProperty({
    description: 'Entity ID',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  @IsUUID()
  entityId: string;

  @ApiProperty({
    description: 'Customer ID',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  @IsUUID()
  customerId: string;

  @ApiProperty({
    description: 'Customer email',
    example: '<EMAIL>',
    type: String,
  })
  @IsEmail()
  customerEmail: string;

  @ApiProperty({
    description: 'Customer name',
    example: 'John Doe',
    type: String,
  })
  @IsString()
  customerName: string;

  @ApiPropertyOptional({
    description: 'Payment method ID',
    example: '550e8400-e29b-41d4-a716-************',
    type: String,
  })
  @IsOptional()
  @IsUUID()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Payment description',
    example: 'Payment for order #12345',
    type: String,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { orderNumber: '12345' },
    type: Object,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Payment trigger type',
    enum: PaymentTriggerType,
    default: PaymentTriggerType.Manual,
  })
  @IsOptional()
  @IsEnum(PaymentTriggerType)
  triggerType?: PaymentTriggerType;
}
