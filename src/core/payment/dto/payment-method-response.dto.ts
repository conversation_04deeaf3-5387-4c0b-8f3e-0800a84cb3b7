import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentMethodType, PaymentProvider } from '../domain/payment.types';

export class PaymentMethodResponseDto {
  @ApiProperty({
    description: 'Payment method ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tenant ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  tenantId: string;

  @ApiProperty({
    description: 'Customer ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  customerId: string;

  @ApiProperty({
    description: 'Payment method type',
    enum: PaymentMethodType,
    example: PaymentMethodType.CreditCard,
  })
  type: PaymentMethodType;

  @ApiProperty({
    description: 'Payment provider',
    enum: PaymentProvider,
    example: PaymentProvider.Stripe,
  })
  provider: PaymentProvider;

  @ApiPropertyOptional({
    description: 'Provider method ID',
    example: 'pm_1NqLOIKb9q6SZUla1EBnC7Xn',
  })
  providerMethodId?: string;

  @ApiProperty({
    description: 'Whether this is the default payment method',
    example: true,
  })
  isDefault: boolean;

  @ApiProperty({
    description: 'Whether this payment method is enabled',
    example: true,
  })
  isEnabled: boolean;

  @ApiProperty({
    description: 'Payment method name',
    example: 'My Visa Card',
  })
  name: string;

  @ApiProperty({
    description: 'Payment method display name',
    example: 'Visa ending in 4242',
  })
  displayName: string;

  @ApiPropertyOptional({
    description: 'Last four digits of the card',
    example: '4242',
  })
  lastFour?: string;

  @ApiPropertyOptional({
    description: 'Card expiry month',
    example: 12,
  })
  expiryMonth?: number;

  @ApiPropertyOptional({
    description: 'Card expiry year',
    example: 2025,
  })
  expiryYear?: number;

  @ApiPropertyOptional({
    description: 'Card brand',
    example: 'visa',
  })
  brand?: string;

  @ApiPropertyOptional({
    description: 'Card country',
    example: 'US',
  })
  country?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { stripeCustomerId: 'cus_123456789' },
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Created at timestamp',
    example: '2023-10-20T12:34:56.789Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated at timestamp',
    example: '2023-10-20T12:34:56.789Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Created by user ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Updated by user ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  updatedBy?: string;
}
