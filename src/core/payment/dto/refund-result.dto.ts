import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RefundResultDto {
  @ApiProperty({
    description: 'Whether the refund operation was successful',
    example: true,
  })
  success: boolean;

  @ApiPropertyOptional({
    description: 'Refund ID from the payment provider',
    example: 're_3NqLOIKb9q6SZUla1EBnC7Xn',
  })
  refundId?: string;

  @ApiProperty({
    description: 'Refund status',
    example: 'Completed',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Refunded amount',
    example: 99.99,
  })
  amount?: number;

  @ApiPropertyOptional({
    description: 'Message from the payment provider',
    example: 'Refund successful',
  })
  message?: string;

  @ApiPropertyOptional({
    description: 'Error code from the payment provider',
    example: 'refund_failed',
  })
  errorCode?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata from the payment provider',
    example: {
      stripeRefundId: 're_3NqLOIKb9q6SZUla1EBnC7Xn',
    },
  })
  metadata?: Record<string, any>;
}
