import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PaymentResultDto {
  @ApiProperty({
    description: 'Whether the payment operation was successful',
    example: true,
  })
  success: boolean;

  @ApiPropertyOptional({
    description: 'Payment ID from the payment provider',
    example: 'pi_3NqLOIKb9q6SZUla1EBnC7Xn',
  })
  paymentId?: string;

  @ApiProperty({
    description: 'Payment status',
    example: 'Completed',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Message from the payment provider',
    example: 'Payment successful',
  })
  message?: string;

  @ApiPropertyOptional({
    description: 'Error code from the payment provider',
    example: 'card_declined',
  })
  errorCode?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata from the payment provider',
    example: {
      clientSecret: 'pi_3NqLOIKb9q6SZUla1EBnC7Xn_secret_...',
      receiptUrl: 'https://pay.stripe.com/receipts/...',
    },
  })
  metadata?: Record<string, any>;
}
