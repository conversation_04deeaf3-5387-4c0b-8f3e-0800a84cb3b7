import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  PaymentMethodType,
  PaymentProvider,
  PaymentStatus,
  PaymentTriggerType,
} from '../domain/payment.types';

export class PaymentResponseDto {
  @ApiProperty({
    description: 'Payment ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tenant ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  tenantId: string;

  @ApiProperty({
    description: 'Payment amount',
    example: 99.99,
  })
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Payment status',
    enum: PaymentStatus,
    example: PaymentStatus.Pending,
  })
  status: PaymentStatus;

  @ApiProperty({
    description: 'Payment provider',
    enum: PaymentProvider,
    example: PaymentProvider.Stripe,
  })
  provider: PaymentProvider;

  @ApiPropertyOptional({
    description: 'Provider transaction ID',
    example: 'pi_3NqLOIKb9q6SZUla1EBnC7Xn',
  })
  providerTransactionId?: string;

  @ApiPropertyOptional({
    description: 'Provider fee',
    example: 2.99,
  })
  providerFee?: number;

  @ApiPropertyOptional({
    description: 'Payment method ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  paymentMethodId?: string;

  @ApiProperty({
    description: 'Payment method type',
    enum: PaymentMethodType,
    example: PaymentMethodType.CreditCard,
  })
  paymentMethodType: PaymentMethodType;

  @ApiPropertyOptional({
    description: 'Payment method details',
    example: { last4: '4242', brand: 'visa' },
  })
  paymentMethodDetails?: Record<string, any>;

  @ApiProperty({
    description: 'Payment trigger type',
    enum: PaymentTriggerType,
    example: PaymentTriggerType.Manual,
  })
  triggerType: PaymentTriggerType;

  @ApiPropertyOptional({
    description: 'Payment description',
    example: 'Payment for order #12345',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { orderNumber: '12345' },
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Entity type',
    example: 'order',
  })
  entityType: string;

  @ApiProperty({
    description: 'Entity ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  entityId: string;

  @ApiProperty({
    description: 'Customer ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  customerId: string;

  @ApiPropertyOptional({
    description: 'Customer email',
    example: '<EMAIL>',
  })
  customerEmail?: string;

  @ApiPropertyOptional({
    description: 'Customer name',
    example: 'John Doe',
  })
  customerName?: string;

  @ApiProperty({
    description: 'Created at timestamp',
    example: '2023-10-20T12:34:56.789Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated at timestamp',
    example: '2023-10-20T12:34:56.789Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Completed at timestamp',
    example: '2023-10-20T12:34:56.789Z',
  })
  completedAt?: Date;

  @ApiPropertyOptional({
    description: 'Failed at timestamp',
    example: '2023-10-20T12:34:56.789Z',
  })
  failedAt?: Date;

  @ApiPropertyOptional({
    description: 'Cancelled at timestamp',
    example: '2023-10-20T12:34:56.789Z',
  })
  cancelledAt?: Date;

  @ApiProperty({
    description: 'Created by user ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Updated by user ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  updatedBy?: string;

  @ApiPropertyOptional({
    description: 'Error message',
    example: 'Your card was declined',
  })
  errorMessage?: string;

  @ApiPropertyOptional({
    description: 'Error code',
    example: 'card_declined',
  })
  errorCode?: string;

  @ApiPropertyOptional({
    description: 'Receipt URL',
    example: 'https://pay.stripe.com/receipts/...',
  })
  receiptUrl?: string;

  @ApiPropertyOptional({
    description: 'Receipt number',
    example: '1234-5678',
  })
  receiptNumber?: string;
}
