export const AUTH_CONSTANTS = {
  JWT_SECRET: process.env.AUTH_JWT_SECRET || 'secret',
  SESSION_EXPIRATION: process.env.AUTH_SESSION_EXPIRES_IN || '24h', // 24 hours
  SESSION_EXPIRATION_MS: process.env.AUTH_SESSION_EXPIRES_IN_MS || 86400000, // 24 hours in ms (24 * 60 * 60 * 1000)

  ENCRYPTION_KEY:
    process.env.JWT_ENCRYPTION_KEY || 'a-strong-32-character-encryption-key!',
  COOKIE_OPTIONS: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    path: '/',
  },
} as const;
