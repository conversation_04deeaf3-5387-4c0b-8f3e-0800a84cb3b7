import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { TokenMissingException } from '@utils/errors/exceptions/auth.exceptions';
import { AUTH_CONSTANTS } from '../domain/auth.constants';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  private jwtService: JwtService;

  constructor() {
    this.jwtService = new JwtService({
      secret: AUTH_CONSTANTS.JWT_SECRET,
    });
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = request.cookies?.session_token;
    if (!token) {
      throw new TokenMissingException('access');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: AUTH_CONSTANTS.JWT_SECRET,
      });

      // Attach user data to request
      request.user = payload;
      return true;
    } catch {
      throw new UnauthorizedException('Invalid session token');
    }
  }
}
