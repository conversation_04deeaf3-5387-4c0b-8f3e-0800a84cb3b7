import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../auth.service';
import { AUTH_CONSTANTS } from '../domain/auth.constants';

@Injectable()
export class SessionExtenderMiddleware implements NestMiddleware {
  constructor(private readonly authService: AuthService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const sessionToken = req.cookies?.session_token;
    if (!sessionToken) return next();

    try {
      const newToken = await this.authService.verifyToken(sessionToken);
      if (newToken) {
        res.cookie('session_token', newToken, {
          ...AUTH_CONSTANTS.COOKIE_OPTIONS,
          maxAge: Number(AUTH_CONSTANTS.SESSION_EXPIRATION_MS),
        });
      }
    } catch {
      // Optionally log error details if needed
      // The auth guard will handle unauthorized access
    }

    return next();
  }
}
