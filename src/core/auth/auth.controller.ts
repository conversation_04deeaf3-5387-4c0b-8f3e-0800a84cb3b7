import {
  Controller,
  Post,
  Body,
  Res,
  HttpStatus,
  HttpCode,
  UseGuards,
  Get,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiCookieAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { CurrentUser } from './decorators/current-user.decorator';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { AUTH_CONSTANTS } from './domain/auth.constants';
import { TenantRegistrationDto } from '@core/auth/dto/tenant-registration.dto';
import {
  CheckCompanyIdDto,
  CompanyIdAvailabilityResponseDto,
} from '@core/auth/dto/check-company-id.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtPayload } from '@core/auth/domain/auth.types';

@ApiTags('Core - Authentication')
@ApiExtraModels(UserEntity)
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User login',
    description:
      'Authenticates user credentials and returns a session token in a cookie.',
  })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully logged in',
    schema: {
      properties: {
        user: {
          $ref: '#/components/schemas/UserEntity',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
  })
  async login(
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const { user, accessToken } = await this.authService.login(loginDto);
    this.setSessionCookie(response, accessToken);
    return { user };
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'User logout',
    description: 'Clears session cookie and invalidates current session.',
  })
  @ApiBearerAuth()
  @ApiCookieAuth('session_token')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully logged out',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or missing session token',
  })
  logout(
    @CurrentUser() user: UserEntity,
    @Res({ passthrough: true }) response: Response,
  ) {
    this.clearSessionCookie(response);
    return { message: 'Logged out successfully' };
  }

  private setSessionCookie(response: Response, token: string) {
    response.cookie('session_token', token, {
      ...AUTH_CONSTANTS.COOKIE_OPTIONS,
      maxAge: +AUTH_CONSTANTS.SESSION_EXPIRATION_MS,
    });
  }

  private clearSessionCookie(response: Response) {
    response.clearCookie('session_token', AUTH_CONSTANTS.COOKIE_OPTIONS);
  }

  @Post('register')
  @ApiOperation({
    summary: 'Register a new user.',
    description: 'Creates a new user in a single transaction',
  })
  @ApiBody({ type: TenantRegistrationDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User successfully registered',
  })
  async registerWithTenant(
    @Body() registrationDto: TenantRegistrationDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const result = await this.authService.registerWithTenant(registrationDto);
    this.setSessionCookie(response, result.accessToken);

    return {
      user: result.user,
      tenant: result.tenant,
    };
  }

  @Post('check-company-id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Check company ID availability',
    description:
      'Takes a company name, generates a company ID, and checks availability',
  })
  @ApiBody({ type: CheckCompanyIdDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Company ID availability check results',
    type: CompanyIdAvailabilityResponseDto,
  })
  checkCompanyId(
    @Body() checkCompanyIdDto: CheckCompanyIdDto,
  ): Promise<CompanyIdAvailabilityResponseDto> {
    return this.authService.checkCompanyIdAvailability(
      checkCompanyIdDto.companyName,
    );
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user information',
    description: 'Returns information about the currently logged-in user',
  })
  @ApiBearerAuth()
  @ApiCookieAuth('session_token')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current user information retrieved successfully',
    schema: {
      properties: {
        user: {
          $ref: '#/components/schemas/UserEntity',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or missing session token',
  })
  async me(@CurrentUser() user: JwtPayload) {
    const currentUser = await this.authService.getCurrentUser(user.sub);
    return { user: currentUser };
  }
}
