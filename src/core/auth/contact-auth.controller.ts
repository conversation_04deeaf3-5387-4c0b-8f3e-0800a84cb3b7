import {
  Controller,
  Post,
  Body,
  Res,
  HttpStatus,
  HttpCode,
  UseGuards,
  Get,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiCookieAuth,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { CurrentUser } from './decorators/current-user.decorator';
import { ContactEntity } from '@app/business/user/contacts/infrastructure/persistence/relational/entities/contact.entity';
import { AUTH_CONSTANTS } from './domain/auth.constants';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { ContactsService } from '@app/business/user/contacts/contacts.service';
import { JwtContactAuthGuard } from './guards/jwt-contact-auth.guard';

@ApiTags('Core - Contact Authentication')
@Controller('auth/contact')
export class ContactAuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly contactsService: ContactsService,
  ) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Contact login',
    description:
      'Authenticates contact credentials and returns a session token in a cookie.',
  })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully logged in',
    schema: {
      properties: {
        contact: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            phoneNumber: { type: 'string', nullable: true },
            phoneCountryCode: { type: 'string', nullable: true },
            permissions: { type: 'object' },
            isActive: { type: 'boolean' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
  })
  async login(
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const { contact, accessToken } =
      await this.authService.loginContact(loginDto);
    this.setSessionCookie(response, accessToken);
    return { contact };
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtContactAuthGuard)
  @ApiOperation({
    summary: 'Contact logout',
    description: 'Clears session cookie and invalidates current session.',
  })
  @ApiBearerAuth()
  @ApiCookieAuth('session_token')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully logged out',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or missing session token',
  })
  logout(
    @CurrentUser() contact: ContactEntity,
    @Res({ passthrough: true }) response: Response,
  ) {
    this.clearSessionCookie(response);
    return { message: 'Logged out successfully' };
  }

  @Get('me')
  @UseGuards(JwtContactAuthGuard)
  @ApiOperation({
    summary: 'Get current contact information',
    description: 'Returns information about the currently logged-in contact',
  })
  @ApiBearerAuth()
  @ApiCookieAuth('session_token')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current contact information retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or missing session token',
  })
  async me(@CurrentUser() contactData: JwtPayload) {
    const contact = await this.contactsService.findById(contactData.sub);
    return { contact };
  }
  private setSessionCookie(response: Response, token: string) {
    response.cookie('contact_session_token', token, {
      ...AUTH_CONSTANTS.COOKIE_OPTIONS,
      maxAge: +AUTH_CONSTANTS.SESSION_EXPIRATION_MS,
    });
  }

  private clearSessionCookie(response: Response) {
    response.clearCookie(
      'contact_session_token',
      AUTH_CONSTANTS.COOKIE_OPTIONS,
    );
  }
}
