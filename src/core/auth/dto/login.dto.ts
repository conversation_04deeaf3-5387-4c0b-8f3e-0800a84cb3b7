import { Is<PERSON><PERSON>, IsString, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password',
    minimum: 8,
  })
  @IsString()
  @MinLength(8)
  password: string;
}
