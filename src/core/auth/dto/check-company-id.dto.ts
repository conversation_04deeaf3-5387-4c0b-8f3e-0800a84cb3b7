import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class CheckCompanyIdDto {
  @ApiProperty({
    example: 'BlobStation',
    description: 'Company name to generate ID from',
  })
  @IsString()
  @IsNotEmpty()
  companyName: string;
}

export class CompanyIdAvailabilityResponseDto {
  @ApiProperty({
    example: 'blobstation',
    description: 'Suggested company unique ID based on the company name',
  })
  suggestedId: string;

  @ApiProperty({
    example: true,
    description: 'Whether the suggested ID is available',
  })
  isAvailable: boolean;

  @ApiProperty({
    example: ['blobstationlogistics', 'blobstation123', 'blobstation456'],
    description:
      'Alternative available IDs if the suggested one is already taken',
  })
  alternatives: string[];
}
