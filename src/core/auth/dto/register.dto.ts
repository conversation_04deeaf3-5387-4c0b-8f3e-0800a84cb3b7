import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Option<PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RegisterDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password',
    minimum: 8,
  })
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'Full name of the user',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    example: 'Acme Corp',
    description: 'Company name (optional)',
  })
  @IsString()
  @IsOptional()
  companyName?: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the tenant',
  })
  @IsUUID()
  tenantId: string;
}
