import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty } from 'class-validator';

export class TenantRegistrationDto {
  // User information
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password',
    minimum: 8,
  })
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'Full name of the user',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  // Tenant information
  @ApiProperty({
    example: 'BlobStation Logistics',
    description: 'Company name',
  })
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({
    example: 'blobstation',
    description: 'Company unique identifier (must be unique)',
  })
  @IsString()
  @IsNotEmpty()
  companyUniqueId: string;

  @ApiProperty({
    example: 'UTC',
    description: 'Company timezone',
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Company contact email',
  })
  @IsEmail()
  contactEmail: string;
}
