import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { UsersModule } from '@app/business/user/users/users.module';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';
import { CompanyIdService } from './services/company-id.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { SessionExtenderMiddleware } from './middleware/session-extender.middleware';
import { AUTH_CONSTANTS } from './domain/auth.constants';
import { ContactAuthController } from './contact-auth.controller';
import { ContactsModule } from '../../business/user/contacts/contacts.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity, TenantEntity]),
    JwtModule.register({
      secret: AUTH_CONSTANTS.JWT_SECRET,
      signOptions: { expiresIn: AUTH_CONSTANTS.SESSION_EXPIRATION },
    }),
    UsersModule,
    ContactsModule,
  ],
  controllers: [AuthController, ContactAuthController],
  providers: [AuthService, JwtStrategy, CompanyIdService, JwtAuthGuard],
  exports: [AuthService, JwtAuthGuard],
})
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(SessionExtenderMiddleware)
      .exclude('auth/login', 'auth/register', 'auth/logout')
      .forRoutes('*');
  }
}
