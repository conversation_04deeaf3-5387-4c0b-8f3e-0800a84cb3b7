import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';

@Injectable()
export class CompanyIdService {
  private readonly logger = new Logger(CompanyIdService.name);
  private readonly INDUSTRY_SUFFIXES = ['logistics', 'transport', 'delivery'];

  constructor(
    @InjectRepository(TenantEntity)
    private readonly tenantRepository: Repository<TenantEntity>,
  ) {}

  /**
   * Generate a base company unique ID from a company name
   */
  private generateBaseId(companyName: string): string {
    // Convert to lowercase, remove spaces and special chars
    let baseId = companyName
      .toLowerCase()
      .replace(/\s+/g, '')
      .replace(/[^a-z0-9]/g, '')
      .trim();

    // Handle edge cases
    if (baseId.length < 3) {
      baseId = `company${Math.floor(1000 + Math.random() * 9000)}`;
    }

    // Truncate if too long
    if (baseId.length > 20) {
      baseId = baseId.substring(0, 20);
    }

    return baseId;
  }

  /**
   * Generate a collection of alternative unique IDs
   */
  private generateAlternatives(baseId: string): string[] {
    const alternatives = new Set<string>();

    // Add industry-specific suffixes
    for (const suffix of this.INDUSTRY_SUFFIXES) {
      alternatives.add(`${baseId}_${suffix}`);
    }

    // Add numeric suffixes
    for (let i = 1; i <= 3; i++) {
      alternatives.add(`${baseId}_${Math.floor(100 + Math.random() * 900)}`);
    }

    return Array.from(alternatives).slice(0, 5);
  }

  /**
   * Check if a company ID is available and suggest alternatives
   */
  async checkCompanyIdAvailability(companyName: string): Promise<{
    suggestedId: string;
    isAvailable: boolean;
    alternatives: string[];
  }> {
    try {
      // Generate base ID from company name
      const baseId = this.generateBaseId(companyName);

      // Check if base ID is available
      const exists = await this.tenantRepository.findOne({
        where: { companyUniqueId: baseId },
      });

      // Generate alternatives
      const alternatives = this.generateAlternatives(baseId);

      // Find available alternatives
      const availableAlternatives: string[] = [];
      for (const altId of alternatives) {
        const altExists = await this.tenantRepository.findOne({
          where: { companyUniqueId: altId },
        });
        if (!altExists) {
          availableAlternatives.push(altId);
        }
      }

      return {
        suggestedId: baseId,
        isAvailable: !exists,
        alternatives: availableAlternatives,
      };
    } catch (error) {
      this.logger.error(
        `Error checking company ID availability: ${error.message}`,
        error.stack,
      );
      // Return safe fallback
      return {
        suggestedId: this.generateBaseId(companyName),
        isAvailable: false,
        alternatives: [],
      };
    }
  }
}
