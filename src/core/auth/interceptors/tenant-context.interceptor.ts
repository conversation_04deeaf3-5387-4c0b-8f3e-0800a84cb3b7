import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHand<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { CryptoUtils } from '@utils/crypto.utils';
import { AUTH_CONSTANTS } from '@core/auth/domain/auth.constants';

export interface RequestWithUser extends Request {
  user?: any;
  tenantContext?: {
    tenantId: string | null;
    hasTenantAccess: boolean;
    userType?: string;
  };
}

@Injectable()
export class TenantContextInterceptor implements NestInterceptor {
  private readonly cryptoUtils: CryptoUtils;

  constructor() {
    this.cryptoUtils = new CryptoUtils(AUTH_CONSTANTS.ENCRYPTION_KEY);
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<RequestWithUser>();

    request.tenantContext = {
      tenantId: null,
      hasTenantAccess: false,
    };

    if (request.user) {
      // Handle encrypted context if present
      if (request.user.ctx) {
        try {
          // Set tenant context from decrypted data
          request.tenantContext = {
            tenantId: request.user.ctx.tenantId,
            hasTenantAccess: !!request.user.ctx.tenantId,
            userType: request.user.ctx.userType,
          };
        } catch (error) {
          console.error('Failed to decrypt context:', error);
        }
      }
      // Fallback to existing approach if no encrypted context
      else if (request.user.tenantId) {
        request.tenantContext = {
          tenantId: request.user.tenantId,
          hasTenantAccess: true,
          userType: request.user.userType,
        };
      }
    }

    return next.handle();
  }
}
