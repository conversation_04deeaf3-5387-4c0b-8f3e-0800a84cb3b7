import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../auth/interceptors/tenant-context.interceptor';
import { TenantValidationService } from '../../business/user/tenants/tenant-validation.service';
import { RbacService } from './rbac.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { RoleResponseDto } from './dto/get-role.dto';
import { BasicRoleResponseDto } from './dto/get-all-role.dto';
import { AssignRoleDto } from './dto/assign-role.dto';
import { RoleAssignmentResponseDto } from './dto/get-role-assignment.dto';
import { CheckPermissionDto } from './dto/check-permission.dto';
import { CheckPermissionResponseDto } from './dto/check-permission-response.dto';

@ApiTags('Core - RBAC')
@ApiBearerAuth()
@Controller({
  path: 'rbac',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class RbacController {
  constructor(
    private readonly rbacService: RbacService,
    private readonly tenantValidationService: TenantValidationService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new role with permissions' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createRoleDto: CreateRoleDto,
  ) {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);

      const response = await this.rbacService.create(tenantId, createRoleDto);
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all roles' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: [BasicRoleResponseDto] })
  async findAll(
    @Req() request: RequestWithUser,
  ): Promise<BasicRoleResponseDto[]> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);
    const roles = await this.rbacService.findAll(tenantId);
    return roles;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get role by id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: RoleResponseDto })
  async findOne(@Param('id') id: string): Promise<RoleResponseDto> {
    const role = await this.rbacService.findOne(id);
    return role;
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update role' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({
    description: 'No Content',
  })
  async update(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() updateRoleDto: CreateRoleDto,
  ): Promise<void> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);
    await this.rbacService.update(id, tenantId, updateRoleDto);
    return;
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete role by id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async delete(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
  ): Promise<void> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);
    await this.rbacService.delete(id, tenantId);
    return;
  }

  @Post('assignments')
  @ApiOperation({ summary: 'Assign role to user' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async assignRole(
    @Req() request: RequestWithUser,
    @Body() assignRoleDto: AssignRoleDto,
  ): Promise<void> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);
    await this.rbacService.assignRole(tenantId, assignRoleDto);
    return;
  }

  @Get('users/:userId/roles')
  @ApiOperation({ summary: 'Get roles assigned to user' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: RoleAssignmentResponseDto })
  async getUserRole(
    @Req() request: RequestWithUser,
    @Param('userId') userId: string,
  ): Promise<RoleAssignmentResponseDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);
    const response = await this.rbacService.getUserRole(tenantId, userId);
    return response;
  }

  @Post('check-permission')
  @ApiOperation({ summary: 'Check if user has specific permission' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: CheckPermissionResponseDto })
  async checkPermission(
    @Req() request: RequestWithUser,
    @Body() checkPermissionDto: CheckPermissionDto,
  ): Promise<CheckPermissionResponseDto> {
    const { id: tenantId } =
      await this.tenantValidationService.validateTenantAccess(request);
    const hasPermission = await this.rbacService.hasPermission(
      tenantId,
      checkPermissionDto,
    );
    return { hasPermission };
  }
}
