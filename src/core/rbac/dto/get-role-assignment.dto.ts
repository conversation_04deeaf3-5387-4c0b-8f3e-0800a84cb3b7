import { ApiProperty } from '@nestjs/swagger';
import { PermissionGroupResponseDto } from './get-role.dto';

export class RoleAssignmentResponseDto {
  @ApiProperty({ example: 'd82e8b71-17e5-4f9d-b9f8-94557f8e16cb' })
  id: string;

  @ApiProperty({ example: 'd82e8b71-17e5-4f9d-b9f8-94557f8e16cb' })
  roleId: string;

  @ApiProperty({ type: () => [PermissionGroupResponseDto] })
  permissionGroups: PermissionGroupResponseDto[];
}
