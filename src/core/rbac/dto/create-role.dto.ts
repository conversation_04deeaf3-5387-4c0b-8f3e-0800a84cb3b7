import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class CreatePermissionDto {
  @ApiProperty({ example: 'create_customer' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  isPermissionAllowed: boolean;
}

export class CreatePermissionGroupDto {
  @ApiProperty({ example: 'customer_management' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @ApiProperty({ type: () => [CreatePermissionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePermissionDto)
  permissions: CreatePermissionDto[];

  @ApiProperty({ type: () => [CreatePermissionGroupDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreatePermissionGroupDto)
  subGroups?: CreatePermissionGroupDto[];
}

export class CreateRoleDto {
  @ApiProperty({ example: 'admin' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isSystemRole: boolean;

  @ApiProperty({ type: () => [CreatePermissionGroupDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePermissionGroupDto)
  permissionGroups: CreatePermissionGroupDto[];
}
