import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON>y, IsNotEmpty, IsUUID, ValidateNested } from 'class-validator';
import { CreatePermissionGroupDto } from './create-role.dto';
import { Type } from 'class-transformer';

export class AssignRoleDto {
  @ApiProperty({ example: 'd82e8b71-17e5-4f9d-b9f8-94557f8e16cb' })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ example: 'd82e8b71-17e5-4f9d-b9f8-94557f8e16cb' })
  @IsUUID()
  @IsNotEmpty()
  roleId: string;

  @ApiProperty({ type: () => [CreatePermissionGroupDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePermissionGroupDto)
  permissionGroups: CreatePermissionGroupDto[];
}
