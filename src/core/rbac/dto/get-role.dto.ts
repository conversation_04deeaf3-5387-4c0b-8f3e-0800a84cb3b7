import { ApiProperty } from '@nestjs/swagger';

export class PermissionResponseDto {
  @ApiProperty({ example: 'd82e8b71-17e5-4f9d-b9f8-94557f8e16cb' })
  id: string;

  @ApiProperty({ example: 'create_customer' })
  name: string;

  @ApiProperty()
  isPermissionAllowed: boolean;
}

export class PermissionGroupResponseDto {
  @ApiProperty({ example: 'd82e8b71-17e5-4f9d-b9f8-94557f8e16cb' })
  id: string;

  @ApiProperty({ example: 'customer_management' })
  name: string;

  @ApiProperty({ type: () => [PermissionResponseDto] })
  permissions: PermissionResponseDto[];

  @ApiProperty({ type: () => [PermissionGroupResponseDto] })
  subGroups?: PermissionGroupResponseDto[];
}

export class RoleResponseDto {
  @ApiProperty({ example: 'd82e8b71-17e5-4f9d-b9f8-94557f8e16cb' })
  id: string;

  @ApiProperty({ example: 'admin' })
  name: string;

  @ApiProperty()
  isSystemRole: boolean;

  @ApiProperty({ type: () => [PermissionGroupResponseDto] })
  permissionGroups: PermissionGroupResponseDto[];
}
