import { Module } from '@nestjs/common';
import { MonitoringModule } from '@core/infrastructure/monitoring/monitoring.module';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { RbacController } from './rbac.controller';
import { RbacService } from './rbac.service';
import { RelationalRbacPersistenceModule } from './infrastructure/relational-persistence.module';
import { TenantsModule } from '../../business/user/tenants/tenants.module';

@Module({
  imports: [
    RelationalRbacPersistenceModule,
    MonitoringModule,
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    TenantsModule,
  ],
  controllers: [RbacController],
  providers: [RbacService],
  exports: [RbacService],
})
export class RbacModule {}
