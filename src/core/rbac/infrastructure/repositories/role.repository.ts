import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RoleEntity } from '../entities/role.entity';
import {
  CreateRoleDto,
  CreatePermissionGroupDto,
} from '../../dto/create-role.dto';
import { PermissionEntity } from '../entities/permission.entity';
import { PermissionGroupEntity } from '../entities/permission-group.entity';
import {
  PermissionGroupResponseDto,
  RoleResponseDto,
} from '../../dto/get-role.dto';
import {
  RoleAssignmentNotFoundException,
  RoleNotFoundException,
} from '../../../../utils/errors/exceptions/rbac-exceptions';
import { BasicRoleResponseDto } from '../../dto/get-all-role.dto';
import { RoleAssignmentEntity } from '../entities/role-assignment.entity';
import { AssignRoleDto } from '../../dto/assign-role.dto';
import { RoleAssignmentResponseDto } from '../../dto/get-role-assignment.dto';

@Injectable()
export class RoleRepository {
  constructor(
    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,
    @InjectRepository(PermissionGroupEntity)
    private readonly permissionGroupRepository: Repository<PermissionGroupEntity>,
    @InjectRepository(PermissionEntity)
    private readonly permissionRepository: Repository<PermissionEntity>,
    @InjectRepository(RoleAssignmentEntity)
    private readonly roleAssignmentRepository: Repository<RoleAssignmentEntity>,
  ) {}

  async create(tenantId: string, data: CreateRoleDto): Promise<RoleEntity> {
    const role = await this.roleRepository.save(
      this.roleRepository.create({
        name: data.name,
        tenantId,
        isSystemRole: data.isSystemRole,
      }),
    );

    await this.createPermissionGroupsRecursively(
      data.permissionGroups,
      null,
      null,
      role.id,
      tenantId,
    );

    return role;
  }

  async findAll(tenantId: string): Promise<BasicRoleResponseDto[]> {
    const roles = await this.roleRepository.find({
      where: { tenantId },
    });

    const response = roles.map((role) => ({
      id: role.id,
      name: role.name,
      isSystemRole: role.isSystemRole,
    }));
    return response;
  }

  async findOne(id: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findOne({ where: { id } });
    if (!role) {
      throw new RoleNotFoundException(id);
    }

    const allPermissionGroups = await this.permissionGroupRepository.find({
      where: { roleId: role.id },
    });

    const topLevelGroups = allPermissionGroups.filter(
      (group) => !group.parentId,
    );

    const permissionGroups = await this.buildNestedGroups(
      topLevelGroups,
      allPermissionGroups,
    );

    const newRole = {
      id: role.id,
      name: role.name,
      isSystemRole: role.isSystemRole,
      permissionGroups,
    };

    return newRole;
  }

  async update(
    id: string,
    tenantId: string,
    data: CreateRoleDto,
  ): Promise<RoleEntity> {
    const role = await this.roleRepository.findOne({ where: { id, tenantId } });

    if (!role) {
      throw new RoleNotFoundException(id);
    }

    if (data.name) {
      role.name = data.name;
    }

    if (data.isSystemRole !== undefined) {
      role.isSystemRole = data.isSystemRole;
    }

    await this.roleRepository.save(role);

    if (data.permissionGroups) {
      const existingGroups = await this.permissionGroupRepository.find({
        where: { roleId: id },
      });

      for (const group of existingGroups) {
        await this.permissionRepository.delete({ permissionGroupId: group.id });
      }

      await this.permissionGroupRepository.delete({ roleId: id });

      await this.createPermissionGroupsRecursively(
        data.permissionGroups,
        null,
        null,
        id,
        tenantId,
      );
    }

    return role;
  }

  async delete(id: string, tenantId: string): Promise<void> {
    const role = await this.roleRepository.findOne({ where: { id, tenantId } });

    if (!role) {
      throw new RoleNotFoundException(id);
    }

    const permissionGroups = await this.permissionGroupRepository.find({
      where: { roleId: id },
    });

    for (const group of permissionGroups) {
      await this.permissionRepository.delete({ permissionGroupId: group.id });
    }

    await this.permissionGroupRepository.delete({ roleId: id });

    await this.roleRepository.delete(id);

    return;
  }

  async assignRole(
    tenantId: string,
    assignRoleDto: AssignRoleDto,
  ): Promise<void> {
    const role = await this.roleRepository.findOne({
      where: { id: assignRoleDto.roleId, tenantId },
    });

    if (!role) {
      throw new RoleNotFoundException(assignRoleDto.roleId);
    }

    const existingAssignment = await this.roleAssignmentRepository.findOne({
      where: { userId: assignRoleDto.userId, tenantId },
    });

    if (existingAssignment) {
      existingAssignment.roleId = assignRoleDto.roleId;
      await this.roleAssignmentRepository.save(existingAssignment);

      await this.permissionGroupRepository.delete({
        userId: assignRoleDto.userId,
      });

      await this.permissionRepository.delete({ userId: assignRoleDto.userId });
    } else {
      await this.roleAssignmentRepository.save(
        this.roleAssignmentRepository.create({
          userId: assignRoleDto.userId,
          roleId: assignRoleDto.roleId,
          tenantId,
        }),
      );
    }

    if (
      assignRoleDto.permissionGroups &&
      assignRoleDto.permissionGroups.length > 0
    ) {
      await this.createPermissionGroupsRecursively(
        assignRoleDto.permissionGroups,
        null,
        assignRoleDto.userId,
        assignRoleDto.roleId,
        tenantId,
      );
    }
  }

  async getUserRole(
    tenantId: string,
    userId: string,
  ): Promise<RoleAssignmentResponseDto> {
    const assignment = await this.roleAssignmentRepository.findOne({
      where: { userId, tenantId },
    });

    if (!assignment) {
      throw new RoleAssignmentNotFoundException(userId);
    }

    const allPermissionGroups = await this.permissionGroupRepository.find({
      where: { roleId: assignment.roleId, userId },
    });

    const topLevelGroups = allPermissionGroups.filter(
      (group) => !group.parentId,
    );

    const permissionGroups = await this.buildNestedGroups(
      topLevelGroups,
      allPermissionGroups,
    );

    const response = {
      id: assignment.id,
      roleId: assignment.roleId,
      permissionGroups,
    };

    return response;
  }

  private async createPermissionGroupsRecursively(
    permissionGroups: CreatePermissionGroupDto[],
    parentId: string | null,
    userId: string | null,
    roleId: string,
    tenantId: string,
  ) {
    for (const permissionGroup of permissionGroups) {
      const savedGroup = await this.permissionGroupRepository.save(
        this.permissionGroupRepository.create({
          name: permissionGroup.name,
          tenantId,
          roleId,
          userId,
          parentId,
        }),
      );

      for (const permission of permissionGroup.permissions) {
        await this.permissionRepository.save(
          this.permissionRepository.create({
            name: permission.name,
            isPermissionAllowed: permission.isPermissionAllowed,
            permissionGroupId: savedGroup.id,
            userId,
            tenantId,
          }),
        );
      }

      if (permissionGroup.subGroups && permissionGroup.subGroups.length > 0) {
        await this.createPermissionGroupsRecursively(
          permissionGroup.subGroups,
          savedGroup.id,
          userId,
          roleId,
          tenantId,
        );
      }
    }
  }

  private async buildNestedGroups(
    parentGroups: PermissionGroupEntity[],
    allPermissionGroups: PermissionGroupEntity[],
  ): Promise<PermissionGroupResponseDto[]> {
    const result: PermissionGroupResponseDto[] = [];

    for (const group of parentGroups) {
      const permissions = await this.permissionRepository.find({
        where: { permissionGroupId: group.id },
      });

      const childGroups = allPermissionGroups.filter(
        (g) => g.parentId === group.id,
      );

      const groupStructure: PermissionGroupResponseDto = {
        id: group.id,
        name: group.name,
        permissions: permissions.map((p) => ({
          id: p.id,
          name: p.name,
          isPermissionAllowed: p.isPermissionAllowed,
        })),
      };

      if (childGroups.length > 0) {
        groupStructure.subGroups = await this.buildNestedGroups(
          childGroups,
          allPermissionGroups,
        );
      }

      result.push(groupStructure);
    }

    return result;
  }
}
