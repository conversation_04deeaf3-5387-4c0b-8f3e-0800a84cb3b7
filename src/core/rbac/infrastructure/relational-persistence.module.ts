import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionGroupEntity } from './entities/permission-group.entity';
import { RoleEntity } from './entities/role.entity';
import { RoleAssignmentEntity } from './entities/role-assignment.entity';
import { RoleRepository } from './repositories/role.repository';
import { PermissionEntity } from './entities/permission.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PermissionEntity,
      PermissionGroupEntity,
      RoleEntity,
      RoleAssignmentEntity,
    ]),
  ],
  providers: [RoleRepository],
  exports: [RoleRepository],
})
export class RelationalRbacPersistenceModule {}
