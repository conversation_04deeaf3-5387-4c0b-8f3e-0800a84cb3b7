import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RbacService } from '../rbac.service';
import { PermissionDeniedException } from '../../../utils/errors/exceptions/rbac-exceptions';

/**
 * Decorator to specify required permission
 */
export const RequirePermission = (permission: string) => {
  return (target: any, key?: string, descriptor?: any) => {
    Reflect.defineMetadata('permission', permission, descriptor.value);
    return descriptor;
  };
};

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private rbacService: RbacService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    return true;
    const permission = this.reflector.get<string>(
      'permission',
      context.getHandler(),
    );

    if (!permission) {
      return true;
    }

    const request = context.switchToHttp().getRequest();

    if (!request.user) {
      throw new PermissionDeniedException(permission);
    }

    try {
      const hasPermission = await this.rbacService.hasPermission(
        request.user.ctx.tenantId,
        {
          userId: request.user.sub,
          permissionName: permission,
        },
      );

      if (!hasPermission) {
        throw new PermissionDeniedException(permission);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }
}
