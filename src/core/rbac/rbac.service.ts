import { Injectable } from '@nestjs/common';
import { RoleRepository } from './infrastructure/repositories/role.repository';
import { CreateRoleDto } from './dto/create-role.dto';
import { BasicRoleResponseDto } from './dto/get-all-role.dto';
import {
  PermissionGroupResponseDto,
  RoleResponseDto,
} from './dto/get-role.dto';
import { RoleEntity } from './infrastructure/entities/role.entity';
import { AssignRoleDto } from './dto/assign-role.dto';
import { RoleAssignmentResponseDto } from './dto/get-role-assignment.dto';
import { CheckPermissionDto } from './dto/check-permission.dto';

@Injectable()
export class RbacService {
  constructor(private readonly roleRepository: RoleRepository) {}

  async create(
    tenantId: string,
    createRoleDto: CreateRoleDto,
  ): Promise<RoleEntity> {
    const role = await this.roleRepository.create(tenantId, createRoleDto);
    return role;
  }

  async findAll(tenantId: string): Promise<BasicRoleResponseDto[]> {
    const roles = await this.roleRepository.findAll(tenantId);
    return roles;
  }

  async findOne(id: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findOne(id);
    return role;
  }

  async update(
    id: string,
    tenantId: string,
    updateRoleDto: CreateRoleDto,
  ): Promise<RoleEntity> {
    const role = await this.roleRepository.update(id, tenantId, updateRoleDto);
    return role;
  }

  async delete(id: string, tenantId: string): Promise<void> {
    await this.roleRepository.delete(id, tenantId);
    return;
  }

  async assignRole(
    tenantId: string,
    assignRoleDto: AssignRoleDto,
  ): Promise<void> {
    await this.roleRepository.assignRole(tenantId, assignRoleDto);
    return;
  }

  async getUserRole(
    tenantId: string,
    userId: string,
  ): Promise<RoleAssignmentResponseDto> {
    const response = await this.roleRepository.getUserRole(tenantId, userId);
    return response;
  }

  async hasPermission(
    tenantId: string,
    checkPermissionDto: CheckPermissionDto,
  ): Promise<boolean> {
    const role = await this.roleRepository.getUserRole(
      tenantId,
      checkPermissionDto.userId,
    );

    const checkPermissionInGroups = (
      groups: PermissionGroupResponseDto[],
    ): boolean => {
      for (const group of groups) {
        const foundPermission = group.permissions.find(
          (p) => p.name === checkPermissionDto.permissionName,
        );
        if (foundPermission && foundPermission.isPermissionAllowed) {
          return true;
        }

        if (group.subGroups && group.subGroups.length > 0) {
          const hasPermInSubgroups = checkPermissionInGroups(group.subGroups);
          if (hasPermInSubgroups) {
            return true;
          }
        }
      }
      return false;
    };

    const hasPermission = checkPermissionInGroups(role.permissionGroups);
    if (hasPermission) {
      return true;
    }

    return false;
  }
}
