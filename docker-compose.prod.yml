services:
  api:
    container_name: api
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "5000:5000"
    env_file:
      - .env
    networks:
      - proxymanager
    restart: unless-stopped

  postgres:
    container_name: postgres
    image: postgres:17.0-alpine
    ports:
      - "5432:5432"
    volumes:
      - ./db:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    networks:
      - proxymanager

networks:
  proxymanager:
    external: true
