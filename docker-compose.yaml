services:
  postgres:
    image: postgres:17.0-alpine
    ports:
      - 5432:5432
    volumes:
      - ./db:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    networks:
      - app-network

  maildev:
    image: maildev/maildev
    ports:
      - ${MAIL_CLIENT_PORT}:1080
      - ${MAIL_PORT}:1025

  jaeger:
    image: jaegertracing/all-in-one:latest
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      #      - "6831:6831/udp"
      #      - "6832:6832/udp"
      #      - "5778:5778"
      #      - "16686:16686"
      #      - "4317:4317"
      - "4318:4318"
    #      - "14250:14250"
    #      - "14268:14268"
    #      - "9411:9411"
    networks:
      - jaeger-net
      - app-network

volumes:
  db:


networks:
  jaeger-net:
    driver: bridge
  app-network:
    driver: bridge